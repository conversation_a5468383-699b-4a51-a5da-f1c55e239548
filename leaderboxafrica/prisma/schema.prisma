// LeaderBox Prisma Schema
// This schema defines the complete database structure for the LeaderBox application
// Based on the existing Supabase schema and localStorage data structures

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Core user management
model User {
  id                   String   @id @default(uuid())
  email                String   @unique
  name                 String?
  phone                String?
  state                String?
  lga                  String?
  gender               String?
  party                String?
  role                 String   @default("User")
  status               String   @default("active")
  avatarUrl            String?  @map("avatar_url")
  isAd<PERSON>  @default(false) @map("is_admin")
  onboardingComplete   Boolean  @default(false) @map("onboarding_complete")
  petitionsCreated     Int      @default(0) @map("petitions_created")
  profileLikesCount    Int      @default(0) @map("profile_likes_count")
  groupMembersCount    Int      @default(0) @map("group_members_count")
  passwordHash         String?  @map("password_hash") // For Lucia Auth
  resetToken           String?  @map("reset_token") // For password reset
  resetTokenExpiresAt  DateTime? @map("reset_token_expires_at") // Reset token expiry
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  sessions             Session[] // For Lucia Auth
  leaderFollows        UserLeaderFollow[]
  leaderRatings        UserLeaderRating[]
  leaderComments       LeaderComment[]
  commentVotes         CommentVote[]
  polls                Poll[]
  pollVotes            PollVote[]
  pollComments         PollComment[]
  banters              Banter[]
  banterVotes          BanterVote[]
  banterComments       BanterComment[]
  petitions            Petition[]
  petitionSignatures  PetitionSignature[]
  groups               Group[]
  groupMemberships     GroupMember[]
  groupDiscussions     GroupDiscussion[]
  profileLikesGiven    UserProfileLike[] @relation("ProfileLiker")
  profileLikesReceived UserProfileLike[] @relation("ProfileLiked")

  @@map("users")
}

// Session management for custom auth
model Session {
  id         String   @id
  userId     String   @map("user_id")
  secretHash Bytes    @map("secret_hash")
  expiresAt  DateTime @map("expires_at")
  createdAt  DateTime @default(now()) @map("created_at")

  user User @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@map("sessions")
}

// Political leaders
model Leader {
  id               String   @id @default(uuid())
  name             String
  position         String?
  party            String?
  state            String?
  lga              String?
  bio              String?
  detailedBio      String?  @map("detailed_bio")
  avatarUrl        String?  @map("avatar_url")
  coverImageUrl    String?  @map("cover_image_url")
  followersCount   Int      @default(0) @map("followers_count")
  ratingAverage    Decimal  @default(0) @map("rating_average") @db.Decimal(3, 2)
  ratingCount      Int      @default(0) @map("rating_count")
  publicSentiment  String?  @map("public_sentiment") // 'Positive', 'Neutral', 'Negative'
  youtubeVideoUrl  String?  @map("youtube_video_url")
  education        String?
  background       String?
  popularityScore  Int      @default(0) @map("popularity_score")
  isVerified       Boolean  @default(false) @map("is_verified")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  followers        UserLeaderFollow[]
  ratings          UserLeaderRating[]
  comments         LeaderComment[]

  @@map("leaders")
}

// User-Leader relationships
model UserLeaderFollow {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  leaderId  String   @map("leader_id")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  leader Leader @relation(fields: [leaderId], references: [id], onDelete: Cascade)

  @@unique([userId, leaderId])
  @@map("user_leader_follows")
}

model UserLeaderRating {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  leaderId  String   @map("leader_id")
  rating    Int      @db.SmallInt
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  leader Leader @relation(fields: [leaderId], references: [id], onDelete: Cascade)

  @@unique([userId, leaderId])
  @@map("user_leader_ratings")
}

// Comments system for leaders
model LeaderComment {
  id           String   @id @default(uuid())
  leaderId     String   @map("leader_id")
  userId       String   @map("user_id")
  text         String
  ratingGiven  Int?     @map("rating_given") @db.SmallInt
  upvotes      Int      @default(0)
  downvotes    Int      @default(0)
  media        Json     @default("[]")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  leader Leader        @relation(fields: [leaderId], references: [id], onDelete: Cascade)
  user   User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  votes  CommentVote[]

  @@map("leader_comments")
}

model CommentVote {
  id          String   @id @default(uuid())
  userId      String   @map("user_id")
  commentId   String   @map("comment_id")
  commentType String   @default("leader_comment") @map("comment_type")
  voteType    String   @map("vote_type") // 'upvote' or 'downvote'
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  comment LeaderComment @relation(fields: [commentId], references: [id], onDelete: Cascade)

  @@unique([userId, commentId, commentType])
  @@map("comment_votes")
}

// Polls system
model Poll {
  id            String   @id @default(uuid())
  topic         String
  description   String?
  creatorId     String?  @map("creator_id")
  totalVotes    Int      @default(0) @map("total_votes")
  upvotes       Int      @default(0)
  downvotes     Int      @default(0)
  commentsCount Int      @default(0) @map("comments_count")
  media         Json     @default("[]")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  creator  User?         @relation(fields: [creatorId], references: [id], onDelete: SetNull)
  options  PollOption[]
  votes    PollVote[]
  comments PollComment[]

  @@map("polls")
}

model PollOption {
  id          String   @id @default(uuid())
  pollId      String   @map("poll_id")
  text        String
  votes       Int      @default(0)
  optionOrder Int      @map("option_order")
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  poll      Poll       @relation(fields: [pollId], references: [id], onDelete: Cascade)
  pollVotes PollVote[]

  @@map("poll_options")
}

model PollVote {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  pollId    String   @map("poll_id")
  optionId  String   @map("option_id")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user   User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  poll   Poll       @relation(fields: [pollId], references: [id], onDelete: Cascade)
  option PollOption @relation(fields: [optionId], references: [id], onDelete: Cascade)

  @@unique([userId, pollId])
  @@map("poll_votes")
}

model PollComment {
  id        String   @id @default(uuid())
  pollId    String   @map("poll_id")
  userId    String   @map("user_id")
  text      String
  upvotes   Int      @default(0)
  downvotes Int      @default(0)
  media     Json     @default("[]")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  poll Poll @relation(fields: [pollId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("poll_comments")
}

// Banter/Social Posts
model Banter {
  id            String   @id @default(uuid())
  authorId      String   @map("author_id")
  content       String
  upvotes       Int      @default(0)
  downvotes     Int      @default(0)
  commentsCount Int      @default(0) @map("comments_count")
  media         Json     @default("[]")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  author   User            @relation(fields: [authorId], references: [id], onDelete: Cascade)
  votes    BanterVote[]
  comments BanterComment[]

  @@map("banters")
}

model BanterVote {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  banterId  String   @map("banter_id")
  voteType  String   @map("vote_type") // 'upvote' or 'downvote'
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  banter Banter @relation(fields: [banterId], references: [id], onDelete: Cascade)

  @@unique([userId, banterId])
  @@map("banter_votes")
}

model BanterComment {
  id        String   @id @default(uuid())
  banterId  String   @map("banter_id")
  userId    String   @map("user_id")
  text      String
  upvotes   Int      @default(0)
  downvotes Int      @default(0)
  media     Json     @default("[]")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  banter Banter @relation(fields: [banterId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("banter_comments")
}

// Petitions
model Petition {
  id          String   @id @default(uuid())
  title       String
  description String
  creatorId   String?  @map("creator_id")
  signatures  Int      @default(0)
  status      String   @default("pending")
  media       Json     @default("[]")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  creator            User?                @relation(fields: [creatorId], references: [id], onDelete: SetNull)
  petitionSignatures PetitionSignature[]

  @@map("petitions")
}

model PetitionSignature {
  id          String   @id @default(uuid())
  petitionId  String   @map("petition_id")
  userId      String   @map("user_id")
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  petition Petition @relation(fields: [petitionId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, petitionId])
  @@map("petition_signatures")
}

// Groups
model Group {
  id             String   @id @default(uuid())
  name           String
  description    String?
  coverImageUrl  String?  @map("cover_image_url")
  creatorId      String?  @map("creator_id")
  isPublic       Boolean  @default(true) @map("is_public")
  status         String   @default("visible")
  tags           Json     @default("{}")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  creator     User?             @relation(fields: [creatorId], references: [id], onDelete: SetNull)
  members     GroupMember[]
  discussions GroupDiscussion[]

  @@map("groups")
}

model GroupMember {
  id       String   @id @default(uuid())
  groupId  String   @map("group_id")
  userId   String   @map("user_id")
  role     String   @default("member")
  joinedAt DateTime @default(now()) @map("joined_at")

  // Relations
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([groupId, userId])
  @@map("group_members")
}

model GroupDiscussion {
  id            String   @id @default(uuid())
  groupId       String   @map("group_id")
  creatorId     String?  @map("creator_id")
  title         String
  content       String?
  upvotes       Int      @default(0)
  downvotes     Int      @default(0)
  commentsCount Int      @default(0) @map("comments_count")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  group   Group @relation(fields: [groupId], references: [id], onDelete: Cascade)
  creator User? @relation(fields: [creatorId], references: [id], onDelete: SetNull)

  @@map("group_discussions")
}

// User profile interactions
model UserProfileLike {
  id           String   @id @default(uuid())
  likerId      String   @map("liker_id")
  likedUserId  String   @map("liked_user_id")
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  liker     User @relation("ProfileLiker", fields: [likerId], references: [id], onDelete: Cascade)
  likedUser User @relation("ProfileLiked", fields: [likedUserId], references: [id], onDelete: Cascade)

  @@unique([likerId, likedUserId])
  @@map("user_profile_likes")
}
