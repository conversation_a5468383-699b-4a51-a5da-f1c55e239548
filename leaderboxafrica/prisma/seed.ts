// Database Seed Script
// Populates the database with initial data for development

import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../src/lib/server/password.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Hash passwords for demo accounts
  const adminPasswordHash = await hashPassword('adminpass123');
  const userPasswordHash = await hashPassword('password123');

  // Create sample users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'Admin',
        isAdmin: true,
        state: 'Lagos',
        lga: 'Ikeja',
        gender: 'Male',
        party: 'Neutral',
        onboardingComplete: true,
        passwordHash: adminPasswordHash,
      },
    }),

    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'LeaderBox Admin',
        role: 'Admin',
        isAdmin: true,
        state: 'Lagos',
        lga: 'Ikeja',
        gender: 'Male',
        party: 'Neutral',
        onboardingComplete: true,
        passwordHash: adminPasswordHash,
      },
    }),

    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Demo User',
        role: 'User',
        isAdmin: false,
        state: 'Lagos',
        lga: 'Victoria Island',
        gender: 'Female',
        party: 'Neutral',
        onboardingComplete: true,
        passwordHash: userPasswordHash,
      },
    }),
  ]);

  console.log('✅ Created users:', users.length);

  // Create sample leaders
  const leaders = await Promise.all([
    prisma.leader.upsert({
      where: { id: 'leader-1' },
      update: {},
      create: {
        id: 'leader-1',
        name: 'Bola Ahmed Tinubu',
        position: 'President',
        party: 'APC',
        state: 'Lagos',
        bio: 'Current President of the Federal Republic of Nigeria',
        detailedBio: 'Bola Ahmed Tinubu is a Nigerian politician who has served as the President of Nigeria since 2023...',
        ratingAverage: 3.8,
        ratingCount: 450,
        followersCount: 1250,
      },
    }),
    prisma.leader.upsert({
      where: { id: 'leader-2' },
      update: {},
      create: {
        id: 'leader-2',
        name: 'Peter Obi',
        position: 'Former Governor',
        party: 'LP',
        state: 'Anambra',
        bio: 'Former Governor of Anambra State and 2023 Presidential Candidate',
        detailedBio: 'Peter Gregory Obi is a Nigerian businessman and politician who served as Governor of Anambra State...',
        ratingAverage: 4.2,
        ratingCount: 680,
        followersCount: 2100,
      },
    }),
    prisma.leader.upsert({
      where: { id: 'leader-3' },
      update: {},
      create: {
        id: 'leader-3',
        name: 'Atiku Abubakar',
        position: 'Former Vice President',
        party: 'PDP',
        state: 'Adamawa',
        bio: 'Former Vice President of Nigeria and 2023 Presidential Candidate',
        detailedBio: 'Atiku Abubakar is a Nigerian politician and businessman who served as Vice President of Nigeria...',
        ratingAverage: 3.5,
        ratingCount: 520,
        followersCount: 980,
      },
    }),
  ]);

  console.log('✅ Created leaders:', leaders.length);

  // Create sample polls
  const polls = await Promise.all([
    prisma.poll.create({
      data: {
        topic: 'Should Nigeria invest more in renewable energy?',
        description: 'Considering Nigeria\'s current energy crisis and climate change concerns, should the government prioritize renewable energy investments?',
        creatorId: users[0].id,
        totalVotes: 115,
        upvotes: 25,
        downvotes: 3,
        options: {
          create: [
            {
              text: 'Yes, prioritize renewable energy',
              votes: 75,
              optionOrder: 0,
            },
            {
              text: 'No, focus on traditional energy',
              votes: 40,
              optionOrder: 1,
            },
          ],
        },
      },
    }),
    prisma.poll.create({
      data: {
        topic: 'What should be the government\'s top priority?',
        description: 'In your opinion, what area should the Nigerian government focus on most urgently?',
        creatorId: users[0].id,
        totalVotes: 89,
        upvotes: 18,
        downvotes: 2,
        options: {
          create: [
            {
              text: 'Economy and Job Creation',
              votes: 35,
              optionOrder: 0,
            },
            {
              text: 'Security and Safety',
              votes: 28,
              optionOrder: 1,
            },
            {
              text: 'Education Reform',
              votes: 15,
              optionOrder: 2,
            },
            {
              text: 'Healthcare Improvement',
              votes: 11,
              optionOrder: 3,
            },
          ],
        },
      },
    }),
  ]);

  console.log('✅ Created polls:', polls.length);

  // Create sample banters
  const banters = await Promise.all([
    prisma.banter.create({
      data: {
        authorId: users[1].id,
        content: 'Just watched the latest presidential address. What are your thoughts on the new economic policies? #NigerianPolitics',
        upvotes: 12,
        downvotes: 2,
        commentsCount: 5,
      },
    }),
    prisma.banter.create({
      data: {
        authorId: users[0].id,
        content: 'Democracy works best when citizens are actively engaged. Let\'s keep the conversation going! 🇳🇬',
        upvotes: 8,
        downvotes: 0,
        commentsCount: 3,
      },
    }),
  ]);

  console.log('✅ Created banters:', banters.length);

  // Create sample petitions
  const petitions = await Promise.all([
    prisma.petition.create({
      data: {
        title: 'Improve Public Transportation in Lagos',
        description: 'We petition the Lagos State Government to invest more in public transportation infrastructure to reduce traffic congestion and improve quality of life for residents.',
        creatorId: users[1].id,
        signatures: 1250,
        status: 'active',
      },
    }),
    prisma.petition.create({
      data: {
        title: 'Increase Funding for Public Education',
        description: 'We call on the Federal Government to increase budget allocation for public education to ensure quality education for all Nigerian children.',
        creatorId: users[0].id,
        signatures: 890,
        status: 'active',
      },
    }),
  ]);

  console.log('✅ Created petitions:', petitions.length);

  // Create sample groups
  const groups = await Promise.all([
    prisma.group.create({
      data: {
        name: 'Lagos Youth Political Forum',
        description: 'A platform for young people in Lagos to discuss political issues and engage in civic activities.',
        creatorId: users[1].id,
        isPublic: true,
        status: 'visible',
        members: {
          create: [
            {
              userId: users[1].id,
              role: 'admin',
            },
            {
              userId: users[0].id,
              role: 'member',
            },
          ],
        },
      },
    }),
    prisma.group.create({
      data: {
        name: 'Nigerian Economic Policy Discussions',
        description: 'Discussing economic policies and their impact on Nigerian citizens.',
        creatorId: users[0].id,
        isPublic: true,
        status: 'visible',
        members: {
          create: [
            {
              userId: users[0].id,
              role: 'admin',
            },
          ],
        },
      },
    }),
  ]);

  console.log('✅ Created groups:', groups.length);

  console.log('🎉 Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
