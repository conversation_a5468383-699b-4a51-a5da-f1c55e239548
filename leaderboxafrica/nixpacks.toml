# Nixpacks configuration for LeaderBox SvelteKit app
# This file configures the build process for platforms that use Nixpacks (like Railway)

[variables]
NODE_ENV = "production"
PORT = "3000"
HOST = "0.0.0.0"

[phases.setup]
nixPkgs = ["nodejs_20", "pnpm"]

[phases.install]
cmds = [
    "pnpm install --frozen-lockfile"
]

[phases.build]
cmds = [
    "pnpm run db:generate",
    "pnpm run build"
]

[start]
cmd = "pnpm start"

[healthcheck]
path = "/health"
interval = 30
timeout = 10
retries = 3
