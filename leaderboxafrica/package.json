{"name": "leaderbox-sveltekit", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "start": "node build", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "type-check": "tsc --noEmit", "postbuild": "prisma generate", "docker:build": "docker build -t leaderbox .", "docker:run": "docker run -p 3000:3000 leaderbox"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/adapter-node": "^5.2.9", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.4.16", "tsx": "^4.20.3", "typescript": "^5.0.0", "vite": "^6.2.6"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@node-rs/argon2": "^2.0.2", "@prisma/client": "^6.11.1", "clsx": "^2.1.1", "lucide-svelte": "^0.525.0", "prisma": "^6.11.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.76"}}