// LeaderBox Server Hooks
// Handle authentication and session management for all requests

import type { Handle } from "@sveltejs/kit";
import { validateSessionToken, createBlankSessionCookie } from "$lib/server/auth.js";

export const handle: Handle = async ({ event, resolve }) => {
	// Get session token from cookie
	const sessionToken = event.cookies.get("session");
	
	if (!sessionToken) {
		event.locals.user = null;
		event.locals.session = null;
		return resolve(event);
	}
	
	// Validate session token
	const { session, user } = await validateSessionToken(sessionToken);
	
	if (!session || !user) {
		// Invalid session, clear cookie
		const blankCookie = createBlankSessionCookie();
		event.cookies.set(blankCookie.name, blankCookie.value, blankCookie.attributes);
		event.locals.user = null;
		event.locals.session = null;
		return resolve(event);
	}
	
	// Valid session, set locals
	event.locals.user = user;
	event.locals.session = session;
	
	return resolve(event);
};
