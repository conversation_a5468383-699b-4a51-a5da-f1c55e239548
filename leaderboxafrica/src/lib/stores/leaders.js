// Leaders Store
// Manages political leaders data and operations

import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// Mock leaders data for testing
const mockLeaders = [
	{
		id: 'leader-1',
		name: '<PERSON>',
		position: 'Former Governor',
		party: 'Labour Party',
		state: 'Anambra',
		avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
		bioSummary: 'Former Governor of Anambra State and presidential candidate.',
		followers: 2500000,
		createdAt: '2020-01-15T00:00:00Z'
	},
	{
		id: 'leader-2',
		name: '<PERSON><PERSON>',
		position: 'President',
		party: 'APC',
		state: 'Lagos',
		avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
		bioSummary: 'President of Nigeria and former Governor of Lagos State.',
		followers: 3200000,
		createdAt: '2019-05-20T00:00:00Z'
	},
	{
		id: 'leader-3',
		name: '<PERSON><PERSON><PERSON>',
		position: 'Former Vice President',
		party: 'PDP',
		state: '<PERSON><PERSON>',
		avatarUrl: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
		bioSummary: 'Former Vice President of Nigeria and presidential candidate.',
		followers: 1800000,
		createdAt: '2019-03-10T00:00:00Z'
	},
	{
		id: 'leader-4',
		name: 'Nyesom Wike',
		position: 'Former Governor',
		party: 'PDP',
		state: 'Rivers',
		avatarUrl: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150&h=150&fit=crop&crop=face',
		bioSummary: 'Former Governor of Rivers State.',
		followers: 950000,
		createdAt: '2020-08-12T00:00:00Z'
	},
	{
		id: 'leader-5',
		name: 'Babajide Sanwo-Olu',
		position: 'Governor',
		party: 'APC',
		state: 'Lagos',
		avatarUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
		bioSummary: 'Governor of Lagos State.',
		followers: 1200000,
		createdAt: '2021-02-28T00:00:00Z'
	}
];

// Leaders state
function createLeadersStore() {
	const { subscribe, set, update } = writable({
		leaders: [],
		loading: false,
		initialized: false
	});

	return {
		subscribe,
		
		// Initialize leaders data
		async initialize() {
			if (!browser) return;

			update(state => ({ ...state, loading: true }));

			try {
				// In a real app, this would fetch from an API
				// For now, we'll use mock data
				await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
				
				set({
					leaders: mockLeaders,
					loading: false,
					initialized: true
				});
			} catch (error) {
				console.error('Failed to load leaders:', error);
				set({
					leaders: [],
					loading: false,
					initialized: true
				});
			}
		},

		// Get leader by ID
		getLeaderById(id) {
			let currentState;
			subscribe(state => currentState = state)();
			return currentState.leaders.find(leader => leader.id === id);
		},

		// Search leaders
		searchLeaders(query) {
			let currentState;
			subscribe(state => currentState = state)();
			
			if (!query.trim()) return currentState.leaders;
			
			const searchTerm = query.toLowerCase();
			return currentState.leaders.filter(leader => 
				leader.name.toLowerCase().includes(searchTerm) ||
				leader.position.toLowerCase().includes(searchTerm) ||
				leader.party.toLowerCase().includes(searchTerm) ||
				leader.state.toLowerCase().includes(searchTerm)
			);
		},

		// Filter leaders by party
		filterByParty(party) {
			let currentState;
			subscribe(state => currentState = state)();
			
			if (!party) return currentState.leaders;
			return currentState.leaders.filter(leader => leader.party === party);
		},

		// Filter leaders by state
		filterByState(state) {
			let currentState;
			subscribe(state => currentState = state)();
			
			if (!state) return currentState.leaders;
			return currentState.leaders.filter(leader => leader.state === state);
		},

		// Get all unique parties
		getParties() {
			let currentState;
			subscribe(state => currentState = state)();
			
			const parties = [...new Set(currentState.leaders.map(leader => leader.party))];
			return parties.sort();
		},

		// Get all unique states
		getStates() {
			let currentState;
			subscribe(state => currentState = state)();
			
			const states = [...new Set(currentState.leaders.map(leader => leader.state))];
			return states.sort();
		},

		// Follow/unfollow leader (would integrate with auth store)
		async toggleFollow(leaderId) {
			// This would typically make an API call and update the auth store
			console.log('Toggle follow for leader:', leaderId);
			// For now, just log the action
		},

		// Add new leader (admin function)
		async addLeader(leaderData) {
			update(state => ({
				...state,
				leaders: [...state.leaders, { ...leaderData, id: `leader-${Date.now()}` }]
			}));
		},

		// Update leader (admin function)
		async updateLeader(leaderId, updates) {
			update(state => ({
				...state,
				leaders: state.leaders.map(leader => 
					leader.id === leaderId ? { ...leader, ...updates } : leader
				)
			}));
		},

		// Delete leader (admin function)
		async deleteLeader(leaderId) {
			update(state => ({
				...state,
				leaders: state.leaders.filter(leader => leader.id !== leaderId)
			}));
		}
	};
}

export const leadersStore = createLeadersStore();
