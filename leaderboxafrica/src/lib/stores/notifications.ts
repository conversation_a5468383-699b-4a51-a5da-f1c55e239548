// Notifications Store
// Manages toast notifications and alerts

import { writable } from 'svelte/store';
import type { NotificationState } from '$lib/types';

// Create notifications store
function createNotificationsStore() {
	const { subscribe, update } = writable<NotificationState[]>([]);

	return {
		subscribe,
		
		// Add a notification
		add(notification: Omit<NotificationState, 'id'>) {
			const id = crypto.randomUUID();
			const newNotification: NotificationState = {
				id,
				duration: 5000, // Default 5 seconds
				...notification
			};
			
			update(notifications => [...notifications, newNotification]);
			
			// Auto-remove after duration
			if (newNotification.duration && newNotification.duration > 0) {
				setTimeout(() => {
					this.remove(id);
				}, newNotification.duration);
			}
			
			return id;
		},
		
		// Remove a notification
		remove(id: string) {
			update(notifications => notifications.filter(n => n.id !== id));
		},
		
		// Clear all notifications
		clear() {
			update(() => []);
		},
		
		// Convenience methods
		success(message: string, duration?: number) {
			return this.add({ type: 'success', message, duration });
		},
		
		error(message: string, duration?: number) {
			return this.add({ type: 'error', message, duration });
		},
		
		warning(message: string, duration?: number) {
			return this.add({ type: 'warning', message, duration });
		},
		
		info(message: string, duration?: number) {
			return this.add({ type: 'info', message, duration });
		}
	};
}

export const notifications = createNotificationsStore();
