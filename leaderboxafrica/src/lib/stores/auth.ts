// Authentication Store
// Manages user authentication state and operations

import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import type { AuthUser, Session } from '$lib/types';

// Auth state
interface AuthState {
	user: AuthUser | null;
	session: Session | null;
	loading: boolean;
	initialized: boolean;
}

// Create the auth store
function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>({
		user: null,
		session: null,
		loading: false,
		initialized: false
	});

	return {
		subscribe,

		// Initialize auth state from server data (SSR)
		initializeFromServer(user: AuthUser | null, session: Session | null) {
			set({
				user,
				session,
				loading: false,
				initialized: true
			});
		},

		// Initialize auth state (called on app startup)
		async initialize() {
			if (!browser) return;

			// First, check localStorage for cached session to reduce flash
			try {
				const cachedSession = localStorage.getItem('leaderbox_session');
				if (cachedSession) {
					const session = JSON.parse(cachedSession);
					// Set a temporary state with cached data
					update(state => ({
						...state,
						session,
						loading: true,
						initialized: false // Still need to validate with server
					}));
				}
			} catch (error) {
				console.warn('Failed to load cached session:', error);
			}

			update(state => ({ ...state, loading: true }));

			try {
				// Validate session with server (using cookies)
				const response = await fetch('/api/auth/validate');
				const data = await response.json();

				if (data.user) {
					set({
						user: data.user,
						session: data.session,
						loading: false,
						initialized: true
					});
				} else {
					// Clear any cached session if validation failed
					if (browser) {
						localStorage.removeItem('leaderbox_session');
					}
					set({
						user: null,
						session: null,
						loading: false,
						initialized: true
					});
				}
			} catch (error) {
				console.error('Auth initialization failed:', error);
				// Clear any cached session on error
				if (browser) {
					localStorage.removeItem('leaderbox_session');
				}
				set({
					user: null,
					session: null,
					loading: false,
					initialized: true
				});
			}
		},

		// Login user
		async login(email: string, password: string) {
			update(state => ({ ...state, loading: true }));

			try {
				const response = await fetch('/api/auth/login', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ email, password })
				});

				const data = await response.json();

				if (data.success && data.user) {
					update(state => ({
						...state,
						user: data.user,
						session: null, // Session is managed via cookies
						loading: false
					}));

					return { success: true };
				} else {
					update(state => ({ ...state, loading: false }));
					return {
						success: false,
						error: data.error || 'Login failed'
					};
				}
			} catch (error) {
				update(state => ({ ...state, loading: false }));
				return {
					success: false,
					error: error instanceof Error ? error.message : 'Login failed'
				};
			}
		},

		// Register new user
		async register(userData: {
			email: string;
			password: string;
			name: string;
			state?: string;
			lga?: string;
			gender?: string;
			party?: string;
		}) {
			update(state => ({ ...state, loading: true }));
			
			try {
				const response = await fetch('/api/auth/register', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(userData)
				});
				
				const data = await response.json();

				if (data.success && data.user) {
					update(state => ({
						...state,
						user: data.user,
						session: null, // Session is managed via cookies
						loading: false
					}));

					return { success: true, user: data.user };
				} else {
					update(state => ({ ...state, loading: false }));
					return {
						success: false,
						error: data.error || 'Registration failed'
					};
				}
			} catch (error) {
				update(state => ({ ...state, loading: false }));
				return { 
					success: false, 
					error: error instanceof Error ? error.message : 'Registration failed' 
				};
			}
		},

		// Logout user
		async logout() {
			try {
				// Call logout endpoint
				await fetch('/api/auth/logout', {
					method: 'POST'
				});
			} catch (error) {
				console.error('Logout request failed:', error);
			}

			// Clear local state regardless of API call result
			set({
				user: null,
				session: null,
				loading: false,
				initialized: true
			});
		},

		// Update user profile
		async updateProfile(updates: any) {
			update(state => ({ ...state, loading: true }));

			try {
				const response = await fetch('/api/auth/profile', {
					method: 'PATCH',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(updates)
				});

				const data = await response.json();

				if (data.success && data.user) {
					update(state => ({
						...state,
						user: data.user,
						loading: false
					}));

					return { success: true, user: data.user };
				} else {
					update(state => ({ ...state, loading: false }));
					return {
						success: false,
						error: data.error || 'Profile update failed'
					};
				}
			} catch (error) {
				update(state => ({ ...state, loading: false }));
				return {
					success: false,
					error: error instanceof Error ? error.message : 'Profile update failed'
				};
			}
		},

		// Complete onboarding (for multi-step registration)
		async completeOnboarding(onboardingData: {
			name?: string;
			phone?: string;
			state?: string;
			lga?: string;
			gender?: string;
			party?: string;
			avatarUrl?: string;
		}) {
			return this.updateProfile({
				...onboardingData,
				onboardingComplete: true
			});
		},

		// Refresh session
		async refreshSession() {
			const currentState = get(authStore);
			if (!currentState.session?.refreshToken) return;

			try {
				const response = await fetch('/api/auth/refresh', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						refreshToken: currentState.session.refreshToken
					})
				});

				if (response.ok) {
					const { session } = await response.json();

					if (browser) {
						localStorage.setItem('leaderbox_session', JSON.stringify(session));
					}

					update(state => ({ ...state, session }));
				}
			} catch (error) {
				console.error('Session refresh failed:', error);
			}
		},

		// Update user details (for settings page)
		async updateUserDetails(userId: string, updates: any) {
			update(state => ({
				...state,
				user: state.user ? { ...state.user, ...updates } : null
			}));

			// In a real app, this would make an API call
			console.log('User details updated:', updates);
			return { success: true };
		},

		// Toggle profile like
		async toggleProfileLike(profileUserId: string) {
			const currentState = get(authStore);
			if (!currentState.user) return;

			// Mock implementation - in real app would call API
			console.log('Toggle profile like for user:', profileUserId);
			return { success: true };
		},

		// Get mock data for testing
		get users() {
			// Mock users data for testing
			return [
				{
					id: 'user-1',
					name: 'John Doe',
					email: '<EMAIL>',
					state: 'Lagos',
					party: 'APC',
					avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
					createdAt: '2023-01-15T00:00:00Z',
					profileLikesCount: 45,
					followedLeaders: ['leader-1', 'leader-2']
				},
				{
					id: 'user-2',
					name: 'Jane Smith',
					email: '<EMAIL>',
					state: 'Abuja',
					party: 'PDP',
					avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
					createdAt: '2023-02-20T00:00:00Z',
					profileLikesCount: 78,
					followedLeaders: ['leader-3']
				}
			];
		},

		// Get mock user profile likes
		get userProfileLikes() {
			// Mock profile likes data
			return {
				'user-1': ['user-2'],
				'user-2': ['user-1'],
				'leader-1': ['user-1', 'user-2']
			};
		}
	};
}

// Create and export the auth store
export const authStore = createAuthStore();

// Derived stores for convenience
export const user = derived(authStore, $auth => $auth.user);
export const isAuthenticated = derived(authStore, $auth => !!$auth.user);
export const isAdmin = derived(authStore, $auth => $auth.user?.isAdmin ?? false);
export const authLoading = derived(authStore, $auth => $auth.loading);
export const authInitialized = derived(authStore, $auth => $auth.initialized);

// Helper function to get current auth state
function get<T>(store: { subscribe: (fn: (value: T) => void) => () => void }): T {
	let value: T;
	const unsubscribe = store.subscribe(v => value = v);
	unsubscribe();
	return value!;
}
