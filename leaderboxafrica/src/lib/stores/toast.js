// Toast notification store for SvelteKit
import { writable } from 'svelte/store';

// Toast store
function createToastStore() {
	const { subscribe, update } = writable([]);

	return {
		subscribe,
		add: (toast) => {
			const id = Date.now() + Math.random();
			const newToast = {
				id,
				type: 'info',
				duration: 5000,
				...toast
			};

			update(toasts => [...toasts, newToast]);

			// Auto-remove toast after duration
			if (newToast.duration > 0) {
				setTimeout(() => {
					update(toasts => toasts.filter(t => t.id !== id));
				}, newToast.duration);
			}

			return id;
		},
		remove: (id) => {
			update(toasts => toasts.filter(t => t.id !== id));
		},
		clear: () => {
			update(() => []);
		}
	};
}

export const toastStore = createToastStore();
export const toasts = toastStore; // For backward compatibility

// Helper functions for different toast types
export const toast = {
	success: (message, options = {}) => toastStore.add({
		message,
		type: 'success',
		...options
	}),
	error: (message, options = {}) => toastStore.add({
		message,
		type: 'error',
		...options
	}),
	warning: (message, options = {}) => toastStore.add({
		message,
		type: 'warning',
		...options
	}),
	info: (message, options = {}) => toastStore.add({
		message,
		type: 'info',
		...options
	})
};
