// Polls Store - Manages poll data and operations
import { writable, derived } from 'svelte/store';
import { authStore } from './auth.js';
import { get } from 'svelte/store';

// Types
export interface PollOption {
	id: string;
	text: string;
	votes: number;
}

export interface PollComment {
	commentId: string;
	userId: string;
	userName: string;
	userAvatarUrl: string;
	text: string;
	timestamp: string;
	upvotes: number;
	downvotes: number;
	replies: PollComment[];
	media: any[];
}

export interface Poll {
	id: string;
	topic: string;
	description: string;
	options: PollOption[];
	source: string;
	createdAt: string;
	totalVotes: number;
	upvotes: number;
	downvotes: number;
	commentsCount: number;
	userVotes: Record<string, string>; // userId -> optionId
	comments: PollComment[];
	media: any[];
	taggedLeader?: string;
	taggedLocation?: string;
	generalTags?: string[];
}

export interface CreatePollData {
	topic: string;
	description: string;
	options: string[];
	taggedLeader?: string;
	taggedLocation?: string;
	generalTags?: string[];
	media?: any[];
}

// Initial mock data
const initialPollsData: Poll[] = [
	{
		id: 'poll1',
		topic: 'Should Nigeria invest more in renewable energy or fossil fuels for immediate energy needs?',
		description: "Considering Nigeria's current energy crisis and long-term environmental goals, what should be the primary focus for investment in the energy sector over the next 5 years? Renewable sources offer sustainability but may require significant upfront costs and infrastructure. Fossil fuels are established but have environmental drawbacks.",
		options: [
			{ id: 'opt1a', text: 'Renewable Energy', votes: 75 },
			{ id: 'opt1b', text: 'Fossil Fuels', votes: 40 },
			{ id: 'opt1c', text: 'A Balanced Mix', votes: 150 }
		],
		source: 'Admin',
		createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
		totalVotes: 265,
		upvotes: 25,
		downvotes: 3,
		commentsCount: 12,
		userVotes: {},
		comments: [
			{
				commentId: 'pc1',
				userId: 'userDemo123',
				userName: 'EcoWarrior',
				userAvatarUrl: 'https://avatar.vercel.sh/ecowarrior.png?size=32',
				text: 'Renewables are the future! We need to think long term.',
				timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
				upvotes: 10,
				downvotes: 1,
				replies: [],
				media: []
			}
		],
		media: [],
		taggedLeader: '',
		taggedLocation: 'Nigeria',
		generalTags: ['energy', 'environment', 'economy']
	},
	{
		id: 'poll2',
		topic: 'What should be the priority for Lagos State infrastructure development?',
		description: 'Lagos State continues to grow rapidly. What infrastructure area needs the most immediate attention?',
		options: [
			{ id: 'opt2a', text: 'Transportation (Roads, Rail)', votes: 120 },
			{ id: 'opt2b', text: 'Housing Development', votes: 85 },
			{ id: 'opt2c', text: 'Water & Sanitation', votes: 95 },
			{ id: 'opt2d', text: 'Digital Infrastructure', votes: 45 }
		],
		source: 'CitizenVoice',
		createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
		totalVotes: 345,
		upvotes: 18,
		downvotes: 2,
		commentsCount: 8,
		userVotes: {},
		comments: [],
		media: [],
		taggedLeader: 'Babajide Sanwo-Olu',
		taggedLocation: 'Lagos',
		generalTags: ['infrastructure', 'lagos', 'development']
	}
];

// Create stores
export const pollsStore = writable<Poll[]>([]);
export const userPollItemVotes = writable<Record<string, any>>({});

// Load initial data
if (typeof window !== 'undefined') {
	const localPolls = localStorage.getItem('polls');
	const storedPolls = localPolls ? JSON.parse(localPolls) : initialPollsData;
	pollsStore.set(storedPolls);

	const storedVotes = localStorage.getItem('userPollItemVotes');
	if (storedVotes) {
		userPollItemVotes.set(JSON.parse(storedVotes));
	}
}

// Subscribe to changes and save to localStorage
pollsStore.subscribe((polls) => {
	if (typeof window !== 'undefined') {
		localStorage.setItem('polls', JSON.stringify(polls));
	}
});

userPollItemVotes.subscribe((votes) => {
	if (typeof window !== 'undefined') {
		localStorage.setItem('userPollItemVotes', JSON.stringify(votes));
	}
});

// Derived stores
export const trendingPolls = derived(pollsStore, ($polls) => {
	return [...$polls].sort((a, b) => 
		(b.totalVotes + (b.comments?.length || 0)) - (a.totalVotes + (a.comments?.length || 0))
	);
});

export const newPolls = derived(pollsStore, ($polls) => {
	return [...$polls].sort((a, b) => 
		new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
	);
});

// Actions
export const pollActions = {
	createPoll: (pollData: CreatePollData): Poll | null => {
		const user = get(authStore).user;
		if (!user) return null;

		const newPoll: Poll = {
			id: `poll${Date.now()}`,
			...pollData,
			options: pollData.options.map((optText, index) => ({
				id: `opt${Date.now()}${index}`,
				text: optText,
				votes: 0
			})),
			source: user.name || user.email.split('@')[0],
			createdAt: new Date().toISOString(),
			totalVotes: 0,
			upvotes: 0,
			downvotes: 0,
			commentsCount: 0,
			userVotes: {},
			comments: [],
			media: pollData.media || [],
			generalTags: pollData.generalTags || []
		};

		pollsStore.update(polls => [newPoll, ...polls]);
		return newPoll;
	},

	voteOnPoll: (pollId: string, optionId: string, comment?: string) => {
		const user = get(authStore).user;
		if (!user) return;

		pollsStore.update(polls => polls.map(poll => {
			if (poll.id === pollId) {
				// Remove previous vote if exists
				const previousVote = poll.userVotes[user.id];
				if (previousVote) {
					poll.options = poll.options.map(opt => 
						opt.id === previousVote 
							? { ...opt, votes: Math.max(0, opt.votes - 1) }
							: opt
					);
					poll.totalVotes = Math.max(0, poll.totalVotes - 1);
				}

				// Add new vote
				poll.options = poll.options.map(opt => 
					opt.id === optionId 
						? { ...opt, votes: opt.votes + 1 }
						: opt
				);
				poll.userVotes[user.id] = optionId;
				poll.totalVotes = poll.options.reduce((sum, opt) => sum + opt.votes, 0);

				// Add comment if provided
				if (comment && comment.trim()) {
					const newComment: PollComment = {
						commentId: `pcmt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
						userId: user.id,
						userName: user.name || user.email.split('@')[0],
						userAvatarUrl: user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`,
						text: comment,
						timestamp: new Date().toISOString(),
						upvotes: 0,
						downvotes: 0,
						replies: [],
						media: []
					};
					poll.comments = [...(poll.comments || []), newComment];
					poll.commentsCount = (poll.commentsCount || 0) + 1;
				}
			}
			return poll;
		}));
	},

	addCommentToPoll: (pollId: string, text: string, media: any[] = []) => {
		const user = get(authStore).user;
		if (!user) return;

		pollsStore.update(polls => polls.map(poll => {
			if (poll.id === pollId) {
				const newComment: PollComment = {
					commentId: `pcmt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
					userId: user.id,
					userName: user.name || user.email.split('@')[0],
					userAvatarUrl: user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`,
					text,
					timestamp: new Date().toISOString(),
					upvotes: 0,
					downvotes: 0,
					replies: [],
					media: media || []
				};
				return {
					...poll,
					comments: [...(poll.comments || []), newComment],
					commentsCount: (poll.commentsCount || 0) + 1
				};
			}
			return poll;
		}));
	},

	deletePoll: (pollId: string) => {
		pollsStore.update(polls => polls.filter(poll => poll.id !== pollId));
	},

	getPollById: (pollId: string): Poll | undefined => {
		const polls = get(pollsStore);
		return polls.find(poll => poll.id === pollId);
	}
};
