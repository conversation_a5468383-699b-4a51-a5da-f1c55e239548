// Environment Variable Validation Utility
// Validates and provides type-safe access to environment variables using Zod

import { z } from 'zod';

// Define the schema for environment variables
const envSchema = z.object({
	// Database Configuration
	DATABASE_URL: z
		.string()
		.url('DATABASE_URL must be a valid URL')
		.refine(
			(url) => url.startsWith('postgresql://'),
			'DATABASE_URL must be a PostgreSQL connection string'
		),
	DIRECT_URL: z
		.string()
		.url('DIRECT_URL must be a valid URL')
		.refine(
			(url) => url.startsWith('postgresql://'),
			'DIRECT_URL must be a PostgreSQL connection string'
		),

	// Application Configuration
	ORIGIN: z
		.string()
		.url('ORIGIN must be a valid URL')
		.default('http://localhost:5173'),
	NODE_ENV: z
		.enum(['development', 'production', 'test'])
		.default('development'),

	// Zeptomail Configuration (for email functionality)
	ZEPTOMAIL_API_KEY: z
		.string()
		.min(1, 'ZEPTOMAIL_API_KEY is required for email functionality')
		.optional(),
	ZEPTOMAIL_FROM_EMAIL: z
		.string()
		.email('ZEPTOMAIL_FROM_EMAIL must be a valid email address')
		.optional(),
	ZEPTOMAIL_FROM_NAME: z
		.string()
		.min(1, 'ZEPTOMAIL_FROM_NAME is required')
		.default('LeaderBox'),

	// Security Configuration
	SESSION_SECRET: z
		.string()
		.min(32, 'SESSION_SECRET must be at least 32 characters long')
		.optional(),

	// Optional: External Service URLs
	APP_URL: z
		.string()
		.url('APP_URL must be a valid URL')
		.optional(),

	// AWS S3 Configuration (for file uploads)
	AWS_REGION: z
		.string()
		.min(1, 'AWS_REGION is required for file uploads')
		.default('us-east-1'),
	AWS_ACCESS_KEY_ID: z
		.string()
		.min(1, 'AWS_ACCESS_KEY_ID is required for file uploads')
		.optional(),
	AWS_SECRET_ACCESS_KEY: z
		.string()
		.min(1, 'AWS_SECRET_ACCESS_KEY is required for file uploads')
		.optional(),
	S3_BUCKET_NAME: z
		.string()
		.min(1, 'S3_BUCKET_NAME is required for file uploads')
		.optional(),
	S3_BUCKET_URL: z
		.string()
		.url('S3_BUCKET_URL must be a valid URL')
		.optional(),
	S3_ENDPOINT: z
		.string()
		.url('S3_ENDPOINT must be a valid URL')
		.optional(),
});

// Type for validated environment variables
export type Env = z.infer<typeof envSchema>;

// Validate environment variables
function validateEnv(): Env {
	// Check if we're in build mode
	const isBuildTime =
		process.env.BUILDING === 'true' ||
		process.env.NODE_ENV === undefined ||
		process.argv.includes('build') ||
		typeof window === 'undefined' && !process.env.DATABASE_URL;

	try {
		const env = envSchema.parse(process.env);
		return env;
	} catch (error) {
		if (error instanceof z.ZodError) {
			// During build time, provide mock values to prevent build failures
			if (isBuildTime) {
				console.warn('⚠️ Using mock environment values during build process');
				return {
					DATABASE_URL: 'postgresql://mock:mock@localhost:5432/mock',
					DIRECT_URL: 'postgresql://mock:mock@localhost:5432/mock',
					ORIGIN: 'http://localhost:5173',
					NODE_ENV: 'development',
					ZEPTOMAIL_FROM_NAME: 'LeaderBox',
					AWS_REGION: 'us-east-1',
				} as Env;
			}

			const missingVars = error.errors
				.map((err) => `${err.path.join('.')}: ${err.message}`)
				.join('\n');

			console.error('❌ Environment validation failed:');
			console.error(missingVars);
			console.error('\n📋 Required environment variables:');
			console.error('- DATABASE_URL: PostgreSQL connection string');
			console.error('- DIRECT_URL: PostgreSQL direct connection string');
			console.error('\n📋 Optional environment variables:');
			console.error('- ORIGIN: Application origin URL (default: http://localhost:5173)');
			console.error('- NODE_ENV: Environment mode (default: development)');
			console.error('- ZEPTOMAIL_API_KEY: Zeptomail API key for email functionality');
			console.error('- ZEPTOMAIL_FROM_EMAIL: From email address for outgoing emails');
			console.error('- ZEPTOMAIL_FROM_NAME: From name for outgoing emails (default: LeaderBox)');
			console.error('- SESSION_SECRET: Secret for session encryption');
			console.error('- APP_URL: Public application URL');
			console.error('- AWS_ACCESS_KEY_ID: AWS access key for S3 file uploads');
			console.error('- AWS_SECRET_ACCESS_KEY: AWS secret key for S3 file uploads');
			console.error('- S3_BUCKET_NAME: S3 bucket name for file storage');
			console.error('- S3_BUCKET_URL: S3 bucket URL for file access');
			console.error('- AWS_REGION: AWS region (default: us-east-1)');

			throw new Error('Environment validation failed. Please check your .env file.');
		}
		throw error;
	}
}

// Validated environment variables (singleton)
let _env: Env | null = null;

export function getEnv(): Env {
	if (!_env) {
		_env = validateEnv();
	}
	return _env;
}

// Individual environment variable getters for convenience
export const env = {
	get DATABASE_URL() {
		return getEnv().DATABASE_URL;
	},
	get DIRECT_URL() {
		return getEnv().DIRECT_URL;
	},
	get ORIGIN() {
		return getEnv().ORIGIN;
	},
	get NODE_ENV() {
		return getEnv().NODE_ENV;
	},
	get ZEPTOMAIL_API_KEY() {
		return getEnv().ZEPTOMAIL_API_KEY;
	},
	get ZEPTOMAIL_FROM_EMAIL() {
		return getEnv().ZEPTOMAIL_FROM_EMAIL;
	},
	get ZEPTOMAIL_FROM_NAME() {
		return getEnv().ZEPTOMAIL_FROM_NAME;
	},
	get SESSION_SECRET() {
		return getEnv().SESSION_SECRET;
	},
	get APP_URL() {
		return getEnv().APP_URL;
	},
	get AWS_REGION() {
		return getEnv().AWS_REGION;
	},
	get AWS_ACCESS_KEY_ID() {
		return getEnv().AWS_ACCESS_KEY_ID;
	},
	get AWS_SECRET_ACCESS_KEY() {
		return getEnv().AWS_SECRET_ACCESS_KEY;
	},
	get S3_BUCKET_NAME() {
		return getEnv().S3_BUCKET_NAME;
	},
	get S3_BUCKET_URL() {
		return getEnv().S3_BUCKET_URL;
	},
	get S3_ENDPOINT() {
		return getEnv().S3_ENDPOINT;
	},

	// Computed properties
	get isDevelopment() {
		return getEnv().NODE_ENV === 'development';
	},
	get isProduction() {
		return getEnv().NODE_ENV === 'production';
	},
	get isTest() {
		return getEnv().NODE_ENV === 'test';
	},
	
	// Email configuration check
	get isEmailConfigured() {
		const envVars = getEnv();
		return !!(envVars.ZEPTOMAIL_API_KEY && envVars.ZEPTOMAIL_FROM_EMAIL);
	},

	// AWS S3 configuration check
	get isS3Configured() {
		const envVars = getEnv();
		return !!(envVars.AWS_ACCESS_KEY_ID && envVars.AWS_SECRET_ACCESS_KEY && envVars.S3_BUCKET_NAME);
	}
};

// Check if we're in build mode (SvelteKit build process)
const isBuildTime =
	process.env.BUILDING === 'true' ||
	process.env.NODE_ENV === undefined ||
	process.argv.includes('build') ||
	typeof window === 'undefined' && !process.env.DATABASE_URL;

// Validate environment on module load (server-side only, but not during build)
if (typeof process !== 'undefined' && process.env && !isBuildTime) {
	try {
		getEnv();
		console.log('✅ Environment variables validated successfully');
	} catch (error) {
		console.error('❌ Environment validation failed on startup');
		// Don't throw here to allow the app to start for better error reporting
	}
}

// Export types for use in other files
export type { Env };

// Utility function to check if a specific environment variable is set
export function hasEnvVar(key: keyof Env): boolean {
	try {
		const envVars = getEnv();
		return envVars[key] !== undefined && envVars[key] !== null && envVars[key] !== '';
	} catch {
		return false;
	}
}

// Utility function to get environment variable with fallback
export function getEnvVar<K extends keyof Env>(
	key: K,
	fallback?: Env[K]
): Env[K] | typeof fallback {
	try {
		const envVars = getEnv();
		return envVars[key] ?? fallback;
	} catch {
		return fallback;
	}
}

// Development helper to log current environment configuration
export function logEnvConfig(): void {
	if (env.isDevelopment) {
		console.log('🔧 Environment Configuration:');
		console.log(`- NODE_ENV: ${env.NODE_ENV}`);
		console.log(`- ORIGIN: ${env.ORIGIN}`);
		console.log(`- Database: ${env.DATABASE_URL ? '✅ Configured' : '❌ Missing'}`);
		console.log(`- Email: ${env.isEmailConfigured ? '✅ Configured' : '❌ Missing'}`);
		console.log(`- Session Secret: ${env.SESSION_SECRET ? '✅ Configured' : '❌ Missing'}`);
		console.log(`- AWS S3: ${env.isS3Configured ? '✅ Configured' : '❌ Missing'}`);
	}
}
