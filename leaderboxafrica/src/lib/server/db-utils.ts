// LeaderBox Database Utilities
// Helper functions for common database operations

import { prisma } from './database.js';
import type { User, Leader, Poll, Banter, Petition, Group } from '@prisma/client';

// User utilities
export const userUtils = {
	// Find user by email
	async findByEmail(email: string) {
		return await prisma.user.findUnique({
			where: { email },
			include: {
				leaderFollows: {
					include: { leader: true }
				},
				groups: {
					include: { members: true }
				}
			}
		});
	},

	// Create new user
	async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) {
		return await prisma.user.create({
			data: userData
		});
	},

	// Update user profile
	async updateProfile(userId: string, data: Partial<User>) {
		return await prisma.user.update({
			where: { id: userId },
			data
		});
	}
};

// Leader utilities
export const leaderUtils = {
	// Get all leaders with ratings
	async getAll() {
		return await prisma.leader.findMany({
			include: {
				ratings: true,
				comments: {
					include: { user: true }
				},
				followers: true
			},
			orderBy: { ratingAverage: 'desc' }
		});
	},

	// Get leader by ID
	async getById(id: string) {
		return await prisma.leader.findUnique({
			where: { id },
			include: {
				ratings: {
					include: { user: true }
				},
				comments: {
					include: { user: true, votes: true }
				},
				followers: {
					include: { user: true }
				}
			}
		});
	},

	// Update leader rating average
	async updateRatingAverage(leaderId: string) {
		const ratings = await prisma.userLeaderRating.findMany({
			where: { leaderId }
		});

		const average = ratings.length > 0 
			? ratings.reduce((sum, rating) => sum + rating.rating, 0) / ratings.length 
			: 0;

		return await prisma.leader.update({
			where: { id: leaderId },
			data: {
				ratingAverage: average,
				ratingCount: ratings.length
			}
		});
	}
};

// Poll utilities
export const pollUtils = {
	// Get all polls with options and votes
	async getAll() {
		return await prisma.poll.findMany({
			include: {
				creator: true,
				options: {
					include: { pollVotes: true }
				},
				votes: {
					include: { user: true, option: true }
				},
				comments: {
					include: { user: true }
				}
			},
			orderBy: { createdAt: 'desc' }
		});
	},

	// Get poll by ID
	async getById(id: string) {
		return await prisma.poll.findUnique({
			where: { id },
			include: {
				creator: true,
				options: {
					include: { pollVotes: true }
				},
				votes: {
					include: { user: true, option: true }
				},
				comments: {
					include: { user: true }
				}
			}
		});
	},

	// Update poll vote counts
	async updateVoteCounts(pollId: string) {
		const votes = await prisma.pollVote.findMany({
			where: { pollId }
		});

		return await prisma.poll.update({
			where: { id: pollId },
			data: {
				totalVotes: votes.length
			}
		});
	}
};

// Banter utilities
export const banterUtils = {
	// Get all banters with votes and comments
	async getAll() {
		return await prisma.banter.findMany({
			include: {
				author: true,
				votes: {
					include: { user: true }
				},
				comments: {
					include: { user: true }
				}
			},
			orderBy: { createdAt: 'desc' }
		});
	},

	// Get banter by ID
	async getById(id: string) {
		return await prisma.banter.findUnique({
			where: { id },
			include: {
				author: true,
				votes: {
					include: { user: true }
				},
				comments: {
					include: { user: true }
				}
			}
		});
	}
};

// Petition utilities
export const petitionUtils = {
	// Get all petitions with signatures
	async getAll() {
		return await prisma.petition.findMany({
			include: {
				creator: true,
				petitionSignatures: {
					include: { user: true }
				}
			},
			orderBy: { createdAt: 'desc' }
		});
	},

	// Get petition by ID
	async getById(id: string) {
		return await prisma.petition.findUnique({
			where: { id },
			include: {
				creator: true,
				petitionSignatures: {
					include: { user: true }
				}
			}
		});
	},

	// Update signature count
	async updateSignatureCount(petitionId: string) {
		const signatures = await prisma.petitionSignature.findMany({
			where: { petitionId }
		});

		return await prisma.petition.update({
			where: { id: petitionId },
			data: {
				signatures: signatures.length
			}
		});
	}
};

// Group utilities
export const groupUtils = {
	// Get all groups with members
	async getAll() {
		return await prisma.group.findMany({
			include: {
				creator: true,
				members: {
					include: { user: true }
				},
				discussions: {
					include: { creator: true }
				}
			},
			orderBy: { createdAt: 'desc' }
		});
	},

	// Get group by ID
	async getById(id: string) {
		return await prisma.group.findUnique({
			where: { id },
			include: {
				creator: true,
				members: {
					include: { user: true }
				},
				discussions: {
					include: { creator: true }
				}
			}
		});
	},

	// Get groups for user
	async getForUser(userId: string) {
		return await prisma.group.findMany({
			where: {
				members: {
					some: { userId }
				}
			},
			include: {
				creator: true,
				members: {
					include: { user: true }
				}
			}
		});
	}
};

// Database health check
export async function checkDatabaseConnection() {
	try {
		await prisma.$queryRaw`SELECT 1`;
		return { status: 'connected', message: 'Database connection successful' };
	} catch (error) {
		return { 
			status: 'error', 
			message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
		};
	}
}

// Database statistics
export async function getDatabaseStats() {
	try {
		const [
			userCount,
			leaderCount,
			pollCount,
			banterCount,
			petitionCount,
			groupCount
		] = await Promise.all([
			prisma.user.count(),
			prisma.leader.count(),
			prisma.poll.count(),
			prisma.banter.count(),
			prisma.petition.count(),
			prisma.group.count()
		]);

		return {
			users: userCount,
			leaders: leaderCount,
			polls: pollCount,
			banters: banterCount,
			petitions: petitionCount,
			groups: groupCount
		};
	} catch (error) {
		throw new Error(`Failed to get database stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}
