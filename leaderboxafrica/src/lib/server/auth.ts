// LeaderBox Authentication Configuration
// Custom session management based on <PERSON> Auth best practices

import { dev } from "$app/environment";
import { prisma } from "./database.js";

// Session configuration
const SESSION_EXPIRES_IN_SECONDS = 60 * 60 * 24 * 7; // 7 days

// Types
export interface Session {
	id: string;
	userId: string;
	secretHash: Uint8Array;
	createdAt: Date;
	expiresAt: Date;
}

export interface SessionWithToken extends Session {
	token: string;
}

export interface User {
	id: string;
	email: string;
	name: string | null;
	role: string;
	isAdmin: boolean;
	state: string | null;
	lga: string | null;
	gender: string | null;
	party: string | null;
	avatarUrl: string | null;
	onboardingComplete: boolean;
	status: string;
}

// Secure random string generation
function generateSecureRandomString(): string {
	// Human readable alphabet (a-z, 0-9 without l, o, 0, 1 to avoid confusion)
	const alphabet = "abcdefghijklmnpqrstuvwxyz23456789";

	// Generate 24 bytes = 192 bits of entropy.
	// We're only going to use 5 bits per byte so the total entropy will be 192 * 5 / 8 = 120 bits
	const bytes = new Uint8Array(24);
	crypto.getRandomValues(bytes);

	let id = "";
	for (let i = 0; i < bytes.length; i++) {
		// >> 3 "removes" the right-most 3 bits of the byte
		id += alphabet[bytes[i] >> 3];
	}
	return id;
}

// Hash secret using SHA-256
async function hashSecret(secret: string): Promise<Uint8Array> {
	const secretBytes = new TextEncoder().encode(secret);
	const secretHashBuffer = await crypto.subtle.digest("SHA-256", secretBytes);
	return new Uint8Array(secretHashBuffer);
}

// Constant-time comparison for security
function constantTimeEqual(a: Uint8Array, b: Uint8Array): boolean {
	if (a.byteLength !== b.byteLength) {
		return false;
	}
	let c = 0;
	for (let i = 0; i < a.byteLength; i++) {
		c |= a[i] ^ b[i];
	}
	return c === 0;
}

// Authentication utility functions
export async function createUser(userData: {
	email: string;
	passwordHash: string;
	name?: string;
	state?: string;
	lga?: string;
	gender?: string;
	party?: string;
}) {
	try {
		const user = await prisma.user.create({
			data: {
				email: userData.email,
				passwordHash: userData.passwordHash,
				name: userData.name,
				state: userData.state,
				lga: userData.lga,
				gender: userData.gender,
				party: userData.party,
				role: "User",
				status: "active",
				isAdmin: false,
				onboardingComplete: false
			}
		});
		return user;
	} catch (error) {
		throw new Error(`Failed to create user: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

export async function getUserByEmail(email: string) {
	try {
		const user = await prisma.user.findUnique({
			where: { email }
		});
		return user;
	} catch (error) {
		throw new Error(`Failed to get user by email: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

export async function getUserById(id: string) {
	try {
		const user = await prisma.user.findUnique({
			where: { id }
		});
		return user;
	} catch (error) {
		throw new Error(`Failed to get user by ID: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

export async function updateUser(id: string, data: Partial<{
	name: string;
	state: string;
	lga: string;
	gender: string;
	party: string;
	avatarUrl: string;
	onboardingComplete: boolean;
}>) {
	try {
		const user = await prisma.user.update({
			where: { id },
			data
		});
		return user;
	} catch (error) {
		throw new Error(`Failed to update user: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

// Password hashing utilities
export { hashPassword, verifyPassword } from './password.js';

// Session management functions
export async function createSession(userId: string): Promise<SessionWithToken> {
	const now = new Date();
	const expiresAt = new Date(now.getTime() + SESSION_EXPIRES_IN_SECONDS * 1000);

	const id = generateSecureRandomString();
	const secret = generateSecureRandomString();
	const secretHash = await hashSecret(secret);

	const token = id + "." + secret;

	const session: SessionWithToken = {
		id,
		userId,
		secretHash,
		createdAt: now,
		expiresAt,
		token
	};

	await prisma.session.create({
		data: {
			id: session.id,
			userId: session.userId,
			secretHash: Buffer.from(session.secretHash),
			expiresAt: session.expiresAt
		}
	});

	return session;
}

export async function validateSessionToken(token: string): Promise<{ session: Session | null; user: User | null }> {
	const tokenParts = token.split(".");
	if (tokenParts.length !== 2) {
		return { session: null, user: null };
	}

	const sessionId = tokenParts[0];
	const sessionSecret = tokenParts[1];

	try {
		const sessionData = await prisma.session.findUnique({
			where: { id: sessionId },
			include: {
				user: true
			}
		});

		if (!sessionData) {
			return { session: null, user: null };
		}

		// Check expiration
		const now = new Date();
		if (now > sessionData.expiresAt) {
			await deleteSession(sessionId);
			return { session: null, user: null };
		}

		// Verify secret
		const tokenSecretHash = await hashSecret(sessionSecret);
		const storedSecretHash = new Uint8Array(sessionData.secretHash);

		if (!constantTimeEqual(tokenSecretHash, storedSecretHash)) {
			return { session: null, user: null };
		}

		const session: Session = {
			id: sessionData.id,
			userId: sessionData.userId,
			secretHash: storedSecretHash,
			createdAt: sessionData.createdAt,
			expiresAt: sessionData.expiresAt
		};

		const user: User = {
			id: sessionData.user.id,
			email: sessionData.user.email,
			name: sessionData.user.name,
			role: sessionData.user.role,
			isAdmin: sessionData.user.isAdmin,
			state: sessionData.user.state,
			lga: sessionData.user.lga,
			gender: sessionData.user.gender,
			party: sessionData.user.party,
			avatarUrl: sessionData.user.avatarUrl,
			onboardingComplete: sessionData.user.onboardingComplete,
			status: sessionData.user.status
		};

		return { session, user };
	} catch (error) {
		return { session: null, user: null };
	}
}

export async function deleteSession(sessionId: string): Promise<void> {
	try {
		await prisma.session.delete({
			where: { id: sessionId }
		});
	} catch (error) {
		// Session might not exist, which is fine
	}
}

export async function deleteAllUserSessions(userId: string): Promise<void> {
	try {
		await prisma.session.deleteMany({
			where: { userId }
		});
	} catch (error) {
		throw new Error(`Failed to delete user sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

// Cookie configuration
export function createSessionCookie(token: string): { name: string; value: string; attributes: Record<string, any> } {
	return {
		name: "session",
		value: token,
		attributes: {
			httpOnly: true,
			secure: !dev,
			sameSite: "lax",
			path: "/",
			maxAge: SESSION_EXPIRES_IN_SECONDS
		}
	};
}

export function createBlankSessionCookie(): { name: string; value: string; attributes: Record<string, any> } {
	return {
		name: "session",
		value: "",
		attributes: {
			httpOnly: true,
			secure: !dev,
			sameSite: "lax",
			path: "/",
			maxAge: 0
		}
	};
}
