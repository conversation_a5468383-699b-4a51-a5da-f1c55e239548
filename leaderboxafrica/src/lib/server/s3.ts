// AWS S3 Configuration and Utilities
// Handles file uploads to S3 with presigned URLs

import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import { env } from './env.js';

// Check if we're in build mode
const isBuildTime =
	process.env.BUILDING === 'true' ||
	process.env.NODE_ENV === undefined ||
	process.argv.includes('build') ||
	typeof window === 'undefined' && !process.env.DATABASE_URL;

// Validate required environment variables (skip during build)
if (!isBuildTime) {
	try {
		if (!env.isS3Configured) {
			if (env.isProduction) {
				throw new Error('Missing required AWS S3 environment variables');
			}
			console.warn('AWS S3 environment variables not configured - file upload will not work');
		}
	} catch (error) {
		// During build, environment validation might fail, so we skip it
		console.warn('S3 configuration check skipped during build');
	}
}

// Lazy S3 client initialization
let _s3Client: S3Client | null = null;

export function getS3Client(): S3Client {
	if (!_s3Client) {
		_s3Client = new S3Client({
			region: env.AWS_REGION,
			credentials: {
				accessKeyId: env.AWS_ACCESS_KEY_ID || '',
				secretAccessKey: env.AWS_SECRET_ACCESS_KEY || ''
			},
			...(env.S3_ENDPOINT && { endpoint: env.S3_ENDPOINT })
		});
	}
	return _s3Client;
}

// Backward compatibility
export const s3Client = new Proxy({} as S3Client, {
	get(target, prop) {
		return getS3Client()[prop as keyof S3Client];
	}
});

// Supported file types and their MIME types
export const SUPPORTED_FILE_TYPES = {
	// Images
	'image/jpeg': { extension: 'jpg', category: 'image', maxSize: 5 * 1024 * 1024 }, // 5MB
	'image/jpg': { extension: 'jpg', category: 'image', maxSize: 5 * 1024 * 1024 },
	'image/png': { extension: 'png', category: 'image', maxSize: 5 * 1024 * 1024 },
	'image/webp': { extension: 'webp', category: 'image', maxSize: 5 * 1024 * 1024 },
	'image/gif': { extension: 'gif', category: 'image', maxSize: 5 * 1024 * 1024 },
	// Documents
	'application/pdf': { extension: 'pdf', category: 'document', maxSize: 10 * 1024 * 1024 }, // 10MB
	'application/msword': { extension: 'doc', category: 'document', maxSize: 10 * 1024 * 1024 },
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { 
		extension: 'docx', 
		category: 'document', 
		maxSize: 10 * 1024 * 1024 
	}
};

// File upload configuration
export interface FileUploadConfig {
	folder: string; // S3 folder/prefix
	allowedTypes: string[]; // MIME types
	maxSize: number; // bytes
	generateThumbnail?: boolean;
}

// Predefined configurations for different use cases
export const UPLOAD_CONFIGS = {
	avatar: {
		folder: 'avatars',
		allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
		maxSize: 2 * 1024 * 1024, // 2MB
		generateThumbnail: true
	},
	cover: {
		folder: 'covers',
		allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
		maxSize: 5 * 1024 * 1024, // 5MB
		generateThumbnail: false
	},
	documents: {
		folder: 'documents',
		allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
		maxSize: 10 * 1024 * 1024, // 10MB
		generateThumbnail: false
	}
} as const;

// Generate unique filename
export function generateFileName(originalName: string, folder: string): string {
	const extension = originalName.split('.').pop()?.toLowerCase() || '';
	const uuid = uuidv4();
	const timestamp = Date.now();
	return `${folder}/${timestamp}-${uuid}.${extension}`;
}

// Validate file
export function validateFile(file: { name: string; type: string; size: number }, config: FileUploadConfig): { valid: boolean; error?: string } {
	// Check file type
	if (!config.allowedTypes.includes(file.type)) {
		return {
			valid: false,
			error: `File type ${file.type} is not allowed. Allowed types: ${config.allowedTypes.join(', ')}`
		};
	}

	// Check file size
	if (file.size > config.maxSize) {
		const maxSizeMB = (config.maxSize / (1024 * 1024)).toFixed(1);
		return {
			valid: false,
			error: `File size ${(file.size / (1024 * 1024)).toFixed(1)}MB exceeds maximum allowed size of ${maxSizeMB}MB`
		};
	}

	return { valid: true };
}

// Generate presigned URL for upload
export async function generatePresignedUploadUrl(
	fileName: string,
	fileType: string,
	config: FileUploadConfig
): Promise<{ uploadUrl: string; fileUrl: string; key: string }> {
	if (!env.S3_BUCKET_NAME) {
		throw new Error('S3 bucket name not configured');
	}

	const key = generateFileName(fileName, config.folder);
	
	const command = new PutObjectCommand({
		Bucket: env.S3_BUCKET_NAME,
		Key: key,
		ContentType: fileType,
		// Note: ACL removed - using bucket policy for public access instead
		// Add metadata
		Metadata: {
			'original-name': fileName,
			'upload-timestamp': Date.now().toString()
		}
	});

	const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour

	// Generate the final file URL
	const fileUrl = env.S3_BUCKET_URL
		? `${env.S3_BUCKET_URL}/${key}`
		: `https://${env.S3_BUCKET_NAME}.s3.${env.AWS_REGION}.amazonaws.com/${key}`;

	return {
		uploadUrl,
		fileUrl,
		key
	};
}

// Delete file from S3
export async function deleteFileFromS3(key: string): Promise<boolean> {
	if (!env.S3_BUCKET_NAME) {
		throw new Error('S3 bucket name not configured');
	}

	try {
		const command = new DeleteObjectCommand({
			Bucket: env.S3_BUCKET_NAME,
			Key: key
		});

		await s3Client.send(command);
		return true;
	} catch (error) {
		console.error('Error deleting file from S3:', error);
		return false;
	}
}

// Extract S3 key from URL
export function extractS3KeyFromUrl(url: string): string | null {
	if (!url) return null;

	// Handle different URL formats
	const patterns = [
		// https://bucket.s3.region.amazonaws.com/key
		/https:\/\/[^.]+\.s3\.[^.]+\.amazonaws\.com\/(.+)/,
		// https://s3.region.amazonaws.com/bucket/key
		/https:\/\/s3\.[^.]+\.amazonaws\.com\/[^/]+\/(.+)/,
		// Custom domain
		/https:\/\/[^/]+\/(.+)/
	];

	for (const pattern of patterns) {
		const match = url.match(pattern);
		if (match) {
			return match[1];
		}
	}

	return null;
}

// Cleanup files when a record is deleted
export async function cleanupFiles(urls: string[]): Promise<{ success: number; failed: number }> {
	let success = 0;
	let failed = 0;

	for (const url of urls) {
		if (!url) continue;

		const key = extractS3KeyFromUrl(url);
		if (!key) {
			failed++;
			continue;
		}

		try {
			const deleted = await deleteFileFromS3(key);
			if (deleted) {
				success++;
			} else {
				failed++;
			}
		} catch (error) {
			console.error(`Failed to delete file ${key}:`, error);
			failed++;
		}
	}

	return { success, failed };
}
