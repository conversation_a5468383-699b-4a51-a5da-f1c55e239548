// Password Hashing Utilities
// Standalone password utilities that can be used in any Node.js context

import { hash, verify } from "@node-rs/argon2";

/**
 * Hash a password using Argon2id
 */
export async function hashPassword(password: string): Promise<string> {
	return hash(password, {
		// Recommended configuration for Argon2id
		memoryCost: 19456,
		timeCost: 2,
		outputLen: 32,
		parallelism: 1
	});
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(hash: string, password: string): Promise<boolean> {
	return verify(hash, password);
}
