// Zeptomail Email Client
// Reusable email utility for sending various types of emails

import { env } from './env.js';

// Email template types
export interface EmailTemplate {
	subject: string;
	htmlContent: string;
	textContent?: string;
}

// Email recipient interface
export interface EmailRecipient {
	email: string;
	name?: string;
}

// Zeptomail API response interface
interface ZeptomailResponse {
	data: Array<{
		code: string;
		additional_info: string;
		message: string;
	}>;
	message: string;
}

// Email client class
export class ZeptomailClient {
	private apiKey: string;
	private fromEmail: string;
	private fromName: string;
	private apiUrl = 'https://api.zeptomail.com/v1.1/email';

	constructor() {
		if (!env.ZEPTOMAIL_API_KEY || !env.ZEPTOMAIL_FROM_EMAIL) {
			throw new Error('Zeptomail configuration is missing. Please set ZEPTOMAIL_API_KEY and ZEPTOMAIL_FROM_EMAIL environment variables.');
		}
		
		this.apiKey = env.ZEPTOMAIL_API_KEY;
		this.fromEmail = env.ZEPTOMAIL_FROM_EMAIL;
		this.fromName = env.ZEPTOMAIL_FROM_NAME || 'LeaderBox';
	}

	/**
	 * Send a single email
	 */
	async sendEmail(
		to: EmailRecipient,
		template: EmailTemplate,
		options?: {
			replyTo?: string;
			trackClicks?: boolean;
			trackOpens?: boolean;
		}
	): Promise<boolean> {
		try {
			const payload = {
				from: {
					address: this.fromEmail,
					name: this.fromName
				},
				to: [
					{
						email_address: {
							address: to.email,
							name: to.name || to.email
						}
					}
				],
				subject: template.subject,
				htmlbody: template.htmlContent,
				textbody: template.textContent || this.stripHtml(template.htmlContent),
				reply_to: options?.replyTo ? [{ address: options.replyTo }] : undefined,
				track_clicks: options?.trackClicks ?? true,
				track_opens: options?.trackOpens ?? true
			};

			const response = await fetch(this.apiUrl, {
				method: 'POST',
				headers: {
					'Accept': 'application/json',
					'Content-Type': 'application/json',
					'Authorization': `Zoho-enczapikey ${this.apiKey}`
				},
				body: JSON.stringify(payload)
			});

			if (!response.ok) {
				const errorData = await response.text();
				console.error('Zeptomail API error:', response.status, errorData);
				return false;
			}

			const result: ZeptomailResponse = await response.json();
			
			// Check if email was sent successfully
			const success = result.data?.every(item => item.code === 'SENT');
			
			if (!success) {
				console.error('Zeptomail send failed:', result);
				return false;
			}

			console.log('✅ Email sent successfully to:', to.email);
			return true;

		} catch (error) {
			console.error('Email sending error:', error);
			return false;
		}
	}

	/**
	 * Send bulk emails (up to 50 recipients)
	 */
	async sendBulkEmail(
		recipients: EmailRecipient[],
		template: EmailTemplate,
		options?: {
			replyTo?: string;
			trackClicks?: boolean;
			trackOpens?: boolean;
		}
	): Promise<boolean> {
		if (recipients.length > 50) {
			throw new Error('Zeptomail bulk email limit is 50 recipients per request');
		}

		try {
			const payload = {
				from: {
					address: this.fromEmail,
					name: this.fromName
				},
				to: recipients.map(recipient => ({
					email_address: {
						address: recipient.email,
						name: recipient.name || recipient.email
					}
				})),
				subject: template.subject,
				htmlbody: template.htmlContent,
				textbody: template.textContent || this.stripHtml(template.htmlContent),
				reply_to: options?.replyTo ? [{ address: options.replyTo }] : undefined,
				track_clicks: options?.trackClicks ?? true,
				track_opens: options?.trackOpens ?? true
			};

			const response = await fetch(this.apiUrl, {
				method: 'POST',
				headers: {
					'Accept': 'application/json',
					'Content-Type': 'application/json',
					'Authorization': `Zoho-enczapikey ${this.apiKey}`
				},
				body: JSON.stringify(payload)
			});

			if (!response.ok) {
				const errorData = await response.text();
				console.error('Zeptomail bulk API error:', response.status, errorData);
				return false;
			}

			const result: ZeptomailResponse = await response.json();
			
			// Check if all emails were sent successfully
			const success = result.data?.every(item => item.code === 'SENT');
			
			if (!success) {
				console.error('Zeptomail bulk send failed:', result);
				return false;
			}

			console.log(`✅ Bulk email sent successfully to ${recipients.length} recipients`);
			return true;

		} catch (error) {
			console.error('Bulk email sending error:', error);
			return false;
		}
	}

	/**
	 * Strip HTML tags for text content
	 */
	private stripHtml(html: string): string {
		return html
			.replace(/<[^>]*>/g, '')
			.replace(/&nbsp;/g, ' ')
			.replace(/&amp;/g, '&')
			.replace(/&lt;/g, '<')
			.replace(/&gt;/g, '>')
			.replace(/&quot;/g, '"')
			.trim();
	}

	/**
	 * Test email configuration
	 */
	async testConfiguration(): Promise<boolean> {
		try {
			const testTemplate: EmailTemplate = {
				subject: 'LeaderBox Email Configuration Test',
				htmlContent: `
					<h2>Email Configuration Test</h2>
					<p>This is a test email to verify your Zeptomail configuration is working correctly.</p>
					<p>If you receive this email, your email service is properly configured!</p>
					<hr>
					<p><small>Sent from LeaderBox Email Service</small></p>
				`
			};

			return await this.sendEmail(
				{ email: this.fromEmail, name: 'Test Recipient' },
				testTemplate
			);
		} catch (error) {
			console.error('Email configuration test failed:', error);
			return false;
		}
	}
}

// Singleton instance
let emailClient: ZeptomailClient | null = null;

export function getEmailClient(): ZeptomailClient {
	if (!emailClient) {
		emailClient = new ZeptomailClient();
	}
	return emailClient;
}

// Convenience function to check if email is configured
export function isEmailConfigured(): boolean {
	return env.isEmailConfigured;
}

// Email template generators
export const emailTemplates = {
	/**
	 * Password reset email template
	 */
	passwordReset: (resetUrl: string, userName: string): EmailTemplate => ({
		subject: 'Reset Your LeaderBox Password',
		htmlContent: `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="utf-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>Reset Your Password</title>
				<style>
					body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
					.container { max-width: 600px; margin: 0 auto; padding: 20px; }
					.header { background: linear-gradient(135deg, #2563eb, #16a34a); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
					.content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
					.button { display: inline-block; background: linear-gradient(135deg, #2563eb, #16a34a); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; }
					.footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
					.warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
				</style>
			</head>
			<body>
				<div class="container">
					<div class="header">
						<h1>🔐 Password Reset Request</h1>
						<p>LeaderBox Account Security</p>
					</div>
					<div class="content">
						<h2>Hello ${userName}!</h2>
						<p>We received a request to reset your LeaderBox account password. If you made this request, click the button below to reset your password:</p>
						
						<div style="text-align: center;">
							<a href="${resetUrl}" class="button">Reset My Password</a>
						</div>
						
						<div class="warning">
							<strong>⚠️ Security Notice:</strong>
							<ul>
								<li>This link will expire in 1 hour for security reasons</li>
								<li>If you didn't request this reset, please ignore this email</li>
								<li>Never share this link with anyone</li>
							</ul>
						</div>
						
						<p>If the button doesn't work, copy and paste this link into your browser:</p>
						<p style="word-break: break-all; background: #e5e7eb; padding: 10px; border-radius: 4px; font-family: monospace;">${resetUrl}</p>
						
						<p>If you have any questions or concerns, please contact our support team.</p>
						
						<p>Best regards,<br>The LeaderBox Team</p>
					</div>
					<div class="footer">
						<p>This email was sent to ${userName} from LeaderBox.<br>
						If you didn't request this email, please ignore it.</p>
					</div>
				</div>
			</body>
			</html>
		`
	}),

	/**
	 * Welcome email template
	 */
	welcome: (userName: string): EmailTemplate => ({
		subject: 'Welcome to LeaderBox! 🎉',
		htmlContent: `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="utf-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>Welcome to LeaderBox</title>
				<style>
					body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
					.container { max-width: 600px; margin: 0 auto; padding: 20px; }
					.header { background: linear-gradient(135deg, #2563eb, #16a34a); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
					.content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
					.feature { background: white; padding: 20px; margin: 15px 0; border-radius: 6px; border-left: 4px solid #2563eb; }
					.footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
				</style>
			</head>
			<body>
				<div class="container">
					<div class="header">
						<h1>🎉 Welcome to LeaderBox!</h1>
						<p>Your Political Engagement Platform</p>
					</div>
					<div class="content">
						<h2>Hello ${userName}!</h2>
						<p>Welcome to LeaderBox! We're excited to have you join our community of engaged citizens working to make a difference in Nigerian politics.</p>
						
						<div class="feature">
							<h3>🗳️ Rate & Review Leaders</h3>
							<p>Share your opinions on political leaders and see what others think.</p>
						</div>
						
						<div class="feature">
							<h3>📊 Participate in Polls</h3>
							<p>Vote on important issues and see real-time results from your community.</p>
						</div>
						
						<div class="feature">
							<h3>💬 Join Discussions</h3>
							<p>Engage in meaningful political conversations in our banter rooms.</p>
						</div>
						
						<div class="feature">
							<h3>📝 Create Petitions</h3>
							<p>Start petitions for causes you care about and gather support.</p>
						</div>
						
						<p>Ready to get started? Log in to your account and explore all that LeaderBox has to offer!</p>
						
						<p>Best regards,<br>The LeaderBox Team</p>
					</div>
					<div class="footer">
						<p>Thank you for joining LeaderBox - Empowering Nigerian Democracy</p>
					</div>
				</div>
			</body>
			</html>
		`
	})
};
