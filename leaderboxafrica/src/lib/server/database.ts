// LeaderBox Database Configuration
// This file sets up the Prisma client for server-side database operations

import { PrismaClient } from '@prisma/client';
import { dev } from '$app/environment';
import { env } from './env.js';

// Global variable to store the Prisma client instance
declare global {
	var __prisma: PrismaClient | undefined;
}

// Create a single instance of Prisma client
// In development, use global variable to prevent multiple instances
// In production, create a new instance each time
export const prisma = globalThis.__prisma || new PrismaClient({
	log: dev ? ['query', 'error', 'warn'] : ['error'],
});

// In development, store the client globally to prevent multiple instances
if (dev) {
	globalThis.__prisma = prisma;
}

// Graceful shutdown
process.on('beforeExit', async () => {
	await prisma.$disconnect();
});

export default prisma;
