// A type for the Leader data model, used in your form and API.
// Using `export type` makes it available for import in other files.
export type Leader = {
	id: string; // Usually a CUID or UUID from the database
	name: string;
	position: string;
	party: string;
	state: string;
	lga?: string | null; // Optional fields are marked with `?`
	bio?: string | null;
	detailedBio?: string | null;
	avatarUrl?: string | null;
	coverImageUrl?: string | null;
	publicSentiment: 'Positive' | 'Neutral' | 'Negative'; // Use literal types for fixed options
	youtubeVideoUrl?: string | null;
	education?: string | null;
	background?: string | null;
	popularityScore: number;
	isVerified: boolean;
	createdAt: Date; // It's good practice to include timestamps
	updatedAt: Date;
};

// You can add other types your application uses here.
// For example, the user object from your auth store:
export type AuthUser = {
	id: string;
	email: string;
	name: string;
	avatarUrl?: string | null;
	isAdmin: boolean;
	onboardingComplete: boolean;
	// ... any other user properties
};

// A type for a Poll
export type Poll = {
	id: string;
	question: string;
	options: { text: string; votes: number }[];
	createdBy: string; // User ID
	createdAt: Date;
};

// And so on for other data models like Petitions, Groups, etc.