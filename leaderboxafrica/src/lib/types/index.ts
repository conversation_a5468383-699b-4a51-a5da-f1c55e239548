// LeaderBox Type Definitions
// Shared types for the application

import type { User, Leader, Poll, Banter, Petition, Group } from '@prisma/client';

// Extended types with relations
export type UserWithRelations = User & {
	leaderFollows?: Array<{
		leader: Leader;
	}>;
	groups?: Array<{
		members: Array<{ user: User }>;
	}>;
};

export type LeaderWithRelations = Leader & {
	ratings?: Array<{
		user: User;
		rating: number;
	}>;
	comments?: Array<{
		user: User;
		text: string;
		votes?: Array<any>;
	}>;
	followers?: Array<{
		user: User;
	}>;
};

export type PollWithRelations = Poll & {
	creator?: User;
	options?: Array<{
		id: string;
		text: string;
		votes: number;
		pollVotes?: Array<any>;
	}>;
	votes?: Array<{
		user: User;
		option: any;
	}>;
	comments?: Array<{
		user: User;
		text: string;
	}>;
};

export type BanterWithRelations = Banter & {
	author: User;
	votes?: Array<{
		user: User;
		voteType: string;
	}>;
	comments?: Array<{
		user: User;
		text: string;
	}>;
};

export type PetitionWithRelations = Petition & {
	creator?: User;
	petitionSignatures?: Array<{
		user: User;
	}>;
};

export type GroupWithRelations = Group & {
	creator?: User;
	members?: Array<{
		user: User;
		role: string;
	}>;
	discussions?: Array<{
		creator?: User;
		title: string;
	}>;
};

// Form types
export interface LoginForm {
	email: string;
	password: string;
}

export interface RegisterForm {
	email: string;
	password: string;
	name: string;
	state?: string;
	lga?: string;
	gender?: string;
	party?: string;
}

export interface LeaderRatingForm {
	leaderId: string;
	rating: number;
	comment?: string;
}

export interface PollCreateForm {
	topic: string;
	description?: string;
	options: string[];
	media?: string[];
}

export interface BanterCreateForm {
	content: string;
	media?: string[];
}

export interface PetitionCreateForm {
	title: string;
	description: string;
	media?: string[];
}

export interface GroupCreateForm {
	name: string;
	description?: string;
	isPublic: boolean;
	tags?: Record<string, any>;
}

// API Response types
export interface ApiResponse<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

export interface PaginatedResponse<T = any> {
	data: T[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

// Authentication types
export interface AuthUser {
	id: string;
	email: string;
	name?: string;
	role: string;
	isAdmin: boolean;
	avatarUrl?: string;
}

export interface Session {
	user: AuthUser;
	accessToken: string;
	refreshToken?: string;
	expiresAt: number;
}

// Store types
export interface AppState {
	user: AuthUser | null;
	loading: boolean;
	error: string | null;
}

export interface NotificationState {
	id: string;
	type: 'success' | 'error' | 'warning' | 'info';
	message: string;
	duration?: number;
}

// Component prop types
export interface ModalProps {
	open: boolean;
	onClose: () => void;
	title?: string;
	size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface ButtonProps {
	variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
	size?: 'sm' | 'md' | 'lg';
	disabled?: boolean;
	loading?: boolean;
}

export interface InputProps {
	type?: string;
	placeholder?: string;
	required?: boolean;
	disabled?: boolean;
	error?: string;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Constants
export const USER_ROLES = {
	USER: 'User',
	ADMIN: 'Admin',
	MODERATOR: 'Moderator',
	CONTENT_CREATOR: 'Content Creator'
} as const;

export const POLL_STATUS = {
	ACTIVE: 'active',
	CLOSED: 'closed',
	DRAFT: 'draft'
} as const;

export const PETITION_STATUS = {
	PENDING: 'pending',
	ACTIVE: 'active',
	SUCCESSFUL: 'successful',
	CLOSED: 'closed'
} as const;

export const GROUP_STATUS = {
	VISIBLE: 'visible',
	PENDING_MEMBERS: 'pending_members',
	PRIVATE: 'private'
} as const;

export const VOTE_TYPES = {
	UPVOTE: 'upvote',
	DOWNVOTE: 'downvote'
} as const;

// Nigerian states and LGAs (sample)
export const NIGERIAN_STATES = [
	'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue',
	'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu',
	'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi',
	'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun',
	'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
] as const;

export const POLITICAL_PARTIES = [
	'APC', 'PDP', 'LP', 'NNPP', 'APGA', 'ADC', 'SDP', 'YPP', 'Neutral'
] as const;

export type NigerianState = typeof NIGERIAN_STATES[number];
export type PoliticalParty = typeof POLITICAL_PARTIES[number];
