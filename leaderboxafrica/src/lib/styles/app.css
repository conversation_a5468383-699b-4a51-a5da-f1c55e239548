/* LeaderBox Application Styles */
/* Base styles and Tailwind CSS imports */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS Variables for shadcn/ui color system .. */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 142.1 70.6% 45.3%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 91.2% 59.8%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 216 34% 17%;
  }

  * {
    @apply border-border;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }
}

/* Custom component styles */
@layer components {
  /* Civic Icons Background */
  .civic-icons-bg {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid hsl(var(--border));
  }

  .civic-icons-bg::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 24 24' fill='none' stroke='%23e5e7eb' stroke-width='0.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16'%3E%3C/path%3E%3Cpath d='M12 20h4'%3E%3C/path%3E%3Cpath d='M12 4H8'%3E%3C/path%3E%3Cpath d='M18 12h-8'%3E%3C/path%3E%3C/svg%3E"),
      url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 24 24' fill='none' stroke='%23e5e7eb' stroke-width='0.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cline x1='10' y1='9' x2='8' y2='9'%3E%3C/line%3E%3C/svg%3E"),
      url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24' fill='none' stroke='%23e5e7eb' stroke-width='0.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: space, space, space;
    background-position: 10% 20%, 50% 50%, 80% 70%;
    background-size: 30px, 40px, 35px;
    opacity: 0.07;
    z-index: -1;
    pointer-events: none;
  }

  /* Gradient Text */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Modern Input */
  .modern-input {
    @apply w-full px-4 py-3 rounded-lg border border-input bg-background placeholder-muted-foreground focus:ring-2 focus:ring-ring focus:border-ring transition-colors duration-200;
  }

  /* Modern Card */
  .modern-card {
    @apply bg-card border border-border rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 ease-in-out;
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-secondary;
  }

  .btn-outline {
    @apply border-input text-foreground bg-background hover:bg-accent hover:text-accent-foreground focus:ring-ring;
  }

  .btn-ghost {
    @apply text-foreground hover:bg-accent hover:text-accent-foreground focus:ring-ring;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:ring-destructive;
  }

  .btn-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent;
  }

  /* Header and Footer Styles - Exact from React */
  .header-override {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid hsl(var(--border));
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .logo-override {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .footer-override {
    background: hsl(var(--muted));
    border-top: 1px solid hsl(var(--border));
    color: hsl(var(--muted-foreground));
  }

  .mobile-nav-override {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid hsl(var(--border));
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* Page Container */
  .page-container {
    @apply container mx-auto px-4 py-6 md:py-8;
  }

  /* Hero Section - Exact from React */
  .civic-icons-bg {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    color: white;
    border: none;
  }

  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .civic-icons-bg .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent))) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
  }

  /* Admin Panel Styles */
  .admin-sidebar {
    @apply fixed left-0 top-0 h-full z-40 md:relative md:z-auto;
  }

  .admin-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 text-gray-600 hover:text-gray-900 hover:bg-gray-100;
  }

  .admin-nav-item.active {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground;
  }

  .admin-nav-item.collapsed {
    @apply justify-center px-2;
  }

  /* Dashboard Styles */
  .modern-card {
    @apply bg-card border border-border shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .dashboard-tab-trigger {
    @apply flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 text-muted-foreground hover:text-foreground hover:bg-background/50;
  }

  .dashboard-tab-trigger.active {
    @apply bg-background text-primary shadow-md;
  }

  /* Custom Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  ::-webkit-scrollbar-corner {
    background: hsl(var(--muted));
  }

  /* Firefox scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
  }

  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }
  
  .input-error {
    @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .badge-error {
    @apply bg-red-100 text-red-800;
  }
  
  /* Avatar styles */
  .avatar {
    @apply inline-flex items-center justify-center rounded-full bg-gray-300 text-gray-700 font-medium;
  }
  
  .avatar-sm {
    @apply w-8 h-8 text-sm;
  }
  
  .avatar-md {
    @apply w-10 h-10 text-base;
  }
  
  .avatar-lg {
    @apply w-12 h-12 text-lg;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
  
  /* Form group */
  .form-group {
    @apply space-y-1;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }
  
  .form-error {
    @apply text-sm text-red-600;
  }
  
  .form-help {
    @apply text-sm text-gray-500;
  }
}

/* Custom utility classes */
@layer utilities {
  /* Text truncation */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Custom animations */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Responsive text sizes */
.text-responsive {
  @apply text-sm sm:text-base lg:text-lg;
}

/* Custom shadows */
.shadow-card {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-card-hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}
