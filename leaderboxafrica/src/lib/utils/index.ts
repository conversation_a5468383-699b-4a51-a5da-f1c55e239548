// LeaderBox Utility Functions
// Common utility functions used throughout the application

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Tailwind CSS class merging utility
export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// Date formatting utilities
export function formatDate(date: Date | string): string {
	const d = new Date(date);
	return d.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: 'numeric'
	});
}

export function formatDateTime(date: Date | string): string {
	const d = new Date(date);
	return d.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
		hour: '2-digit',
		minute: '2-digit'
	});
}

export function formatRelativeTime(date: Date | string): string {
	const d = new Date(date);
	const now = new Date();
	const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);

	if (diffInSeconds < 60) return 'just now';
	if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
	if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
	if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
	
	return formatDate(d);
}

// String utilities
export function truncateText(text: string, maxLength: number): string {
	if (text.length <= maxLength) return text;
	return text.slice(0, maxLength) + '...';
}

export function slugify(text: string): string {
	return text
		.toLowerCase()
		.replace(/[^\w\s-]/g, '')
		.replace(/[\s_-]+/g, '-')
		.replace(/^-+|-+$/g, '');
}

export function capitalizeFirst(text: string): string {
	return text.charAt(0).toUpperCase() + text.slice(1);
}

export function capitalizeWords(text: string): string {
	return text.replace(/\b\w/g, (char) => char.toUpperCase());
}

// Number utilities
export function formatNumber(num: number): string {
	if (num >= 1000000) {
		return (num / 1000000).toFixed(1) + 'M';
	}
	if (num >= 1000) {
		return (num / 1000).toFixed(1) + 'K';
	}
	return num.toString();
}

export function formatPercentage(value: number, total: number): string {
	if (total === 0) return '0%';
	return Math.round((value / total) * 100) + '%';
}

export function calculateRatingAverage(ratings: number[]): number {
	if (ratings.length === 0) return 0;
	const sum = ratings.reduce((acc, rating) => acc + rating, 0);
	return Math.round((sum / ratings.length) * 10) / 10;
}

// Validation utilities
export function isValidEmail(email: string): boolean {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email);
}

export function isValidPassword(password: string): boolean {
	// At least 8 characters, 1 uppercase, 1 lowercase, 1 number
	const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
	return passwordRegex.test(password);
}

export function isValidPhoneNumber(phone: string): boolean {
	// Nigerian phone number format
	const phoneRegex = /^(\+234|0)[789][01]\d{8}$/;
	return phoneRegex.test(phone.replace(/\s/g, ''));
}

// Array utilities
export function removeDuplicates<T>(array: T[], key?: keyof T): T[] {
	if (!key) {
		return [...new Set(array)];
	}
	const seen = new Set();
	return array.filter(item => {
		const value = item[key];
		if (seen.has(value)) {
			return false;
		}
		seen.add(value);
		return true;
	});
}

export function sortByKey<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {
	return [...array].sort((a, b) => {
		const aVal = a[key];
		const bVal = b[key];
		
		if (aVal < bVal) return direction === 'asc' ? -1 : 1;
		if (aVal > bVal) return direction === 'asc' ? 1 : -1;
		return 0;
	});
}

export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
	return array.reduce((groups, item) => {
		const groupKey = String(item[key]);
		if (!groups[groupKey]) {
			groups[groupKey] = [];
		}
		groups[groupKey].push(item);
		return groups;
	}, {} as Record<string, T[]>);
}

// Object utilities
export function omit<T extends Record<string, any>, K extends keyof T>(
	obj: T,
	keys: K[]
): Omit<T, K> {
	const result = { ...obj };
	keys.forEach(key => delete result[key]);
	return result;
}

export function pick<T extends Record<string, any>, K extends keyof T>(
	obj: T,
	keys: K[]
): Pick<T, K> {
	const result = {} as Pick<T, K>;
	keys.forEach(key => {
		if (key in obj) {
			result[key] = obj[key];
		}
	});
	return result;
}

// URL utilities
export function buildUrl(base: string, params: Record<string, string | number | boolean>): string {
	const url = new URL(base, window.location.origin);
	Object.entries(params).forEach(([key, value]) => {
		url.searchParams.set(key, String(value));
	});
	return url.toString();
}

export function getQueryParam(name: string): string | null {
	if (typeof window === 'undefined') return null;
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(name);
}

// Local storage utilities
export function setLocalStorage(key: string, value: any): void {
	if (typeof window === 'undefined') return;
	try {
		localStorage.setItem(key, JSON.stringify(value));
	} catch (error) {
		console.error('Failed to set localStorage:', error);
	}
}

export function getLocalStorage<T>(key: string, defaultValue: T): T {
	if (typeof window === 'undefined') return defaultValue;
	try {
		const item = localStorage.getItem(key);
		return item ? JSON.parse(item) : defaultValue;
	} catch (error) {
		console.error('Failed to get localStorage:', error);
		return defaultValue;
	}
}

export function removeLocalStorage(key: string): void {
	if (typeof window === 'undefined') return;
	try {
		localStorage.removeItem(key);
	} catch (error) {
		console.error('Failed to remove localStorage:', error);
	}
}

// Debounce utility
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number
): (...args: Parameters<T>) => void {
	let timeout: NodeJS.Timeout;
	return (...args: Parameters<T>) => {
		clearTimeout(timeout);
		timeout = setTimeout(() => func(...args), wait);
	};
}

// Throttle utility
export function throttle<T extends (...args: any[]) => any>(
	func: T,
	limit: number
): (...args: Parameters<T>) => void {
	let inThrottle: boolean;
	return (...args: Parameters<T>) => {
		if (!inThrottle) {
			func(...args);
			inThrottle = true;
			setTimeout(() => inThrottle = false, limit);
		}
	};
}

// Error handling utilities
export function getErrorMessage(error: unknown): string {
	if (error instanceof Error) return error.message;
	if (typeof error === 'string') return error;
	return 'An unknown error occurred';
}

export function isNetworkError(error: unknown): boolean {
	return error instanceof Error && 
		(error.message.includes('fetch') || 
		 error.message.includes('network') ||
		 error.message.includes('Failed to fetch'));
}

// File utilities
export function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 Bytes';
	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function getFileExtension(filename: string): string {
	return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

// Color utilities
export function generateAvatarColor(name: string): string {
	const colors = [
		'#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
		'#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
	];
	const index = name.charCodeAt(0) % colors.length;
	return colors[index];
}

export function getInitials(name: string): string {
	return name
		.split(' ')
		.map(word => word.charAt(0))
		.join('')
		.toUpperCase()
		.slice(0, 2);
}
