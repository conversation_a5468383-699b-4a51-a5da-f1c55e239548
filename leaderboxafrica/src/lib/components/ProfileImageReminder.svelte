<!-- Profile Image Reminder Component -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/auth.js';
	import Card from './ui/Card.svelte';
	import CardContent from './ui/CardContent.svelte';
	import Button from './ui/Button.svelte';

	export let showReminder = true;

	$: user = $authStore.user;
	
	// Check if user needs profile image reminder
	$: needsProfileImage = user && (!user.avatarUrl || user.avatarUrl === '');
	
	// Local storage key for dismissed reminders
	const REMINDER_DISMISSED_KEY = 'profile-image-reminder-dismissed';
	
	let isDismissed = false;

	onMount(() => {
		// Check if reminder was previously dismissed
		const dismissed = localStorage.getItem(REMINDER_DISMISSED_KEY);
		isDismissed = dismissed === 'true';
	});

	function dismissReminder() {
		isDismissed = true;
		localStorage.setItem(REMINDER_DISMISSED_KEY, 'true');
	}

	function goToProfile() {
		goto('/settings'); // Will redirect to settings page when implemented
	}

	// Show reminder if user needs profile image, hasn't dismissed it, and showReminder prop is true
	$: shouldShowReminder = showReminder && needsProfileImage && !isDismissed;
</script>

{#if shouldShowReminder}
	<Card className="border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800 mb-4">
		<CardContent className="p-4">
			<div class="flex items-start space-x-3">
				<div class="flex-shrink-0">
					<div class="w-10 h-10 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center">
						<svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
						</svg>
					</div>
				</div>
				<div class="flex-1 min-w-0">
					<h3 class="text-sm font-semibold text-orange-800 dark:text-orange-200">
						Complete Your Profile
					</h3>
					<p class="text-sm text-orange-700 dark:text-orange-300 mt-1">
						Add a profile picture to help other citizens and leaders recognize you. A complete profile builds trust in the political community.
					</p>
					<div class="flex items-center space-x-3 mt-3">
						<Button 
							size="sm" 
							on:click={goToProfile}
							className="bg-orange-600 hover:bg-orange-700 text-white"
						>
							<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
							</svg>
							Add Photo
						</Button>
						<Button 
							variant="ghost" 
							size="sm" 
							on:click={dismissReminder}
							className="text-orange-700 hover:text-orange-800 dark:text-orange-300 dark:hover:text-orange-200"
						>
							Maybe Later
						</Button>
					</div>
				</div>
				<button
					on:click={dismissReminder}
					class="flex-shrink-0 text-orange-400 hover:text-orange-600 dark:text-orange-500 dark:hover:text-orange-300"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>
	</CardContent>
</Card>
{/if}
