<!-- Dashboard Activity Tab - Exact replica of React DashboardActivityTab.jsx -->
<script lang="ts">
	import { authStore } from '$lib/stores/auth.ts';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';

	export let activity: Array<any> = [];
	export let type: string = '';
	export let linkPrefix: string = '';
	export let emptyMessage: string = '';
	export let ctaLink: string = '';
	export let ctaText: string = '';

	$: user = $authStore.user;

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString();
	}

	function getActivityDate(item: any): string {
		if (item.comments?.length > 0) {
			return formatDate(item.comments[item.comments.length - 1].timestamp);
		}
		return formatDate(item.createdAt || item.timestamp);
	}

	function getActivityText(item: any): string {
		if (item.createdBy === (user?.name || user?.email.split('@')[0]) && type === 'Petition') {
			return 'Created by you';
		}
		if (type === 'Petition') {
			return 'You signed';
		}
		return 'Last activity';
	}
</script>

<Card className="modern-card">
	<CardHeader>
		<h3 class="text-xl text-primary">Your {type} Activity</h3>
	</CardHeader>
	<CardContent className="space-y-3">
		{#if activity.length > 0}
			{#each activity as item}
				<a 
					href="{linkPrefix}/{item.id}" 
					class="block p-3 hover:bg-secondary/50 rounded-md transition-colors border border-transparent hover:border-border"
				>
					<p class="font-semibold text-foreground">{item.title || item.topic}</p>
					<p class="text-xs text-muted-foreground">
						{getActivityText(item)}: {getActivityDate(item)}
					</p>
				</a>
			{/each}
		{:else}
			<p class="text-muted-foreground p-4 text-center">
				{emptyMessage} 
				<a href={ctaLink} class="text-primary hover:underline">{ctaText}</a>
			</p>
		{/if}
	</CardContent>
</Card>
