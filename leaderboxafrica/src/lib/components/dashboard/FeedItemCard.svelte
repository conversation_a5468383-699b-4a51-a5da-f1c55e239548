<!-- Feed Item Card - Exact replica of React FeedItemCard.jsx -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import Button from '$lib/components/ui/Button.svelte';

	interface Props {
		item: any;
	}

	let { item }: Props = $props();

	let content = '';
	let icon = '';
	let actionText = 'View';
	let linkTo = '/';

	// Process item based on type using Svelte 5 effect
	$effect(() => {
		switch (item.type) {
			case 'banter_mention':
				icon = 'message-square';
				content = `You were mentioned in a banter: ${item.data.title}`;
				actionText = 'View Banter';
				linkTo = `/banter/${item.data.id}`;
				break;
			case 'poll_voted':
				icon = 'bar-chart-2';
				content = `You voted on the poll: ${item.data.topic}`;
				actionText = 'View Poll';
				linkTo = `/poll/${item.data.id}`;
				break;
			case 'leader_followed_update':
				icon = 'user-check';
				content = `Update from a leader you follow: ${item.data.name} (${item.data.update || "shared an update"})`;
				actionText = 'View Profile';
				linkTo = `/leaders/${item.data.id}`;
				break;
			case 'new_petition_location':
				icon = 'file-text';
				content = `New petition relevant to your location (${item.data.location || 'Nigeria'}): ${item.data.title}`;
				actionText = 'View Petition';
				linkTo = `/petition/${item.data.id}`;
				break;
			default:
				icon = 'message-square';
				content = `Generic feed item: ${item.data.title || item.data.text}`;
		}
	});

	function getIconSvg(iconName: string) {
		const icons = {
			'message-square': { color: 'text-blue-500', path: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z' },
			'bar-chart-2': { color: 'text-green-500', path: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
			'user-check': { color: 'text-purple-500', path: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' },
			'file-text': { color: 'text-orange-500', path: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' }
		};
		return icons[iconName] || icons['message-square'];
	}

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString();
	}

	function handleClick() {
		goto(linkTo);
	}

	let iconData = $derived(getIconSvg(icon));
</script>

<Card className="modern-card hover:shadow-lg transition-shadow duration-200">
	<CardContent className="p-4 flex items-start space-x-3">
		<div class="flex-shrink-0 pt-1">
			<svg class="w-5 h-5 {iconData.color}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={iconData.path} />
			</svg>
		</div>
		<div class="flex-grow">
			{#if item.type === 'banter_mention'}
				<p class="text-sm text-muted-foreground">You were mentioned in a banter:</p>
				<p class="font-semibold text-foreground">{item.data.title}</p>
			{:else if item.type === 'poll_voted'}
				<p class="text-sm text-muted-foreground">You voted on the poll:</p>
				<p class="font-semibold text-foreground">{item.data.topic}</p>
			{:else if item.type === 'leader_followed_update'}
				<p class="text-sm text-muted-foreground">Update from a leader you follow:</p>
				<p class="font-semibold text-foreground">
					{item.data.name} 
					<span class="text-xs text-muted-foreground">({item.data.update || "shared an update"})</span>
				</p>
			{:else if item.type === 'new_petition_location'}
				<p class="text-sm text-muted-foreground">New petition relevant to your location ({item.data.location || 'Nigeria'}):</p>
				<p class="font-semibold text-foreground">{item.data.title}</p>
			{:else}
				<p class="text-sm text-foreground">Generic feed item: {item.data.title || item.data.text}</p>
			{/if}
			<p class="text-xs text-muted-foreground mt-1">{formatDate(item.timestamp)}</p>
		</div>
		<Button 
			variant="ghost" 
			size="sm" 
			className="self-center text-primary hover:bg-primary/10" 
			on:click={handleClick}
		>
			{actionText}
		</Button>
	</CardContent>
</Card>
