<!-- Create Poll Dialog - Exact replica of React CreatePollDialog.jsx -->
<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';

	export let isOpen = false;
	export let onCreatePoll;

	const dispatch = createEventDispatcher();

	// Form state
	let topic = '';
	let description = '';
	let options = ['', ''];
	let taggedLeader = '';
	let taggedLocation = '';
	let generalTags = '';
	let errors = {};

	// Constants
	const MAX_OPTIONS = 6;
	const MIN_OPTIONS = 2;

	// Functions
	function addOption() {
		if (options.length < MAX_OPTIONS) {
			options = [...options, ''];
		}
	}

	function removeOption(index) {
		if (options.length > MIN_OPTIONS) {
			options = options.filter((_, i) => i !== index);
		}
	}

	function updateOption(index, value) {
		options[index] = value;
		options = [...options]; // Trigger reactivity
	}

	function validateForm() {
		errors = {};

		if (!topic.trim()) {
			errors.topic = 'Poll topic is required';
		}

		if (!description.trim()) {
			errors.description = 'Poll description is required';
		}

		const validOptions = options.filter(opt => opt.trim() !== '');
		if (validOptions.length < MIN_OPTIONS) {
			errors.options = `At least ${MIN_OPTIONS} options are required`;
		}

		// Check for duplicate options
		const uniqueOptions = new Set(validOptions.map(opt => opt.trim().toLowerCase()));
		if (uniqueOptions.size !== validOptions.length) {
			errors.options = 'Options must be unique';
		}

		return Object.keys(errors).length === 0;
	}

	function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		const validOptions = options.filter(opt => opt.trim() !== '');
		const tagsArray = generalTags.split(',').map(tag => tag.trim()).filter(tag => tag !== '');

		const pollData = {
			topic: topic.trim(),
			description: description.trim(),
			options: validOptions,
			taggedLeader: taggedLeader.trim() || undefined,
			taggedLocation: taggedLocation.trim() || undefined,
			generalTags: tagsArray.length > 0 ? tagsArray : undefined
		};

		onCreatePoll(pollData);
		resetForm();
		closeDialog();
	}

	function resetForm() {
		topic = '';
		description = '';
		options = ['', ''];
		taggedLeader = '';
		taggedLocation = '';
		generalTags = '';
		errors = {};
	}

	function closeDialog() {
		isOpen = false;
		resetForm();
	}

	// Handle escape key
	function handleKeydown(event) {
		if (event.key === 'Escape') {
			closeDialog();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-card border border-border rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
			<!-- Header -->
			<div class="flex items-center justify-between p-6 border-b border-border">
				<div>
					<h2 class="text-xl font-semibold text-primary">Create New Poll</h2>
					<p class="text-sm text-muted-foreground mt-1">Ask the community what they think about important issues</p>
				</div>
				<button 
					on:click={closeDialog}
					class="text-muted-foreground hover:text-foreground transition-colors"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>

			<!-- Form -->
			<form on:submit|preventDefault={handleSubmit} class="p-6 space-y-6">
				<!-- Topic -->
				<div>
					<label for="poll-topic" class="block text-sm font-medium text-foreground mb-2">
						Poll Topic *
					</label>
					<Input
						id="poll-topic"
						bind:value={topic}
						placeholder="e.g., Should Nigeria invest more in renewable energy?"
						className={errors.topic ? 'border-red-500' : ''}
					/>
					{#if errors.topic}
						<p class="text-red-500 text-sm mt-1">{errors.topic}</p>
					{/if}
				</div>

				<!-- Description -->
				<div>
					<label for="poll-description" class="block text-sm font-medium text-foreground mb-2">
						Description *
					</label>
					<textarea
						id="poll-description"
						bind:value={description}
						placeholder="Provide more context about this poll..."
						rows="3"
						class="w-full px-3 py-2 border border-border rounded-md focus:ring-primary focus:border-primary {errors.description ? 'border-red-500' : ''}"
					></textarea>
					{#if errors.description}
						<p class="text-red-500 text-sm mt-1">{errors.description}</p>
					{/if}
				</div>

				<!-- Options -->
				<div>
					<div class="flex items-center justify-between mb-2">
						<label class="block text-sm font-medium text-foreground">
							Poll Options * (minimum {MIN_OPTIONS})
						</label>
						<Button 
							type="button" 
							variant="outline" 
							size="sm" 
							on:click={addOption}
							disabled={options.length >= MAX_OPTIONS}
						>
							<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
							</svg>
							Add Option
						</Button>
					</div>
					<div class="space-y-2">
						{#each options as option, index}
							<div class="flex items-center space-x-2">
								<Input
									value={option}
									on:input={(e) => updateOption(index, e.target.value)}
									placeholder={`Option ${index + 1}`}
									className="flex-1"
								/>
								{#if options.length > MIN_OPTIONS}
									<Button 
										type="button" 
										variant="ghost" 
										size="sm" 
										on:click={() => removeOption(index)}
										className="text-red-600 hover:text-red-700"
									>
										<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
										</svg>
									</Button>
								{/if}
							</div>
						{/each}
					</div>
					{#if errors.options}
						<p class="text-red-500 text-sm mt-1">{errors.options}</p>
					{/if}
				</div>

				<!-- Tags and Targeting -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label for="tagged-leader" class="block text-sm font-medium text-foreground mb-2">
							Tagged Leader (Optional)
						</label>
						<Input
							id="tagged-leader"
							bind:value={taggedLeader}
							placeholder="e.g., Bola Ahmed Tinubu"
						/>
					</div>
					<div>
						<label for="tagged-location" class="block text-sm font-medium text-foreground mb-2">
							Location (Optional)
						</label>
						<Input
							id="tagged-location"
							bind:value={taggedLocation}
							placeholder="e.g., Lagos, Nigeria"
						/>
					</div>
				</div>

				<!-- General Tags -->
				<div>
					<label for="general-tags" class="block text-sm font-medium text-foreground mb-2">
						Tags (Optional)
					</label>
					<Input
						id="general-tags"
						bind:value={generalTags}
						placeholder="e.g., energy, environment, economy (comma-separated)"
					/>
					<p class="text-xs text-muted-foreground mt-1">
						Separate multiple tags with commas
					</p>
				</div>

				<!-- Actions -->
				<div class="flex items-center justify-end space-x-3 pt-4 border-t border-border">
					<Button type="button" variant="outline" on:click={closeDialog}>
						Cancel
					</Button>
					<Button type="submit" className="bg-primary text-primary-foreground hover:bg-primary/90">
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
						Create Poll
					</Button>
				</div>
			</form>
		</div>
	</div>
{/if}
