<!-- Poll Card Component - Exact replica of React PollCard.jsx -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardFooter from '$lib/components/ui/CardFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';

	interface Props {
		poll: any;
		user: any;
		onVoteClick: (poll: any, optionId: string) => void;
	}

	let { poll, user, onVoteClick }: Props = $props();

	// Computed values using Svelte 5 runes
	let userHasVoted = $derived(user && poll.userVotes && poll.userVotes[user.id]);
	let userVotedOption = $derived(userHasVoted ? poll.userVotes[user.id] : null);

	// Functions
	function handleVote(optionId) {
		if (!user) {
			goto('/login');
			return;
		}
		onVoteClick(poll, optionId);
	}

	function getPercentage(votes, total) {
		if (total === 0) return 0;
		return Math.round((votes / total) * 100);
	}

	function formatDate(dateString) {
		return new Date(dateString).toLocaleDateString();
	}

	function formatTimeAgo(dateString) {
		const date = new Date(dateString);
		const now = new Date();
		const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
		
		if (diffInHours < 1) {
			return 'Just now';
		} else if (diffInHours < 24) {
			return `${diffInHours}h ago`;
		} else {
			const diffInDays = Math.floor(diffInHours / 24);
			return `${diffInDays}d ago`;
		}
	}

	function getOptionColor(optionId) {
		if (!userHasVoted) return 'bg-secondary';
		if (userVotedOption === optionId) return 'bg-primary';
		return 'bg-muted';
	}

	function getOptionTextColor(optionId) {
		if (!userHasVoted) return 'text-foreground';
		if (userVotedOption === optionId) return 'text-primary-foreground';
		return 'text-muted-foreground';
	}
</script>

<Card className="modern-card hover:shadow-lg transition-all duration-200 cursor-pointer group" on:click={() => goto(`/poll/${poll.id}`)}>
	<CardContent className="p-6 space-y-4">
		<!-- Header -->
		<div class="flex items-start justify-between">
			<div class="flex-1">
				<h3 class="text-lg font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
					{poll.topic}
				</h3>
				<div class="flex items-center space-x-2 mt-2 text-sm text-muted-foreground">
					<span>by {poll.source}</span>
					<span>•</span>
					<span>{formatTimeAgo(poll.createdAt)}</span>
				</div>
			</div>
		</div>

		<!-- Description -->
		{#if poll.description}
			<p class="text-sm text-muted-foreground line-clamp-3">
				{poll.description}
			</p>
		{/if}

		<!-- Tags -->
		{#if poll.generalTags && poll.generalTags.length > 0}
			<div class="flex flex-wrap gap-1">
				{#each poll.generalTags.slice(0, 3) as tag}
					<Badge variant="secondary" className="text-xs">
						{tag}
					</Badge>
				{/each}
				{#if poll.generalTags.length > 3}
					<Badge variant="outline" className="text-xs">
						+{poll.generalTags.length - 3} more
					</Badge>
				{/if}
			</div>
		{/if}

		<!-- Tagged Leader/Location -->
		{#if poll.taggedLeader || poll.taggedLocation}
			<div class="flex items-center space-x-2 text-xs text-muted-foreground">
				{#if poll.taggedLeader}
					<div class="flex items-center">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
						</svg>
						{poll.taggedLeader}
					</div>
				{/if}
				{#if poll.taggedLocation}
					<div class="flex items-center">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
						</svg>
						{poll.taggedLocation}
					</div>
				{/if}
			</div>
		{/if}

		<!-- Poll Options -->
		<div class="space-y-2">
			{#each poll.options as option}
				<div class="relative">
					<button
						class="w-full text-left p-3 rounded-lg border border-border hover:border-primary/50 transition-all duration-200 relative overflow-hidden {getOptionTextColor(option.id)}"
						on:click|stopPropagation={() => handleVote(option.id)}
						disabled={userHasVoted}
					>
						<!-- Progress bar background -->
						<div 
							class="absolute inset-0 {getOptionColor(option.id)} opacity-20 transition-all duration-300"
							style="width: {getPercentage(option.votes, poll.totalVotes)}%"
						></div>
						
						<!-- Option content -->
						<div class="relative flex items-center justify-between">
							<span class="font-medium">{option.text}</span>
							<div class="flex items-center space-x-2">
								<span class="text-sm font-semibold">
									{getPercentage(option.votes, poll.totalVotes)}%
								</span>
								<span class="text-xs text-muted-foreground">
									({option.votes} votes)
								</span>
								{#if userVotedOption === option.id}
									<svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
									</svg>
								{/if}
							</div>
						</div>
					</button>
				</div>
			{/each}
		</div>

		<!-- Vote Status -->
		{#if userHasVoted}
			<div class="text-center py-2">
				<Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
					<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
					</svg>
					You voted
				</Badge>
			</div>
		{:else if user}
			<div class="text-center py-2">
				<p class="text-sm text-muted-foreground">Click an option to vote</p>
			</div>
		{:else}
			<div class="text-center py-2">
				<Button variant="outline" size="sm" on:click={() => goto('/login')}>
					Login to Vote
				</Button>
			</div>
		{/if}
	</CardContent>

	<CardFooter className="px-6 py-4 border-t border-border bg-secondary/30">
		<div class="flex items-center justify-between w-full text-sm text-muted-foreground">
			<div class="flex items-center space-x-4">
				<div class="flex items-center">
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
					</svg>
					<span>{poll.totalVotes} votes</span>
				</div>
				<div class="flex items-center">
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
					</svg>
					<span>{poll.commentsCount || 0} comments</span>
				</div>
			</div>
			<div class="flex items-center space-x-2">
				<button class="flex items-center hover:text-primary transition-colors" on:click|stopPropagation={() => {}}>
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
					</svg>
					<span>{poll.upvotes || 0}</span>
				</button>
				<button class="flex items-center hover:text-primary transition-colors" on:click|stopPropagation={() => goto(`/poll/${poll.id}`)}>
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
					</svg>
					View Details
				</button>
			</div>
		</div>
	</CardFooter>
</Card>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.modern-card {
		@apply bg-card border border-border shadow-sm hover:shadow-md transition-shadow duration-200;
	}
</style>
