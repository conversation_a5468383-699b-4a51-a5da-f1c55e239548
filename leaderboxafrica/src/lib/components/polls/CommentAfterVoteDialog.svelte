<!-- Comment After Vote Dialog - Exact replica of React CommentAfterVoteDialog.jsx -->
<script lang="ts">
	import Button from '$lib/components/ui/Button.svelte';

	interface Props {
		isOpen?: boolean;
		pollTopic?: string;
		onSubmitComment: (comment: string) => void;
		onClose: () => void;
	}

	let { isOpen = $bindable(false), pollTopic = '', onSubmitComment, onClose }: Props = $props();

	let comment = '';

	function handleSubmit() {
		onSubmitComment(comment.trim());
		comment = '';
	}

	function handleSkip() {
		onSubmitComment('');
		comment = '';
	}

	function handleClose() {
		comment = '';
		onClose();
	}

	// Handle escape key
	function handleKeydown(event) {
		if (event.key === 'Escape') {
			handleClose();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-card border border-border rounded-lg shadow-lg max-w-md w-full">
			<!-- Header -->
			<div class="p-6 border-b border-border text-center">
				<div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
					<svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
					</svg>
				</div>
				<h2 class="text-xl font-semibold text-primary mb-2">Vote Submitted!</h2>
				<p class="text-sm text-muted-foreground">
					Thank you for participating in this poll. Would you like to share your thoughts?
				</p>
			</div>

			<!-- Form -->
			<form on:submit|preventDefault={handleSubmit} class="p-6 space-y-4">
				<!-- Poll Topic -->
				<div class="bg-secondary/30 rounded-lg p-3">
					<p class="text-sm font-medium text-foreground line-clamp-2">
						{pollTopic}
					</p>
				</div>

				<!-- Comment Input -->
				<div>
					<label for="vote-comment" class="block text-sm font-medium text-foreground mb-2">
						Share your thoughts (optional)
					</label>
					<textarea
						id="vote-comment"
						bind:value={comment}
						placeholder="Why did you choose this option? Share your perspective..."
						rows="4"
						class="w-full px-3 py-2 border border-border rounded-md focus:ring-primary focus:border-primary resize-none"
					></textarea>
					<p class="text-xs text-muted-foreground mt-1">
						Your comment will be visible to other users
					</p>
				</div>

				<!-- Actions -->
				<div class="flex items-center justify-between space-x-3">
					<Button type="button" variant="ghost" on:click={handleSkip}>
						Skip
					</Button>
					<div class="flex space-x-2">
						<Button type="button" variant="outline" on:click={handleClose}>
							Cancel
						</Button>
						<Button type="submit" disabled={!comment.trim()}>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
							</svg>
							Post Comment
						</Button>
					</div>
				</div>
			</form>
		</div>
	</div>
{/if}

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
