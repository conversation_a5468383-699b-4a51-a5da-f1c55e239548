<!-- Admin Leader Form with Loading States -->
<script lang="ts">
	import { toast } from '$lib/stores/toast.js';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';

	interface Props {
		leader?: any; // null for create, object for edit
		isOpen?: boolean;
		onClose: () => void;
		onSave?: (leader: any) => void;
	}

	let { leader = null, isOpen = $bindable(false), onClose, onSave }: Props = $props();

	// Form state
	let formData = $state({
		name: '',
		position: '',
		party: '',
		state: '',
		lga: '',
		bio: '',
		detailedBio: '',
		avatarUrl: '',
		coverImageUrl: '',
		publicSentiment: 'Neutral',
		youtubeVideoUrl: '',
		education: '',
		background: '',
		popularityScore: 75,
		isVerified: false
	});

	// Loading states
	let isSubmitting = $state(false);
	let isLoading = $state(false);

	// Reactive variables using Svelte 5 runes
	let isEditing = $derived(!!leader);
	let modalTitle = $derived(isEditing ? 'Edit Leader Profile' : 'Create New Leader');
	let submitButtonText = $derived(isEditing ? 'Save Changes' : 'Create Leader');

	// Initialize form data when leader changes
	$effect(() => {
		if (leader && isOpen) {
		formData = {
			name: leader.name || '',
			position: leader.position || '',
			party: leader.party || '',
			state: leader.state || '',
			lga: leader.lga || '',
			bio: leader.bio || '',
			detailedBio: leader.detailedBio || '',
			avatarUrl: leader.avatarUrl || '',
			coverImageUrl: leader.coverImageUrl || '',
			publicSentiment: leader.publicSentiment || 'Neutral',
			youtubeVideoUrl: leader.youtubeVideoUrl || '',
			education: leader.education || '',
			background: leader.background || '',
			popularityScore: leader.popularityScore || 75,
			isVerified: leader.isVerified || false
		};
	} else if (!leader && isOpen) {
		// Reset form for create
		formData = {
			name: '',
			position: '',
			party: '',
			state: '',
			lga: '',
			bio: '',
			detailedBio: '',
			avatarUrl: '',
			coverImageUrl: '',
			publicSentiment: 'Neutral',
			youtubeVideoUrl: '',
			education: '',
			background: '',
			popularityScore: 75,
			isVerified: false
		};
	}
});

	// Validation
	function validateForm() {
		if (!formData.name.trim()) {
			toast.error('Leader name is required');
			return false;
		}
		if (!formData.position.trim()) {
			toast.error('Position is required');
			return false;
		}
		if (!formData.party.trim()) {
			toast.error('Party is required');
			return false;
		}
		if (!formData.state.trim()) {
			toast.error('State is required');
			return false;
		}
		
		// Validate popularityScore
		const score = parseInt(formData.popularityScore);
		if (isNaN(score) || score < 0 || score > 100) {
			toast.error('Popularity score must be a number between 0 and 100');
			return false;
		}
		formData.popularityScore = score; // Ensure it's an integer

		return true;
	}

	// Submit handler
	async function handleSubmit() {
		if (!validateForm()) return;

		isSubmitting = true;

		try {
			const url = isEditing ? `/api/admin/leaders/${leader.id}` : '/api/admin/leaders';
			const method = isEditing ? 'PUT' : 'POST';

			const response = await fetch(url, {
				method,
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Failed to save leader');
			}

			const result = await response.json();
			
			toast.success(isEditing ? 'Leader updated successfully!' : 'Leader created successfully!');
			onSave?.(result.leader);
			onClose();

		} catch (error) {
			console.error('Error saving leader:', error);
			toast.error(error.message || 'Failed to save leader');
		} finally {
			isSubmitting = false;
		}
	}

	// Handle close
	function handleClose() {
		if (isSubmitting) return; // Prevent closing while submitting
		onClose();
	}

	// Nigerian states for dropdown
	const nigerianStates = [
		'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',
		'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo',
		'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa',
		'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba',
		'Yobe', 'Zamfara'
	];

	// Political parties
	const politicalParties = ['APC', 'PDP', 'LP', 'NNPP', 'APGA', 'ADC', 'SDP', 'YPP', 'Other'];

	// Public sentiment options
	const sentimentOptions = ['Positive', 'Neutral', 'Negative'];
</script>

{#if isOpen}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
			<Card className="border-0 shadow-none">
				<CardHeader className="border-b">
					<div class="flex items-center justify-between">
						<h2 class="text-xl font-semibold text-gray-900">{modalTitle}</h2>
						<button 
							on:click={handleClose}
							disabled={isSubmitting}
							class="text-gray-400 hover:text-gray-600 disabled:opacity-50"
						>
							<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
							</svg>
						</button>
					</div>
				</CardHeader>
				
				<CardContent className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
					<form on:submit|preventDefault={handleSubmit} class="space-y-6">
						<!-- Basic Information -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label for="name" class="block text-sm font-medium text-gray-700 mb-1">
									Name <span class="text-red-500">*</span>
								</label>
								<input
									id="name"
									type="text"
									bind:value={formData.name}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									placeholder="Full name"
									required
								/>
							</div>
							
							<div>
								<label for="position" class="block text-sm font-medium text-gray-700 mb-1">
									Position <span class="text-red-500">*</span>
								</label>
								<input
									id="position"
									type="text"
									bind:value={formData.position}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									placeholder="e.g., President, Governor, Senator"
									required
								/>
							</div>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label for="party" class="block text-sm font-medium text-gray-700 mb-1">
									Party <span class="text-red-500">*</span>
								</label>
								<select
									id="party"
									bind:value={formData.party}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									required
								>
									<option value="">Select Party</option>
									{#each politicalParties as party}
										<option value={party}>{party}</option>
									{/each}
								</select>
							</div>
							
							<div>
								<label for="state" class="block text-sm font-medium text-gray-700 mb-1">
									State <span class="text-red-500">*</span>
								</label>
								<select
									id="state"
									bind:value={formData.state}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									required
								>
									<option value="">Select State</option>
									{#each nigerianStates as state}
										<option value={state}>{state}</option>
									{/each}
								</select>
							</div>
						</div>

						<!-- Additional fields -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label for="lga" class="block text-sm font-medium text-gray-700 mb-1">LGA</label>
								<input
									id="lga"
									type="text"
									bind:value={formData.lga}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									placeholder="Local Government Area"
								/>
							</div>
							
							<div>
								<label for="popularityScore" class="block text-sm font-medium text-gray-700 mb-1">
									Popularity Score (0-100)
								</label>
								<input
									id="popularityScore"
									type="number"
									min="0"
									max="100"
									bind:value={formData.popularityScore}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
								/>
							</div>
						</div>

						<!-- Bio -->
						<div>
							<label for="bio" class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
							<textarea
								id="bio"
								bind:value={formData.bio}
								disabled={isSubmitting}
								rows="3"
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
								placeholder="Short biography"
							></textarea>
						</div>

						<!-- Detailed Bio -->
						<div>
							<label for="detailedBio" class="block text-sm font-medium text-gray-700 mb-1">Detailed Bio</label>
							<textarea
								id="detailedBio"
								bind:value={formData.detailedBio}
								disabled={isSubmitting}
								rows="5"
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
								placeholder="Detailed biography"
							></textarea>
						</div>

						<!-- URLs and Media -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label for="avatarUrl" class="block text-sm font-medium text-gray-700 mb-1">Avatar URL</label>
								<input
									id="avatarUrl"
									type="url"
									bind:value={formData.avatarUrl}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									placeholder="https://example.com/avatar.jpg"
								/>
							</div>
							
							<div>
								<label for="youtubeVideoUrl" class="block text-sm font-medium text-gray-700 mb-1">YouTube Video URL</label>
								<input
									id="youtubeVideoUrl"
									type="url"
									bind:value={formData.youtubeVideoUrl}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									placeholder="https://youtube.com/watch?v=..."
								/>
							</div>
						</div>

						<!-- Education and Background -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label for="education" class="block text-sm font-medium text-gray-700 mb-1">Education</label>
								<input
									id="education"
									type="text"
									bind:value={formData.education}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									placeholder="Educational background"
								/>
							</div>
							
							<div>
								<label for="background" class="block text-sm font-medium text-gray-700 mb-1">Background</label>
								<input
									id="background"
									type="text"
									bind:value={formData.background}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
									placeholder="Professional background"
								/>
							</div>
						</div>

						<!-- Settings -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label for="publicSentiment" class="block text-sm font-medium text-gray-700 mb-1">Public Sentiment</label>
								<select
									id="publicSentiment"
									bind:value={formData.publicSentiment}
									disabled={isSubmitting}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
								>
									{#each sentimentOptions as sentiment}
										<option value={sentiment}>{sentiment}</option>
									{/each}
								</select>
							</div>
							
							<div class="flex items-center pt-6">
								<input
									id="isVerified"
									type="checkbox"
									bind:checked={formData.isVerified}
									disabled={isSubmitting}
									class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
								/>
								<label for="isVerified" class="ml-2 block text-sm text-gray-700">
									Verified Leader
								</label>
							</div>
						</div>
					</form>
				</CardContent>

				<!-- Footer -->
				<div class="border-t px-6 py-4 flex justify-end space-x-3">
					<Button 
						variant="outline" 
						on:click={handleClose}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button 
						on:click={handleSubmit}
						disabled={isSubmitting}
						className="bg-blue-600 hover:bg-blue-700 text-white"
					>
						{#if isSubmitting}
							<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
							</svg>
							{isEditing ? 'Saving...' : 'Creating...'}
						{:else}
							{submitButtonText}
						{/if}
					</Button>
				</div>
			</Card>
		</div>
	</div>
{/if}
