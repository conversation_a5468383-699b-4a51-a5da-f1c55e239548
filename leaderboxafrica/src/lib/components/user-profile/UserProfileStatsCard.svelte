<!-- User Profile Stats Card Component - Svelte conversion of React UserProfileStatsCard.jsx -->
<script lang="ts">
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import { Heart, UserCheck, FileText } from 'lucide-svelte';

	// Props
	let {
		profileUser
	} = $props();

	// Derived values
	let leadersFollowedCount = $derived(
		!profileUser?.isLeader ? (profileUser?.followedLeaders?.length || 0) : undefined
	);

	let activityCount = $derived(
		profileUser?.activityCount || Math.floor(Math.random() * 50)
	);
</script>

<Card class="modern-card">
	<CardHeader>
		<CardTitle class="text-lg text-primary">Profile Stats</CardTitle>
	</CardHeader>
	<CardContent className="p-6 pt-0 space-y-2 text-sm">
		<!-- Profile Likes -->
		<div class="flex justify-between">
			<span class="flex items-center">
				<Heart size={14} class="mr-1.5 text-muted-foreground"/> 
				Profile Likes:
			</span> 
			<span class="font-semibold">{profileUser?.profileLikesCount || 0}</span>
		</div>
		
		<!-- Followers (for leaders only) -->
		{#if profileUser?.isLeader}
			<div class="flex justify-between">
				<span class="flex items-center">
					<UserCheck size={14} class="mr-1.5 text-muted-foreground"/> 
					Followers:
				</span> 
				<span class="font-semibold">{profileUser?.followers || 0}</span>
			</div>
		{/if}
		
		<!-- Leaders Followed (for non-leaders only) -->
		{#if leadersFollowedCount !== undefined}
			<div class="flex justify-between">
				<span class="flex items-center">
					<UserCheck size={14} class="mr-1.5 text-muted-foreground"/> 
					Leaders Followed:
				</span> 
				<span class="font-semibold">{leadersFollowedCount}</span>
			</div>
		{/if}
		
		<!-- Posts/Activities -->
		<div class="flex justify-between">
			<span class="flex items-center">
				<FileText size={14} class="mr-1.5 text-muted-foreground"/>
				Posts/Activities:
			</span>
			<span class="font-semibold">{activityCount}</span>
		</div>
	</CardContent>
</Card>

<style>
	:global(.modern-card) {
		@apply bg-card border border-border rounded-lg shadow-sm;
	}
</style>
