<!-- User Activity Tabs Content Component - Svelte conversion of React UserActivityTabsContent.jsx -->
<script lang="ts">
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import Tabs from '$lib/components/ui/Tabs.svelte';
	import TabsList from '$lib/components/ui/TabsList.svelte';
	import TabsTrigger from '$lib/components/ui/TabsTrigger.svelte';
	import TabsContent from '$lib/components/ui/TabsContent.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';

	// Props
	let {
		profileUser,
		leaders = []
	} = $props();

	// State
	let activeTab = $state('activity');

	// Helper functions
	function formatDate(date: Date) {
		return date.toLocaleDateString();
	}

	// Mock activity data - in real app, this would come from stores
	let baseActivityItems = [
		{
			type: 'Comment',
			content: 'Replied to "Fuel Subsidy Debate"',
			date: formatDate(new Date(Date.now() - 1 * 60 * 60 * 1000))
		},
		{
			type: 'Vote',
			content: 'Voted on "Best approach to urban development?"',
			date: formatDate(new Date(Date.now() - 5 * 60 * 60 * 1000))
		}
	];

	// Derived activity items with follow activity
	let activityItems = $derived(() => {
		let items = [...baseActivityItems];

		// Add follow activity if user is not a leader and has followed leaders
		if (!profileUser?.isLeader && profileUser?.followedLeaders?.length > 0) {
			const followedLeader = leaders.find(l => l.id === profileUser.followedLeaders[0]);
			if (followedLeader) {
				items.push({
					type: 'Follow',
					content: `Started following ${followedLeader.name}`,
					date: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000))
				});
			}
		}

		return items;
	});

	// Derived values
	let followedLeaders = $derived(() => {
		if (!profileUser?.followedLeaders || !leaders) return [];
		return profileUser.followedLeaders
			.map(leaderId => leaders.find(l => l.id === leaderId))
			.filter(Boolean);
	});

</script>

<div>
	<Tabs bind:value={activeTab}>
		<!-- Tab List -->
		<TabsList class="grid w-full grid-cols-2 sm:grid-cols-3 bg-secondary/50 rounded-lg p-1 mb-4 overflow-x-auto no-scrollbar">
			<TabsTrigger value="activity" class="dashboard-tab-trigger">
				Activity
			</TabsTrigger>
			<TabsTrigger value="banters" class="dashboard-tab-trigger">
				Banters
			</TabsTrigger>
			{#if !profileUser?.isLeader}
				<TabsTrigger value="followedLeaders" class="dashboard-tab-trigger">
					Following
				</TabsTrigger>
			{/if}
		</TabsList>

		<!-- Activity Tab -->
		<TabsContent value="activity">
			<Card class="modern-card">
				<CardHeader>
					<CardTitle class="text-lg text-primary">Recent Activity</CardTitle>
				</CardHeader>
				<CardContent>
					{#each activityItems as item}
						<div class="p-3 border-b border-border last:border-b-0 hover:bg-secondary/50 rounded-md">
							<p class="text-sm text-foreground">{item.content}</p>
							<p class="text-xs text-muted-foreground">{item.type} &bull; {item.date}</p>
						</div>
					{/each}
					<p class="text-center text-muted-foreground pt-4">More activity coming soon...</p>
				</CardContent>
			</Card>
		</TabsContent>

		<!-- Banters Tab -->
		<TabsContent value="banters">
			<Card class="modern-card">
				<CardContent>
					<p class="text-muted-foreground">User's banters will appear here.</p>
				</CardContent>
			</Card>
		</TabsContent>

		<!-- Followed Leaders Tab -->
		{#if !profileUser?.isLeader}
			<TabsContent value="followedLeaders">
				<Card class="modern-card">
					<CardHeader>
						<CardTitle class="text-lg text-primary">Leaders Followed</CardTitle>
					</CardHeader>
					<CardContent>
					{#if followedLeaders.length > 0}
						<ul class="space-y-2">
							{#each followedLeaders as leader (leader.id)}
								<li class="flex items-center space-x-2 p-2 hover:bg-secondary/30 rounded-md">
									<Avatar
										className="h-8 w-8"
										src={leader.avatarUrl}
										alt={leader.name}
										fallback={leader.name.substring(0, 1)}
									/>
									<span class="text-sm text-foreground">
										{leader.name}
									</span>
								</li>
							{/each}
						</ul>
					{:else}
						<p class="text-muted-foreground">Not following any leaders yet.</p>
					{/if}
					</CardContent>
				</Card>
			</TabsContent>
		{/if}
	</Tabs>
</div>

<style>
	:global(.no-scrollbar::-webkit-scrollbar) {
		display: none;
	}
	:global(.no-scrollbar) {
		-ms-overflow-style: none; 
		scrollbar-width: none; 
	}
	:global(.dashboard-tab-trigger) {
		@apply data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-3 py-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1.5 whitespace-nowrap;
	}
</style>
