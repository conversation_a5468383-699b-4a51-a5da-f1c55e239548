<!-- User Profile Header Card Component - Svelte conversion of React UserProfileHeaderCard.jsx -->
<script lang="ts">
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';
	import Tooltip from '$lib/components/ui/Tooltip.svelte';
	import { Settings, Mail, MapPin, Briefcase, ShieldCheck, CalendarDays, Heart, Megaphone, Users } from 'lucide-svelte';

	// Props
	let {
		profileUser,
		isCurrentUserProfile = false,
		onSendMessage,
		onEditProfile,
		onLikeToggle,
		isLikedByCurrentUser = false
	} = $props();

	// Helper function to get user badges
	function getBadges(user: any) {
		const badges = [];
		if ((user.petitionsCreated || 0) >= 10) {
			badges.push({ 
				name: 'Activist', 
				icon: Megaphone, 
				color: 'bg-orange-500/20 text-orange-500 border-orange-500/30', 
				description: 'Posted 10+ petitions' 
			});
		}
		if ((user.profileLikesCount || 0) >= 1000) {
			badges.push({ 
				name: 'Influencer', 
				icon: Heart, 
				color: 'bg-pink-500/20 text-pink-500 border-pink-500/30', 
				description: 'Over 1000 profile likes' 
			});
		}
		if ((user.groupMembersCount || 0) >= 1000) {
			badges.push({ 
				name: 'Organizer', 
				icon: Users, 
				color: 'bg-blue-500/20 text-blue-500 border-blue-500/30', 
				description: 'Over 1000 group members' 
			});
		}
		return badges;
	}

	// Derived values
	let userJoinedDate = $derived(
		profileUser?.createdAt ? new Date(profileUser.createdAt).toLocaleDateString() : "Earlier"
	);

	let userBadges = $derived(
		profileUser ? getBadges(profileUser) : []
	);

	let username = $derived(
		profileUser?.name?.toLowerCase().replace(/\s+/g, '') || profileUser?.email?.split('@')[0] || 'user'
	);

	let avatarFallback = $derived(
		profileUser?.name
			? profileUser.name.substring(0, 2).toUpperCase()
			: (profileUser?.email ? profileUser.email.substring(0, 2).toUpperCase() : 'U')
	);

</script>

{#if profileUser}
	<div class="modern-card overflow-hidden shadow-xl">
		<!-- Header Background -->
		<div class="relative">
			<div class="h-40 md:h-48 bg-gradient-to-br from-primary/60 via-accent/40 to-secondary/50"></div>
			<div class="absolute left-1/2 md:left-8 transform -translate-x-1/2 md:translate-x-0 -bottom-16 md:-bottom-12 z-10">
				<Avatar
					className="h-32 w-32 md:h-36 md:w-36 border-4 border-card shadow-lg bg-secondary rounded-full"
					src={profileUser.avatarUrl || `https://avatar.vercel.sh/${profileUser.name || profileUser.email}.png?size=144`}
					alt={profileUser.name}
					fallback={avatarFallback}
				/>
			</div>
		</div>

		<!-- Profile Content -->
		<div class="pt-20 md:pt-8 pb-6 px-6 md:pl-[calc(2rem+9.5rem)] text-center md:text-left">
			<div class="flex flex-col md:flex-row justify-between items-center gap-4">
				<!-- User Info -->
				<div>
					<h1 class="text-2xl md:text-3xl font-bold text-foreground flex items-center justify-center md:justify-start">
						{profileUser.name || "User"}
						{#if profileUser.isLeader}
							<ShieldCheck size={24} class="ml-2 text-blue-500" title="Verified Leader"/>
						{/if}
					</h1>
					<p class="text-muted-foreground text-sm">@{username}</p>
				</div>

				<!-- Action Buttons -->
				<div class="flex items-center gap-2">
					{#if !isCurrentUserProfile}
						<Button
							on:click={onLikeToggle}
							variant={isLikedByCurrentUser ? "default" : "outline"}
							className={`transition-all ${isLikedByCurrentUser ? 'btn-primary' : 'button-outline-override'}`}
						>
							<Heart size={18} class={`mr-2 ${isLikedByCurrentUser ? 'fill-current' : ''}`} />
							{profileUser.profileLikesCount || 0}
						</Button>
					{/if}

					{#if isCurrentUserProfile}
						<Button variant="outline" on:click={onEditProfile} className="button-outline-override">
							<Settings size={18} class="mr-2" />
							Edit Profile
						</Button>
					{:else}
						<Button on:click={onSendMessage} className="btn-primary">
							<Mail size={18} class="mr-2" />
							Message
						</Button>
					{/if}
				</div>
			</div>

			<!-- Badges -->
			{#if userBadges.length > 0}
				<div class="mt-3 flex flex-wrap gap-2 justify-center md:justify-start">
					{#each userBadges as badge (badge.name)}
						<Tooltip content={badge.description}>
							<Badge variant="outline" class={`font-semibold ${badge.color}`}>
								<svelte:component this={badge.icon} size={12} class="mr-1" />
								{badge.name}
							</Badge>
						</Tooltip>
					{/each}
				</div>
			{/if}

			<!-- Bio -->
			{#if profileUser.bio}
				<p class="mt-3 text-sm text-foreground/80 max-w-xl">{profileUser.bio}</p>
			{/if}

			<!-- Profile Details -->
			<div class="mt-4 flex flex-wrap gap-x-4 gap-y-2 text-xs text-muted-foreground justify-center md:justify-start">
				{#if profileUser.position}
					<span class="flex items-center">
						<Briefcase size={14} class="mr-1.5"/> 
						{profileUser.position}
					</span>
				{/if}
				
				{#if profileUser.state}
					<span class="flex items-center">
						<MapPin size={14} class="mr-1.5"/> 
						{profileUser.state}{profileUser.lga ? `, ${profileUser.lga}` : ''}
					</span>
				{/if}
				
				{#if profileUser.party}
					<span class="flex items-center">
						<ShieldCheck size={14} class="mr-1.5"/> 
						{profileUser.party}
					</span>
				{/if}
				
				<span class="flex items-center">
					<CalendarDays size={14} class="mr-1.5"/> 
					Joined {userJoinedDate}
				</span>
			</div>
		</div>
	</div>
{/if}

<style>
	:global(.modern-card) {
		@apply bg-card border border-border rounded-lg shadow-sm;
	}
	
	:global(.btn-primary) {
		@apply bg-primary text-primary-foreground hover:bg-primary/90;
	}
	
	:global(.button-outline-override) {
		@apply border-border hover:bg-accent hover:text-accent-foreground;
	}
</style>
