<!-- Header Component -->
<!-- Main navigation header for the application -->

<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { authStore, user, isAuthenticated } from '$lib/stores/auth.ts';
	import { notifications } from '$lib/stores/notifications';

	// Navigation items
	const navItems = [
		{ href: '/', label: 'Home' },
		{ href: '/leaders', label: 'Leaders' },
		{ href: '/polls', label: 'Polls' },
		{ href: '/banter', label: 'Banter' },
		{ href: '/petitions', label: 'Petitions' },
		{ href: '/groups', label: 'Groups' }
	];

	// Mobile menu state
	let mobileMenuOpen = false;

	// Handle logout
	async function handleLogout() {
		const result = await authStore.logout();
		notifications.success('Logged out successfully');
		goto('/');
	}

	// Close mobile menu when route changes using Svelte 5 effect
	$effect(() => {
		if ($page.url.pathname) {
			mobileMenuOpen = false;
		}
	});
</script>

<header class="bg-white shadow-sm border-b border-gray-200">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="flex justify-between items-center h-16">
			<!-- Logo -->
			<div class="flex items-center">
				<a href="/" class="flex items-center">
					<img src="/leaderbox-logo.svg" alt="LeaderBox" class="h-8 w-auto" />
				</a>
			</div>

			<!-- Desktop Navigation -->
			<nav class="hidden md:flex space-x-8">
				{#each navItems as item}
					<a
						href={item.href}
						class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors
							{$page.url.pathname === item.href ? 'text-blue-600 bg-blue-50' : ''}"
					>
						{item.label}
					</a>
				{/each}
			</nav>

			<!-- User Menu -->
			<div class="flex items-center space-x-4">
				{#if $isAuthenticated && $user}
					<!-- User dropdown -->
					<div class="relative">
						<button
							type="button"
							class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md p-2"
						>
							<div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
								{#if $user.avatarUrl}
									<img src={$user.avatarUrl} alt={$user.name} class="w-8 h-8 rounded-full" />
								{:else}
									<span class="text-sm font-medium text-gray-700">
										{$user.name?.charAt(0) || $user.email.charAt(0)}
									</span>
								{/if}
							</div>
							<span class="hidden sm:block text-sm font-medium">{$user.name || 'User'}</span>
						</button>
						
						<!-- Dropdown menu (you can implement this with a proper dropdown library) -->
						<!-- For now, just show basic user info -->
					</div>

					<!-- Logout button -->
					<button
						on:click={handleLogout}
						class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
					>
						Logout
					</button>

					{#if $user.isAdmin}
						<a
							href="/admin"
							class="bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
						>
							Admin
						</a>
					{/if}
				{:else}
					<!-- Auth buttons -->
					<a
						href="/auth/login"
						class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
					>
						Login
					</a>
					<a
						href="/auth/register"
						class="bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
					>
						Sign Up
					</a>
				{/if}

				<!-- Mobile menu button -->
				<button
					type="button"
					class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
					on:click={() => mobileMenuOpen = !mobileMenuOpen}
				>
					<span class="sr-only">Open main menu</span>
					{#if mobileMenuOpen}
						<!-- X icon -->
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					{:else}
						<!-- Hamburger icon -->
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					{/if}
				</button>
			</div>
		</div>
	</div>

	<!-- Mobile menu -->
	{#if mobileMenuOpen}
		<div class="md:hidden">
			<div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
				{#each navItems as item}
					<a
						href={item.href}
						class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors
							{$page.url.pathname === item.href ? 'text-blue-600 bg-blue-50' : ''}"
					>
						{item.label}
					</a>
				{/each}
				
				{#if $isAuthenticated && $user}
					<div class="border-t border-gray-200 pt-4 mt-4">
						<div class="px-3 py-2">
							<div class="text-base font-medium text-gray-800">{$user.name || 'User'}</div>
							<div class="text-sm font-medium text-gray-500">{$user.email}</div>
						</div>
						{#if $user.isAdmin}
							<a
								href="/admin"
								class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
							>
								Admin Dashboard
							</a>
						{/if}
						<button
							on:click={handleLogout}
							class="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
						>
							Logout
						</button>
					</div>
				{:else}
					<div class="border-t border-gray-200 pt-4 mt-4">
						<a
							href="/auth/login"
							class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
						>
							Login
						</a>
						<a
							href="/auth/register"
							class="block px-3 py-2 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700"
						>
							Sign Up
						</a>
					</div>
				{/if}
			</div>
		</div>
	{/if}
</header>
