<!-- Media Display Item Component - Svelte 5 conversion of React MediaDisplayItem.jsx -->
<script lang="ts">
	import { FileImage, Video, FileText, Paperclip } from 'lucide-svelte';
	import ImageViewerModal from '$lib/components/ui/ImageViewerModal.svelte';

	// Props
	let { 
		mediaItem,
		...restProps 
	} = $props();

	// State
	let isImageViewerOpen = $state(false);
	let selectedImageUrl = $state('');

	// Helper function to get media icon
	function getMediaIconElement(fileName: string, type?: string) {
		const commonClass = "h-5 w-5 flex-shrink-0";
		
		if (type?.startsWith('image/')) return { component: FileImage, class: `${commonClass} text-blue-500` };
		if (type?.startsWith('video/')) return { component: Video, class: `${commonClass} text-purple-500` };
		if (type === 'application/pdf') return { component: FileText, class: `${commonClass} text-red-500` };
		if (type === 'application/msword' || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
			return { component: FileText, class: `${commonClass} text-sky-500` };
		}
		
		if (/\.(jpe?g|png|gif|webp)$/i.test(fileName)) return { component: FileImage, class: `${commonClass} text-blue-500` };
		if (/\.(mp4|mov|avi|wmv)$/i.test(fileName)) return { component: Video, class: `${commonClass} text-purple-500` };
		if (/\.(pdf)$/i.test(fileName)) return { component: FileText, class: `${commonClass} text-red-500` };
		if (/\.(doc|docx)$/i.test(fileName)) return { component: FileText, class: `${commonClass} text-sky-500` };
		
		return { component: Paperclip, class: `${commonClass} text-gray-500` };
	}

	// Derived values
	let isImage = $derived(
		mediaItem?.type?.startsWith('image/') || 
		/\.(jpe?g|png|gif|webp)$/i.test(mediaItem?.name || '')
	);
	let displayUrl = $derived(mediaItem?.url || mediaItem?.previewUrl);
	let iconInfo = $derived(getMediaIconElement(mediaItem?.name || '', mediaItem?.type));

	// Handle image click
	function handleImageClick(e: MouseEvent) {
		if (isImage && displayUrl) {
			e.preventDefault();
			selectedImageUrl = displayUrl;
			isImageViewerOpen = true;
		}
	}

	// Handle close viewer
	function handleCloseViewer() {
		isImageViewerOpen = false;
		selectedImageUrl = '';
	}

	// Common classes
	const commonClasses = "flex items-center gap-1.5 text-xs p-1.5 bg-secondary/60 rounded-md hover:bg-secondary transition-colors max-w-[150px] sm:max-w-[180px] truncate text-left w-full";
</script>

{#if mediaItem && mediaItem.name}
	{#if isImage}
		<button 
			type="button" 
			on:click={handleImageClick} 
			class={commonClasses} 
			title="View {mediaItem.name}"
			{...restProps}
		>
			{#if isImage && displayUrl}
				<img src={displayUrl} alt={mediaItem.name} class="h-6 w-6 rounded object-cover flex-shrink-0" />
			{:else}
				<svelte:component this={iconInfo.component} class={iconInfo.class} />
			{/if}
			<span class="truncate text-foreground/90">{mediaItem.name}</span>
		</button>
	{:else}
		<a 
			href={displayUrl || '#'} 
			target="_blank" 
			rel="noopener noreferrer"
			class={commonClasses}
			title="Open {mediaItem.name}"
			{...restProps}
		>
			<svelte:component this={iconInfo.component} class={iconInfo.class} />
			<span class="truncate text-foreground/90">{mediaItem.name}</span>
		</a>
	{/if}

	{#if isImage}
		<ImageViewerModal 
			bind:isOpen={isImageViewerOpen}
			onClose={handleCloseViewer}
			imageUrl={selectedImageUrl}
			imageAlt={mediaItem.name}
		/>
	{/if}
{/if}
