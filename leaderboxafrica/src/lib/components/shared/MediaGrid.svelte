<!-- Media Grid Component - Svelte 5 conversion of React MediaGrid.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import ImageViewerModal from '$lib/components/ui/ImageViewerModal.svelte';

	// Props
	let { 
		mediaItems = [],
		...restProps 
	} = $props();

	// State
	let viewerState = $state({ isOpen: false, imageUrl: '' });

	// Handle item click
	function handleItemClick(item: any) {
		const isImage = item.type?.startsWith('image/') || /\.(jpe?g|png|gif|webp|svg)$/i.test(item.name);
		if (isImage) {
			viewerState = { isOpen: true, imageUrl: item.url };
		} else {
			// For non-image files, open in new tab
			window.open(item.url, '_blank');
		}
	}

	// Handle close viewer
	function handleCloseViewer() {
		viewerState = { isOpen: false, imageUrl: '' };
	}

	// Derived values
	let count = $derived(mediaItems?.length || 0);

	// Helper to check if item is image or video
	function isImage(item: any) {
		return item.type?.startsWith('image/') || /\.(jpe?g|png|gif|webp|svg)$/i.test(item.name);
	}

	function isVideo(item: any) {
		return item.type?.startsWith('video/') || /\.(mp4|mov|avi|wmv)$/i.test(item.name);
	}

	// Base classes
	const baseClasses = "group aspect-square";
</script>

{#if mediaItems && count > 0}
	<div {...restProps}>
		{#if count === 1}
			<div class="grid grid-cols-1 aspect-[16/9]">
				<div
					class="aspect-auto relative w-full h-full cursor-pointer overflow-hidden group"
					on:click={() => handleItemClick(mediaItems[0])}
					role="button"
					tabindex="0"
					on:keydown={(e) => e.key === 'Enter' && handleItemClick(mediaItems[0])}
				>
					{#if isImage(mediaItems[0])}
						<img src={mediaItems[0].url} alt={mediaItems[0].name} class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
					{:else if isVideo(mediaItems[0])}
						<video src={mediaItems[0].url} class="w-full h-full object-cover" controls={false}></video>
					{:else}
						<div class="w-full h-full bg-secondary flex items-center justify-center">
							<p class="text-xs text-muted-foreground p-2">{mediaItems[0].name}</p>
						</div>
					{/if}
				</div>
			</div>
		{:else if count === 2}
			<div class="grid grid-cols-2 gap-0.5 aspect-[16/9]">
				{#each mediaItems.slice(0, 2) as item}
					<div
						class="{baseClasses} relative w-full h-full cursor-pointer overflow-hidden"
						on:click={() => handleItemClick(item)}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleItemClick(item)}
					>
						{#if isImage(item)}
							<img src={item.url} alt={item.name} class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
						{:else if isVideo(item)}
							<video src={item.url} class="w-full h-full object-cover" controls={false}></video>
						{:else}
							<div class="w-full h-full bg-secondary flex items-center justify-center">
								<p class="text-xs text-muted-foreground p-2">{item.name}</p>
							</div>
						{/if}
					</div>
				{/each}
			</div>
		{:else if count === 3}
			<div class="grid grid-cols-2 grid-rows-2 gap-0.5 aspect-[16/9]">
				<div class="row-span-2">
					<div
						class="h-full relative w-full cursor-pointer overflow-hidden group"
						on:click={() => handleItemClick(mediaItems[0])}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleItemClick(mediaItems[0])}
					>
						{#if isImage(mediaItems[0])}
							<img src={mediaItems[0].url} alt={mediaItems[0].name} class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
						{:else if isVideo(mediaItems[0])}
							<video src={mediaItems[0].url} class="w-full h-full object-cover" controls={false}></video>
						{:else}
							<div class="w-full h-full bg-secondary flex items-center justify-center">
								<p class="text-xs text-muted-foreground p-2">{mediaItems[0].name}</p>
							</div>
						{/if}
					</div>
				</div>
				{#each mediaItems.slice(1, 3) as item}
					<div
						class="{baseClasses} relative w-full h-full cursor-pointer overflow-hidden"
						on:click={() => handleItemClick(item)}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleItemClick(item)}
					>
						{#if isImage(item)}
							<img src={item.url} alt={item.name} class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
						{:else if isVideo(item)}
							<video src={item.url} class="w-full h-full object-cover" controls={false}></video>
						{:else}
							<div class="w-full h-full bg-secondary flex items-center justify-center">
								<p class="text-xs text-muted-foreground p-2">{item.name}</p>
							</div>
						{/if}
					</div>
				{/each}
			</div>
		{:else}
			<!-- 4 or more items -->
			<div class="grid grid-cols-2 grid-rows-2 gap-0.5 aspect-square">
				{#each mediaItems.slice(0, 4) as item, index}
					<div
						class="{baseClasses} relative w-full h-full cursor-pointer overflow-hidden"
						on:click={() => handleItemClick(item)}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleItemClick(item)}
					>
						{#if isImage(item)}
							<img src={item.url} alt={item.name} class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
						{:else if isVideo(item)}
							<video src={item.url} class="w-full h-full object-cover" controls={false}></video>
						{:else}
							<div class="w-full h-full bg-secondary flex items-center justify-center">
								<p class="text-xs text-muted-foreground p-2">{item.name}</p>
							</div>
						{/if}
						{#if index === 3 && count > 4}
							<div class="absolute inset-0 bg-black/50 flex items-center justify-center">
								<span class="text-white text-2xl font-bold">+{count - 4}</span>
							</div>
						{/if}
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<ImageViewerModal
		bind:isOpen={viewerState.isOpen}
		onClose={handleCloseViewer}
		imageUrl={viewerState.imageUrl}
	/>
{/if}
