<!-- Popover Trigger Component -->
<script lang="ts">
	import { getContext } from 'svelte';

	// Props
	let { 
		children,
		disabled = false,
		...restProps 
	} = $props();

	// Get popover context
	const popoverContext = getContext('popover');

	// Handle click
	function handleClick() {
		if (!disabled && popoverContext) {
			popoverContext.setOpen(!popoverContext.open);
		}
	}
</script>

<div on:click={handleClick} {...restProps}>
	{@render children?.()}
</div>
