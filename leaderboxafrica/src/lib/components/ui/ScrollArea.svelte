<!-- Scroll Area Component - Svelte 5 conversion of React scroll-area.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';

	// Props
	let { 
		className = '',
		height = 'auto',
		children,
		...restProps 
	} = $props();
</script>

<div
	class={cn("relative overflow-hidden", className)}
	style="height: {height}"
	{...restProps}
>
	<div class="h-full w-full rounded-[inherit] overflow-auto">
		{@render children?.()}
	</div>
	
	<!-- Custom scrollbar styling via CSS -->
	<style>
		:global(.scroll-area) {
			scrollbar-width: thin;
			scrollbar-color: hsl(var(--border)) transparent;
		}
		
		:global(.scroll-area::-webkit-scrollbar) {
			width: 10px;
		}
		
		:global(.scroll-area::-webkit-scrollbar-track) {
			background: transparent;
		}
		
		:global(.scroll-area::-webkit-scrollbar-thumb) {
			background-color: hsl(var(--border));
			border-radius: 5px;
			border: 2px solid transparent;
			background-clip: content-box;
		}
		
		:global(.scroll-area::-webkit-scrollbar-thumb:hover) {
			background-color: hsl(var(--border) / 0.8);
		}
	</style>
</div>
