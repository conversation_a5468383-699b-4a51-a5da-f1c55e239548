<!-- Tabs Content Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { getContext } from 'svelte';

	// Props
	let { 
		value: contentValue,
		className = '',
		children,
		...restProps 
	} = $props();

	// Get tabs context
	const tabsContext = getContext('tabs');
	
	// Derived state
	let isActive = $derived(tabsContext?.value === contentValue);
</script>

{#if isActive}
	<div
		class={cn(
			"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
			className
		)}
		role="tabpanel"
		{...restProps}
	>
		{@render children?.()}
	</div>
{/if}
