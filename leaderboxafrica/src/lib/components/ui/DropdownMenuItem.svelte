<!-- Dropdown Menu Item Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { getContext } from 'svelte';

	// Props
	let { 
		className = '',
		inset = false,
		disabled = false,
		onClick,
		children,
		...restProps 
	} = $props();

	// Get dropdown context
	const dropdownContext = getContext('dropdown');

	// Handle click
	function handleClick(event: MouseEvent) {
		if (!disabled) {
			onClick?.(event);
			// Close dropdown after item click
			if (dropdownContext) {
				dropdownContext.setOpen(false);
			}
		}
	}
</script>

<button
	type="button"
	class={cn(
		"relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground hover:bg-accent hover:text-accent-foreground",
		inset && "pl-8",
		disabled && "pointer-events-none opacity-50",
		className
	)}
	{disabled}
	on:click={handleClick}
	{...restProps}
>
	{@render children?.()}
</button>
