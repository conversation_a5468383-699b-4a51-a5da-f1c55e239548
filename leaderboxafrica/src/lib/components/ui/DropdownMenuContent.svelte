<!-- Dropdown Menu Content Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { getContext } from 'svelte';

	// Props
	let { 
		className = '',
		sideOffset = 4,
		children,
		...restProps 
	} = $props();

	// Get dropdown context
	const dropdownContext = getContext('dropdown');
	let contentElement: HTMLDivElement;

	// Handle click outside
	function handleClickOutside(event: MouseEvent) {
		if (dropdownContext && dropdownContext.open && 
			contentElement && !contentElement.contains(event.target as Node)) {
			dropdownContext.setOpen(false);
		}
	}
</script>

<svelte:window on:click={handleClickOutside} />

{#if dropdownContext?.open}
	<!-- Portal-like behavior -->
	<div
		bind:this={contentElement}
		class={cn(
			"absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md",
			"animate-in fade-in-0 zoom-in-95 duration-200",
			"top-full mt-1",
			className
		)}
		style="margin-top: {sideOffset}px"
		{...restProps}
	>
		{@render children?.()}
	</div>
{/if}
