<!-- <PERSON><PERSON> Cancel Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import Button from './Button.svelte';

	// Props
	let { 
		className = '',
		variant = 'outline',
		size = 'default',
		onClick,
		children,
		...restProps 
	} = $props();

	function handleClick(event: MouseEvent) {
		onClick?.(event);
	}
</script>

<Button
	{variant}
	{size}
	class={cn("mt-2 sm:mt-0", className)}
	on:click={handleClick}
	{...restProps}
>
	{@render children?.()}
</Button>
