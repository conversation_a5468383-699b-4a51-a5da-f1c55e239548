<!-- Combobox Component - Svelte 5 conversion of React combobox.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { Check, ChevronsUpDown } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';
	import Button from './Button.svelte';
	import Input from './Input.svelte';

	// Props
	let { 
		options = [],
		value = $bindable(''),
		onChange,
		placeholder = 'Select option...',
		inputClassName = '',
		disabled = false,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let open = $state(false);
	let inputValue = $state('');
	let popoverElement: HTMLDivElement;
	let triggerElement: HTMLDivElement;

	// Derived values
	let filteredOptions = $derived(
		options.filter(option => 
			option.label.toLowerCase().includes(inputValue.toLowerCase())
		)
	);

	// Effects
	$effect(() => {
		const selectedOption = options.find(opt => opt.value === value);
		inputValue = selectedOption ? selectedOption.label : (value || '');
	});

	// Handle selection
	function handleSelect(optionValue: string) {
		const selectedOption = options.find(opt => opt.value === optionValue);
		value = optionValue;
		inputValue = selectedOption ? selectedOption.label : '';
		open = false;
		onChange?.(optionValue);
		dispatch('change', optionValue);
	}

	// Handle input change
	function handleInputChange(event: Event) {
		const target = event.target as HTMLInputElement;
		inputValue = target.value;
		
		const matchedOption = options.find(opt => 
			opt.label.toLowerCase() === inputValue.toLowerCase()
		);
		
		if (matchedOption) {
			value = matchedOption.value;
			onChange?.(matchedOption.value);
		} else {
			value = inputValue;
			onChange?.(inputValue);
		}
		dispatch('change', value);
	}

	// Handle click outside
	function handleClickOutside(event: MouseEvent) {
		if (triggerElement && !triggerElement.contains(event.target as Node) &&
			popoverElement && !popoverElement.contains(event.target as Node)) {
			open = false;
		}
	}

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			open = false;
		}
	}
</script>

<svelte:window on:click={handleClickOutside} on:keydown={handleKeydown} />

<div class="relative" bind:this={triggerElement} {...restProps}>
	<div class="relative">
		<Input
			bind:value={inputValue}
			on:input={handleInputChange}
			{placeholder}
			className={cn("w-full justify-between pr-10", inputClassName, disabled ? "cursor-not-allowed opacity-50" : "")}
			{disabled}
			on:click={() => !disabled && (open = true)}
		/>
		{#if !disabled}
			<Button
				variant="ghost"
				class="absolute right-0 top-0 h-full px-3"
				on:click={() => open = !open}
			>
				<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
			</Button>
		{/if}
	</div>

	{#if open}
		<div
			bind:this={popoverElement}
			class="absolute z-50 w-full mt-1 bg-popover border border-border rounded-md shadow-md p-0"
		>
			<div class="max-h-60 overflow-auto">
				{#if filteredOptions.length === 0}
					<div class="py-6 text-center text-sm text-muted-foreground">
						No option found.
					</div>
				{:else}
					{#each filteredOptions as option (option.value)}
						<button
							type="button"
							class="relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
							on:click={() => handleSelect(option.value)}
						>
							<Check
								class={cn(
									"absolute left-2 h-4 w-4",
									value === option.value ? "opacity-100" : "opacity-0"
								)}
							/>
							{option.label}
						</button>
					{/each}
				{/if}
			</div>
		</div>
	{/if}
</div>
