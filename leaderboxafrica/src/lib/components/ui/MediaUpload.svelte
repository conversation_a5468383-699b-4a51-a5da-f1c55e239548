<!-- Media Upload Component - Svelte 5 conversion of React MediaUpload.jsx -->
<script lang="ts">
	import Input from './Input.svelte';
	import Button from './Button.svelte';
	import ScrollArea from './ScrollArea.svelte';
	import Label from './Label.svelte';
	import ImageViewerModal from './ImageViewerModal.svelte';
	import { Paperclip, FileImage, Video, FileText, X, UploadCloud } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		onFilesChange,
		existingFiles = [],
		maxFiles = 5,
		maxFileSizeMB = 10,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let selectedFilesData = $state(
		existingFiles.map((file: any) => ({
			id: `existing-${file.name}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
			file: file,
			previewUrl: file.url || (file.type?.startsWith('image/') ? URL.createObjectURL(file) : null)
		}))
	);

	let isImageViewerOpen = $state(false);
	let selectedImageUrl = $state('');
	let selectedImageAlt = $state('');

	// Helper function to get file icon
	function getFileIconElement(fileName: string, fileType?: string) {
		const commonClass = "h-5 w-5 flex-shrink-0";
		
		if (fileType?.startsWith('image/')) return { component: FileImage, class: `${commonClass} text-blue-500` };
		if (fileType?.startsWith('video/')) return { component: Video, class: `${commonClass} text-purple-500` };
		if (fileType === 'application/pdf') return { component: FileText, class: `${commonClass} text-red-500` };
		if (fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
			return { component: FileText, class: `${commonClass} text-sky-500` };
		}
		
		if (/\.(jpe?g|png|gif|webp)$/i.test(fileName)) return { component: FileImage, class: `${commonClass} text-blue-500` };
		if (/\.(mp4|mov|avi|wmv)$/i.test(fileName)) return { component: Video, class: `${commonClass} text-purple-500` };
		if (/\.(pdf)$/i.test(fileName)) return { component: FileText, class: `${commonClass} text-red-500` };
		if (/\.(doc|docx)$/i.test(fileName)) return { component: FileText, class: `${commonClass} text-sky-500` };
		
		return { component: Paperclip, class: `${commonClass} text-gray-500` };
	}

	// Handle file change
	function handleFileChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const files = Array.from(target.files || []);
		
		if (selectedFilesData.length + files.length > maxFiles) {
			dispatch('toast', {
				title: "File Limit Exceeded",
				description: `You can only upload a maximum of ${maxFiles} files.`,
				variant: "destructive",
			});
			return;
		}

		const newFilesData: any[] = [];
		const currentFiles = selectedFilesData.map(f => f.file);

		files.forEach(file => {
			if (file.size > maxFileSizeMB * 1024 * 1024) {
				dispatch('toast', {
					title: "File Too Large",
					description: `File "${file.name}" exceeds the ${maxFileSizeMB}MB size limit.`,
					variant: "destructive",
				});
				return;
			}

			const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
			let previewUrl = null;
			if (file.type.startsWith('image/')) {
				previewUrl = URL.createObjectURL(file);
			}
			newFilesData.push({ id: fileId, file, previewUrl });
		});
		
		const updatedFilesData = [...selectedFilesData, ...newFilesData];
		selectedFilesData = updatedFilesData;
		onFilesChange?.(updatedFilesData.map(f => f.file));
	}

	// Handle remove file
	function handleRemoveFile(fileIdToRemove: string) {
		const fileToRemove = selectedFilesData.find(f => f.id === fileIdToRemove);
		if (fileToRemove && fileToRemove.previewUrl && fileToRemove.previewUrl.startsWith('blob:')) {
			URL.revokeObjectURL(fileToRemove.previewUrl);
		}

		const updatedFilesData = selectedFilesData.filter(f => f.id !== fileIdToRemove);
		selectedFilesData = updatedFilesData;
		onFilesChange?.(updatedFilesData.map(f => f.file));
	}

	// Handle image preview click
	function handleImagePreviewClick(fileWrapper: any) {
		if (fileWrapper.previewUrl && fileWrapper.file.type.startsWith('image/')) {
			selectedImageUrl = fileWrapper.previewUrl;
			selectedImageAlt = fileWrapper.file.name;
			isImageViewerOpen = true;
		}
	}

	// Handle close viewer
	function handleCloseViewer() {
		isImageViewerOpen = false;
		selectedImageUrl = '';
		selectedImageAlt = '';
	}
</script>

<div class="space-y-3 mt-2" {...restProps}>
	<Label htmlFor="media-upload-input" className="cursor-pointer block">
		<div class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-border rounded-lg hover:border-primary transition-colors bg-secondary/30">
			<UploadCloud class="h-10 w-10 text-muted-foreground mb-2" />
			<span class="text-sm font-medium text-foreground">Click to upload or drag & drop</span>
			<span class="text-xs text-muted-foreground">Images, Videos, PDF (Max {maxFileSizeMB}MB, {maxFiles} files)</span>
		</div>
		<Input
			id="media-upload-input"
			type="file"
			multiple
			accept="image/*,video/*,.pdf"
			on:change={handleFileChange}
			className="hidden"
			disabled={selectedFilesData.length >= maxFiles}
		/>
	</Label>
	
	{#if selectedFilesData.length > 0}
		<div class="space-y-2">
			<p class="text-sm font-medium text-muted-foreground">Selected files ({selectedFilesData.length}/{maxFiles}):</p>
			<ScrollArea className="h-auto max-h-48 w-full rounded-md border border-border p-2 bg-background">
				<div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
					{#each selectedFilesData as fileWrapper (fileWrapper.id)}
						<div class="flex items-center justify-between p-1.5 bg-secondary/50 rounded-md text-xs">
							<div class="flex items-center gap-2 overflow-hidden">
								{#if fileWrapper.previewUrl && fileWrapper.file.type.startsWith('image/')}
									<button 
										type="button" 
										on:click={() => handleImagePreviewClick(fileWrapper)} 
										class="block h-8 w-8 flex-shrink-0 rounded overflow-hidden"
									>
										<img src={fileWrapper.previewUrl} alt={fileWrapper.file.name} class="h-full w-full object-cover" />
									</button>
								{:else}
									{@const iconInfo = getFileIconElement(fileWrapper.file.name, fileWrapper.file.type)}
									<span class="flex-shrink-0 p-1.5">
										<svelte:component this={iconInfo.component} class={iconInfo.class} />
									</span>
								{/if}
								<span class="truncate text-foreground" title={fileWrapper.file.name}>{fileWrapper.file.name}</span>
							</div>
							<Button 
								variant="ghost" 
								size="icon" 
								on:click={() => handleRemoveFile(fileWrapper.id)} 
								class="h-6 w-6 text-destructive hover:bg-destructive/10"
							>
								<X size={14} />
							</Button>
						</div>
					{/each}
				</div>
			</ScrollArea>
		</div>
	{/if}

	<ImageViewerModal 
		bind:isOpen={isImageViewerOpen}
		onClose={handleCloseViewer}
		imageUrl={selectedImageUrl}
		imageAlt={selectedImageAlt}
	/>
</div>
