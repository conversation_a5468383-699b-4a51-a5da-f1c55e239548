<!-- <PERSON><PERSON>alog Action Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import Button from './Button.svelte';

	// Props
	let { 
		className = '',
		variant = 'default',
		size = 'default',
		onClick,
		children,
		...restProps 
	} = $props();

	function handleClick(event: MouseEvent) {
		onClick?.(event);
	}
</script>

<Button
	{variant}
	{size}
	class={cn(className)}
	on:click={handleClick}
	{...restProps}
>
	{@render children?.()}
</Button>
