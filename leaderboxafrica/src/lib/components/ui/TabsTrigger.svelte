<!-- Tabs Trigger Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { getContext } from 'svelte';

	// Props
	let { 
		value: triggerValue,
		className = '',
		disabled = false,
		children,
		...restProps 
	} = $props();

	// Get tabs context
	const tabsContext = getContext('tabs');
	
	// Derived state
	let isActive = $derived(tabsContext?.value === triggerValue);

	// Handle click
	function handleClick() {
		if (!disabled && tabsContext) {
			tabsContext.setValue(triggerValue);
		}
	}

	// Handle keyboard events
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleClick();
		}
	}
</script>

<button
	type="button"
	role="tab"
	aria-selected={isActive}
	{disabled}
	class={cn(
		"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
		isActive ? "bg-background text-foreground shadow-sm" : "",
		className
	)}
	on:click={handleClick}
	on:keydown={handleKeydown}
	{...restProps}
>
	{@render children?.()}
</button>
