<!-- Sheet Component - Svelte 5 conversion of React sheet.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { X } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		open = $bindable(false),
		onOpenChange,
		side = 'right',
		className = '',
		children,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// Sheet variants based on side
	const sheetVariants = {
		top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
		bottom: "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
		left: "inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
		right: "inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"
	};

	// Handle open state changes
	function handleOpenChange(newOpen: boolean) {
		open = newOpen;
		onOpenChange?.(newOpen);
		dispatch('openChange', newOpen);
	}

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && open) {
			handleOpenChange(false);
		}
	}

	// Handle overlay click
	function handleOverlayClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleOpenChange(false);
		}
	}

	// Handle close button click
	function handleClose() {
		handleOpenChange(false);
	}

	// Get sheet classes based on side using Svelte 5 runes
	let sheetClasses = $derived(cn(
		"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out",
		"duration-300 animate-in",
		sheetVariants[side],
		className
	));
</script>

<svelte:window on:keydown={handleKeydown} />

{#if open}
	<!-- Portal-like behavior -->
	<div class="fixed inset-0 z-50">
		<!-- Overlay -->
		<div
			class="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-in fade-in-0 duration-200"
			on:click={handleOverlayClick}
			role="presentation"
		></div>

		<!-- Sheet Content -->
		<div
			class={sheetClasses}
			role="dialog"
			aria-modal="true"
			{...restProps}
		>
			{@render children?.()}
			
			<!-- Close button -->
			<button
				type="button"
				class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
				on:click={handleClose}
			>
				<X class="h-4 w-4" />
				<span class="sr-only">Close</span>
			</button>
		</div>
	</div>
{/if}
