<!-- Card Component - Exact replica of React Card -->
<script lang="ts">
	import { cn } from '$lib/utils.ts';
	import type { Snippet } from 'svelte';

	interface Props {
		className?: string;
		element?: string;
		children?: Snippet;
	}

	let { className = '', element = 'div', children, ...restProps }: Props = $props();
</script>

<svelte:element
	this={element}
	class={cn("rounded-lg border bg-card text-card-foreground shadow-sm", className)}
	{...restProps}
>
	{@render children?.()}
</svelte:element>
