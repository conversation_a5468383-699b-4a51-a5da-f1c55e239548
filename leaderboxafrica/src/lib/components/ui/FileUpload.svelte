<!-- Reusable File Upload Component with AWS S3 Integration -->
<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import Button from './Button.svelte';

	// Props
	export let uploadType: 'avatar' | 'cover' | 'documents' = 'avatar';
	export let currentFileUrl: string = '';
	export let label: string = 'Upload File';
	export let accept: string = 'image/*';
	export let disabled: boolean = false;
	export let required: boolean = false;
	export let className: string = '';
	export let showPreview: boolean = true;
	export let previewSize: 'sm' | 'md' | 'lg' = 'md';

	// State
	let uploading = false;
	let uploadProgress = 0;
	let dragOver = false;
	let fileInput: HTMLInputElement;
	let error: string = '';
	let uploadConfig: any = null;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		upload: { fileUrl: string; key: string };
		error: { error: string };
		progress: { progress: number };
	}>();

	// Preview size classes
	const previewSizes = {
		sm: 'w-16 h-16',
		md: 'w-24 h-24',
		lg: 'w-32 h-32'
	};

	// Load upload configuration
	async function loadUploadConfig() {
		try {
			const response = await fetch(`/api/upload/presigned-url?type=${uploadType}`);
			const data = await response.json();
			
			if (data.success) {
				uploadConfig = data.data[uploadType];
			}
		} catch (err) {
			console.error('Failed to load upload config:', err);
		}
	}

	// Initialize
	loadUploadConfig();

	// Handle file selection
	async function handleFileSelect(files: FileList | null) {
		if (!files || files.length === 0) return;
		
		const file = files[0];
		await uploadFile(file);
	}

	// Upload file to S3
	async function uploadFile(file: File) {
		if (!file) return;

		error = '';
		uploading = true;
		uploadProgress = 0;

		try {
			// Get presigned URL
			const presignedResponse = await fetch('/api/upload/presigned-url', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					fileName: file.name,
					fileType: file.type,
					fileSize: file.size,
					uploadType
				})
			});

			const presignedData = await presignedResponse.json();

			if (!presignedData.success) {
				throw new Error(presignedData.error);
			}

			const { uploadUrl, fileUrl, key } = presignedData.data;

			// Upload file to S3
			const uploadResponse = await fetch(uploadUrl, {
				method: 'PUT',
				body: file,
				headers: {
					'Content-Type': file.type
				}
			});

			if (!uploadResponse.ok) {
				throw new Error('Failed to upload file to S3');
			}

			// Success
			currentFileUrl = fileUrl;
			dispatch('upload', { fileUrl, key });

		} catch (err) {
			error = err instanceof Error ? err.message : 'Upload failed';
			dispatch('error', { error });
		} finally {
			uploading = false;
			uploadProgress = 0;
		}
	}

	// Handle drag and drop
	function handleDragOver(event: DragEvent) {
		event.preventDefault();
		dragOver = true;
	}

	function handleDragLeave(event: DragEvent) {
		event.preventDefault();
		dragOver = false;
	}

	function handleDrop(event: DragEvent) {
		event.preventDefault();
		dragOver = false;
		
		const files = event.dataTransfer?.files;
		if (files) {
			handleFileSelect(files);
		}
	}

	// Format file size
	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	// Get file type description
	function getFileTypeDescription(): string {
		if (!uploadConfig) return '';
		
		const types = uploadConfig.allowedTypes.map((type: string) => {
			if (type.startsWith('image/')) return type.replace('image/', '').toUpperCase();
			if (type.includes('pdf')) return 'PDF';
			if (type.includes('word')) return 'DOC';
			return type;
		});
		
		return types.join(', ');
	}
</script>

<div class="file-upload-container {className}">
	<!-- Label -->
	{#if label}
		<label class="block text-sm font-medium text-gray-700 mb-2">
			{label}
			{#if required}<span class="text-red-500">*</span>{/if}
		</label>
	{/if}

	<!-- Upload Area -->
	<div
		class="upload-area border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200 {dragOver
			? 'border-primary bg-primary/5'
			: 'border-gray-300 hover:border-gray-400'} {disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}"
		on:dragover={handleDragOver}
		on:dragleave={handleDragLeave}
		on:drop={handleDrop}
		on:click={() => !disabled && fileInput?.click()}
		role="button"
		tabindex="0"
		on:keydown={(e) => {
			if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
				e.preventDefault();
				fileInput?.click();
			}
		}}
	>
		{#if uploading}
			<!-- Uploading State -->
			<div class="space-y-3">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
				<p class="text-sm text-gray-600">Uploading...</p>
				{#if uploadProgress > 0}
					<div class="w-full bg-gray-200 rounded-full h-2">
						<div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: {uploadProgress}%"></div>
					</div>
				{/if}
			</div>
		{:else if currentFileUrl && showPreview}
			<!-- Preview State -->
			<div class="space-y-3">
				{#if uploadType === 'avatar' || uploadType === 'cover'}
					<img
						src={currentFileUrl}
						alt="Preview"
						class="mx-auto rounded-lg object-cover {previewSizes[previewSize]}"
					/>
				{:else}
					<div class="mx-auto {previewSizes[previewSize]} bg-gray-100 rounded-lg flex items-center justify-center">
						<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
						</svg>
					</div>
				{/if}
				<p class="text-sm text-gray-600">File uploaded successfully</p>
				<Button
					variant="outline"
					size="sm"
					on:click={(e) => {
						e.stopPropagation();
						fileInput?.click();
					}}
				>
					Change File
				</Button>
			</div>
		{:else}
			<!-- Default State -->
			<div class="space-y-3">
				<svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
					<path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				<div>
					<p class="text-sm text-gray-600">
						<span class="font-medium text-primary">Click to upload</span> or drag and drop
					</p>
					{#if uploadConfig}
						<p class="text-xs text-gray-500 mt-1">
							{getFileTypeDescription()} up to {formatFileSize(uploadConfig.maxSize)}
						</p>
					{/if}
				</div>
			</div>
		{/if}
	</div>

	<!-- Error Message -->
	{#if error}
		<p class="mt-2 text-sm text-red-600">{error}</p>
	{/if}

	<!-- Hidden File Input -->
	<input
		bind:this={fileInput}
		type="file"
		{accept}
		{disabled}
		class="hidden"
		on:change={(e) => handleFileSelect(e.currentTarget.files)}
	/>
</div>

<style>
	.upload-area:focus {
		@apply outline-2 outline-primary outline-offset-2;
	}
</style>
