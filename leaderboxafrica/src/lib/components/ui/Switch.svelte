<!-- Switch Component - Svelte 5 conversion of React switch.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		checked = $bindable(false),
		onCheckedChange,
		disabled = false,
		className = '',
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// Handle toggle
	function handleToggle() {
		if (disabled) return;
		
		checked = !checked;
		onCheckedChange?.(checked);
		dispatch('change', checked);
	}

	// Handle keyboard events
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleToggle();
		}
	}
</script>

<button
	type="button"
	role="switch"
	aria-checked={checked}
	{disabled}
	class={cn(
		"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",
		checked ? "bg-primary" : "bg-input",
		className
	)}
	on:click={handleToggle}
	on:keydown={handleKeydown}
	{...restProps}
>
	<span
		class={cn(
			"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform",
			checked ? "translate-x-5" : "translate-x-0"
		)}
	></span>
</button>
