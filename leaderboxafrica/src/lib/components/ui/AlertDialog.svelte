<!-- <PERSON>ert Dialog Component - Svelte 5 conversion of React alert-dialog.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		open = $bindable(false),
		onOpenChange,
		className = '',
		children,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// Handle open state changes
	function handleOpenChange(newOpen: boolean) {
		open = newOpen;
		onOpenChange?.(newOpen);
		dispatch('openChange', newOpen);
	}

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && open) {
			handleOpenChange(false);
		}
	}

	// Handle overlay click - Alert dialogs typically don't close on overlay click
	// but we'll include it for consistency
	function handleOverlayClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			// Alert dialogs usually require explicit action, so we won't auto-close
			// handleOpenChange(false);
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if open}
	<!-- Portal-like behavior -->
	<div class="fixed inset-0 z-50">
		<!-- Overlay -->
		<div
			class={cn(
				"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
				"animate-in fade-in-0 duration-200"
			)}
			on:click={handleOverlayClick}
			role="presentation"
		></div>

		<!-- Content -->
		<div
			class={cn(
				"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200",
				"animate-in fade-in-0 zoom-in-95 slide-in-from-left-1/2 slide-in-from-top-[48%]",
				"sm:rounded-lg",
				className
			)}
			role="alertdialog"
			aria-modal="true"
			{...restProps}
		>
			{@render children?.()}
		</div>
	</div>
{/if}
