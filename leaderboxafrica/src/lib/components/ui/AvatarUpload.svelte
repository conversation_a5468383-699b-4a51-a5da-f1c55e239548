<!-- Specialized Avatar Upload Component -->
<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import Avatar from './Avatar.svelte';

	// Props
	export let currentAvatarUrl: string = '';
	export let userName: string = '';
	export let disabled: boolean = false;
	export let required: boolean = false;
	export let size: 'sm' | 'md' | 'lg' | 'xl' = 'lg';
	export let className: string = '';

	// State
	let uploading = false;
	let error = '';
	let fileInput: HTMLInputElement;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		upload: { fileUrl: string; key: string };
		error: { error: string };
	}>();

	// Size configurations
	const sizeConfig = {
		sm: { avatar: 'h-16 w-16', container: 'w-20' },
		md: { avatar: 'h-20 w-20', container: 'w-24' },
		lg: { avatar: 'h-24 w-24', container: 'w-28' },
		xl: { avatar: 'h-32 w-32', container: 'w-36' }
	};

	// Handle file selection
	async function handleFileSelect(files: FileList | null) {
		if (!files || files.length === 0) return;

		const file = files[0];
		await uploadFile(file);
	}

	// Upload file to S3
	async function uploadFile(file: File) {
		if (!file) return;

		error = '';
		uploading = true;

		try {
			// Get presigned URL
			const presignedResponse = await fetch('/api/upload/presigned-url', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					fileName: file.name,
					fileType: file.type,
					fileSize: file.size,
					uploadType: 'avatar'
				})
			});

			const presignedData = await presignedResponse.json();

			if (!presignedData.success) {
				throw new Error(presignedData.error);
			}

			const { uploadUrl, fileUrl, key } = presignedData.data;

			// Upload file to S3
			const uploadResponse = await fetch(uploadUrl, {
				method: 'PUT',
				body: file,
				headers: {
					'Content-Type': file.type
				}
			});

			if (!uploadResponse.ok) {
				throw new Error('Failed to upload file to S3');
			}

			// Success
			currentAvatarUrl = fileUrl;
			dispatch('upload', { fileUrl, key });

		} catch (err) {
			error = err instanceof Error ? err.message : 'Upload failed';
			dispatch('error', { error });
		} finally {
			uploading = false;
		}
	}

	// Handle avatar click
	function handleAvatarClick() {
		if (!disabled) {
			fileInput?.click();
		}
	}
</script>

<div class="avatar-upload {className}">
	<div class="flex flex-col items-center space-y-4">
		<!-- Current Avatar Display -->
		<div class="relative {sizeConfig[size].container}">
			<div
				class="cursor-pointer {disabled ? 'cursor-not-allowed opacity-50' : ''}"
				on:click={handleAvatarClick}
				on:keydown={(e) => {
					if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
						e.preventDefault();
						handleAvatarClick();
					}
				}}
				role="button"
				tabindex="0"
				title={disabled ? '' : (currentAvatarUrl ? 'Click to change avatar' : 'Click to upload avatar')}
			>
				<Avatar
					src={currentAvatarUrl}
					alt={userName}
					fallback={userName ? userName.split(' ').map(n => n[0]).join('') : 'U'}
					className="{sizeConfig[size].avatar} border-4 border-white shadow-lg"
				/>

				<!-- Upload Overlay -->
				{#if !disabled}
					<div class="absolute inset-0 bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center group">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
						</svg>
					</div>
				{/if}

				<!-- Loading Overlay -->
				{#if uploading}
					<div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
						<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
					</div>
				{/if}
			</div>
		</div>

		<!-- Upload Instructions -->
		<div class="text-center">
			<p class="text-sm font-medium text-gray-700">
				{currentAvatarUrl ? 'Change Avatar' : 'Upload Avatar'}
				{#if required}<span class="text-red-500">*</span>{/if}
			</p>
			<p class="text-xs text-gray-500 mt-1">
				Click the avatar to {currentAvatarUrl ? 'change' : 'upload'} image
			</p>
		</div>

		<!-- Error Display -->
		{#if error}
			<div class="w-full max-w-xs">
				<p class="text-sm text-red-600 text-center">{error}</p>
			</div>
		{/if}

		<!-- Hidden File Input -->
		<input
			bind:this={fileInput}
			type="file"
			accept="image/*"
			{disabled}
			class="hidden"
			on:change={(e) => handleFileSelect(e.currentTarget.files)}
		/>
	</div>
</div>

<style>
	.avatar-upload {
		@apply w-full;
	}
</style>
