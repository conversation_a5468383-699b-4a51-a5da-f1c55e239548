<!-- Dropdown Menu Component - Svelte 5 conversion of React dropdown-menu.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { createEventDispatcher, setContext } from 'svelte';

	// Props
	let { 
		open = $bindable(false),
		onOpenChange,
		className = '',
		children,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// Handle open state changes
	function handleOpenChange(newOpen: boolean) {
		open = newOpen;
		onOpenChange?.(newOpen);
		dispatch('openChange', newOpen);
	}

	// Set context for child components
	setContext('dropdown', {
		get open() { return open; },
		setOpen: handleOpenChange
	});

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && open) {
			handleOpenChange(false);
		}
	}

	// Handle click outside
	function handleClickOutside(event: MouseEvent) {
		// This will be handled by individual components
	}
</script>

<svelte:window on:keydown={handleKeydown} />

<div class={cn("relative", className)} {...restProps}>
	{@render children?.()}
</div>
