<!-- Popover Content Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { getContext } from 'svelte';

	// Props
	let { 
		className = '',
		align = 'center',
		sideOffset = 4,
		children,
		...restProps 
	} = $props();

	// Get popover context
	const popoverContext = getContext('popover');
	
	// Handle click outside
	function handleClickOutside(event: MouseEvent) {
		if (popoverContext && popoverContext.open) {
			popoverContext.setOpen(false);
		}
	}

	// Position classes based on align
	const alignClasses = {
		center: 'left-1/2 transform -translate-x-1/2',
		start: 'left-0',
		end: 'right-0'
	};
</script>

{#if popoverContext?.open}
	<!-- Backdrop -->
	<div 
		class="fixed inset-0 z-40" 
		on:click={handleClickOutside}
		role="presentation"
	></div>
	
	<!-- Content -->
	<div
		class={cn(
			"absolute z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none",
			"animate-in fade-in-0 zoom-in-95 duration-200",
			alignClasses[align],
			className
		)}
		style="top: calc(100% + {sideOffset}px)"
		{...restProps}
	>
		{@render children?.()}
	</div>
{/if}
