<!-- Dropdown Menu Trigger Component -->
<script lang="ts">
	import { getContext } from 'svelte';

	// Props
	let { 
		children,
		disabled = false,
		...restProps 
	} = $props();

	// Get dropdown context
	const dropdownContext = getContext('dropdown');

	// Handle click
	function handleClick() {
		if (!disabled && dropdownContext) {
			dropdownContext.setOpen(!dropdownContext.open);
		}
	}
</script>

<div on:click={handleClick} {...restProps}>
	{@render children?.()}
</div>
