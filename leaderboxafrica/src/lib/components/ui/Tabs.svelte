<!-- Tabs Component - Svelte 5 conversion of React tabs.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { createEventDispatcher, setContext, getContext } from 'svelte';

	// Props
	let { 
		value = $bindable(''),
		onValueChange,
		className = '',
		children,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// Set context for child components
	setContext('tabs', {
		get value() { return value; },
		setValue: (newValue: string) => {
			value = newValue;
			onValueChange?.(newValue);
			dispatch('valueChange', newValue);
		}
	});
</script>

<div
	class={cn("", className)}
	{...restProps}
>
	{@render children?.()}
</div>
