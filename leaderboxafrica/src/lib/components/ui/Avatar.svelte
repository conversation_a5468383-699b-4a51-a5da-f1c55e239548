<!-- Avatar Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';

	interface Props {
		className?: string;
		src?: string;
		alt?: string;
		fallback?: string;
	}

	let { className = '', src = '', alt = '', fallback = '' }: Props = $props();
</script>

<div class={cn("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full", className)}>
	{#if src}
		<img {src} {alt} class="aspect-square h-full w-full object-cover" />
	{:else}
		<div class="flex h-full w-full items-center justify-center rounded-full bg-muted">
			<span class="text-sm font-medium text-muted-foreground">{fallback}</span>
		</div>
	{/if}
</div>
