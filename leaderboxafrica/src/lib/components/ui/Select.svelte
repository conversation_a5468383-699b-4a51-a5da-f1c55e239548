<!-- Select Component -->
<script lang="ts">
	import { cn } from '$lib/utils.ts';

	interface Props {
		value?: string;
		className?: string;
		placeholder?: string;
		options?: Array<{value: string, label: string}>;
		onChange?: (value: string) => void;
	}

	let {
		value = $bindable(''),
		className = '',
		placeholder = 'Select...',
		options = [],
		onChange
	}: Props = $props();

	let isOpen = $state(false);
	let selectElement: HTMLDivElement;

	function toggleOpen() {
		isOpen = !isOpen;
	}

	function selectOption(optionValue: string) {
		value = optionValue;
		isOpen = false;
		// Call change handler
		onChange?.(optionValue);
	}

	function handleClickOutside(event: MouseEvent) {
		if (selectElement && !selectElement.contains(event.target as Node)) {
			isOpen = false;
		}
	}

	let selectedOption = $derived(options.find(opt => opt.value === value));
</script>

<svelte:window on:click={handleClickOutside} />

<div bind:this={selectElement} class="relative">
	<button
		type="button"
		class={cn(
			"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
			className
		)}
		on:click={toggleOpen}
		{...$$restProps}
	>
		<span class={selectedOption ? '' : 'text-muted-foreground'}>
			{selectedOption ? selectedOption.label : placeholder}
		</span>
		<svg class="h-4 w-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
		</svg>
	</button>

	{#if isOpen}
		<div class="absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md mt-1 w-full">
			<div class="p-1">
				{#each options as option}
					<button
						type="button"
						class="relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
						on:click={() => selectOption(option.value)}
					>
						{#if option.value === value}
							<span class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
								<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
								</svg>
							</span>
						{/if}
						{option.label}
					</button>
				{/each}
			</div>
		</div>
	{/if}
</div>
