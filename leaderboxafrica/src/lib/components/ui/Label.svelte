<!-- Label Component - Svelte 5 conversion of React label.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';

	// Props
	let { 
		className = '',
		htmlFor = '',
		children,
		...restProps 
	} = $props();
</script>

<label
	for={htmlFor}
	class={cn(
		"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
		className
	)}
	{...restProps}
>
	{@render children?.()}
</label>
