<!-- Command Component - Svelte 5 conversion of React command.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { setContext } from 'svelte';

	// Props
	let { 
		className = '',
		filter,
		children,
		...restProps 
	} = $props();

	// State
	let searchValue = $state('');
	let items = $state([]);

	// Set context for child components
	setContext('command', {
		get searchValue() { return searchValue; },
		setSearchValue: (value: string) => { searchValue = value; },
		filter: filter || ((itemValue: string, search: string) => {
			return itemValue.toLowerCase().includes(search.toLowerCase()) ? 1 : 0;
		}),
		get items() { return items; },
		setItems: (newItems: any[]) => { items = newItems; }
	});
</script>

<div
	class={cn(
		"flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",
		className
	)}
	{...restProps}
>
	{@render children?.()}
</div>
