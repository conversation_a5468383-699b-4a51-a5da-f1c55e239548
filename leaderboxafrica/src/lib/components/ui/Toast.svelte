<!-- Toast Component for SvelteKit -->
<script>
	import { toasts } from '$lib/stores/toast.js';
	import { fly } from 'svelte/transition';

	function getToastClass(type) {
		const baseClass = 'flex items-center p-4 mb-4 text-sm rounded-lg shadow-lg';
		switch (type) {
			case 'success':
				return `${baseClass} text-green-800 bg-green-50 border border-green-200`;
			case 'error':
				return `${baseClass} text-red-800 bg-red-50 border border-red-200`;
			case 'warning':
				return `${baseClass} text-yellow-800 bg-yellow-50 border border-yellow-200`;
			case 'info':
			default:
				return `${baseClass} text-blue-800 bg-blue-50 border border-blue-200`;
		}
	}

	function getIcon(type) {
		switch (type) {
			case 'success':
				return `<svg class="flex-shrink-0 w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
				</svg>`;
			case 'error':
				return `<svg class="flex-shrink-0 w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
				</svg>`;
			case 'warning':
				return `<svg class="flex-shrink-0 w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
				</svg>`;
			case 'info':
			default:
				return `<svg class="flex-shrink-0 w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
				</svg>`;
		}
	}
</script>

<!-- Toast Container -->
<div class="fixed top-4 right-4 z-50 space-y-2">
	{#each $toasts as toast (toast.id)}
		<div 
			class={getToastClass(toast.type)}
			in:fly={{ x: 300, duration: 300 }}
			out:fly={{ x: 300, duration: 300 }}
		>
			{@html getIcon(toast.type)}
			<div class="flex-1">
				{#if toast.title}
					<div class="font-medium">{toast.title}</div>
				{/if}
				<div class={toast.title ? 'text-sm' : ''}>{toast.message}</div>
			</div>
			<button 
				class="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 items-center justify-center"
				on:click={() => toasts.remove(toast.id)}
			>
				<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
				</svg>
			</button>
		</div>
	{/each}
</div>
