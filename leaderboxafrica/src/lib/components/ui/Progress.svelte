<!-- Progress Component - Svelte 5 conversion of React progress.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';

	// Props
	let { 
		className = '',
		value = 0,
		max = 100,
		indicatorClassName = '',
		...restProps 
	} = $props();

	// Derived values
	let percentage = $derived(Math.min(Math.max((value / max) * 100, 0), 100));
</script>

<div
	class={cn(
		"relative h-4 w-full overflow-hidden rounded-full bg-secondary",
		className
	)}
	role="progressbar"
	aria-valuenow={value}
	aria-valuemax={max}
	{...restProps}
>
	<div
		class={cn("h-full w-full flex-1 bg-primary transition-all", indicatorClassName)}
		style="transform: translateX(-{100 - percentage}%)"
	></div>
</div>
