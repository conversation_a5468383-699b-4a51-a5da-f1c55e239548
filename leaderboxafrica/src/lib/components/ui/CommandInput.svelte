<!-- Command Input Component -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { Search } from 'lucide-svelte';
	import { getContext } from 'svelte';

	// Props
	let { 
		className = '',
		placeholder = 'Search...',
		...restProps 
	} = $props();

	// Get command context
	const commandContext = getContext('command');

	// Handle input change
	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		if (commandContext) {
			commandContext.setSearchValue(target.value);
		}
	}
</script>

<div class="flex items-center border-b px-3">
	<Search class="mr-2 h-4 w-4 shrink-0 opacity-50" />
	<input
		type="text"
		{placeholder}
		value={commandContext?.searchValue || ''}
		on:input={handleInput}
		class={cn(
			"flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
			className
		)}
		{...restProps}
	/>
</div>
