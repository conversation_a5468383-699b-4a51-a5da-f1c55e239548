<!-- Dialog Component - Svelte 5 conversion of React dialog.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { X } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		open = $bindable(false),
		onOpenChange,
		className = '',
		children,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// Handle open state changes
	function handleOpenChange(newOpen: boolean) {
		open = newOpen;
		onOpenChange?.(newOpen);
		dispatch('openChange', newOpen);
	}

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && open) {
			handleOpenChange(false);
		}
	}

	// Handle overlay click
	function handleOverlayClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleOpenChange(false);
		}
	}

	// Handle close button click
	function handleClose() {
		handleOpenChange(false);
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if open}
	<!-- Portal-like behavior using teleport -->
	<div class="fixed inset-0 z-50">
		<!-- Overlay -->
		<div
			class={cn(
				"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
				"animate-in fade-in-0 duration-200",
				className
			)}
			on:click={handleOverlayClick}
			role="presentation"
		></div>

		<!-- Content -->
		<div
			class={cn(
				"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200",
				"animate-in fade-in-0 zoom-in-95 slide-in-from-left-1/2 slide-in-from-top-[48%]",
				"sm:rounded-lg",
				className
			)}
			role="dialog"
			aria-modal="true"
			{...restProps}
		>
			{@render children?.()}
			
			<!-- Close button -->
			<button
				type="button"
				class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
				on:click={handleClose}
			>
				<X class="h-4 w-4" />
				<span class="sr-only">Close</span>
			</button>
		</div>
	</div>
{/if}
