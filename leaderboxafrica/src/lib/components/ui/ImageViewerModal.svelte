<!-- Image Viewer Modal Component - Svelte 5 conversion of React ImageViewerModal.jsx -->
<script lang="ts">
	import Dialog from './Dialog.svelte';
	import DialogHeader from './DialogHeader.svelte';
	import DialogTitle from './DialogTitle.svelte';
	import Button from './Button.svelte';
	import { X } from 'lucide-svelte';

	// Props
	let { 
		isOpen = $bindable(false),
		onClose,
		imageUrl = '',
		imageAlt = 'Full view',
		...restProps 
	} = $props();

	// Handle close
	function handleClose() {
		isOpen = false;
		onClose?.();
	}

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleClose();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen && imageUrl}
	<Dialog bind:open={isOpen} onOpenChange={handleClose}>
		<div 
			class="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm"
			on:click={handleClose}
			role="presentation"
		></div>
		
		<div 
			class="fixed left-[50%] top-[50%] z-50 translate-x-[-50%] translate-y-[-50%] w-[95vw] h-[90vh] max-w-6xl p-2 sm:p-4 bg-background/90 backdrop-blur-sm border border-border shadow-2xl rounded-lg"
			role="dialog"
			aria-modal="true"
			{...restProps}
		>
			<!-- Screen reader only header -->
			<div class="sr-only">
				<h2>{imageAlt}</h2>
			</div>
			
			<!-- Close button -->
			<Button 
				type="button" 
				variant="ghost" 
				size="icon" 
				class="absolute top-2 right-2 z-[60] rounded-full bg-background/70 hover:bg-background text-foreground hover:text-primary"
				on:click={handleClose}
			>
				<X class="h-5 w-5" />
			</Button>
			
			<!-- Image container -->
			<div class="flex justify-center items-center w-full h-full overflow-hidden pt-8">
				<img 
					src={imageUrl} 
					alt={imageAlt} 
					class="max-w-full max-h-full object-contain rounded-md" 
				/>
			</div>
		</div>
	</Dialog>
{/if}
