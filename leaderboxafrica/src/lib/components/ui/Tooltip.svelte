<!-- Tooltip Component - Svelte 5 conversion of React tooltip.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.ts';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		content = '',
		side = 'top',
		sideOffset = 4,
		delay = 700,
		className = '',
		children,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let isVisible = $state(false);
	let timeoutId: number | null = null;
	let triggerElement: HTMLElement;
	let tooltipElement: HTMLElement;

	// Show tooltip with delay
	function showTooltip() {
		if (timeoutId) clearTimeout(timeoutId);
		timeoutId = setTimeout(() => {
			isVisible = true;
			dispatch('show');
		}, delay);
	}

	// Hide tooltip immediately
	function hideTooltip() {
		if (timeoutId) {
			clearTimeout(timeoutId);
			timeoutId = null;
		}
		isVisible = false;
		dispatch('hide');
	}

	// Position classes based on side
	const sideClasses = {
		top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-1',
		bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-1',
		left: 'right-full top-1/2 transform -translate-y-1/2 mr-1',
		right: 'left-full top-1/2 transform -translate-y-1/2 ml-1'
	};

	// Animation classes based on side
	const animationClasses = {
		top: 'animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2',
		bottom: 'animate-in fade-in-0 zoom-in-95 slide-in-from-top-2',
		left: 'animate-in fade-in-0 zoom-in-95 slide-in-from-right-2',
		right: 'animate-in fade-in-0 zoom-in-95 slide-in-from-left-2'
	};
</script>

<div 
	class="relative inline-block"
	bind:this={triggerElement}
	on:mouseenter={showTooltip}
	on:mouseleave={hideTooltip}
	on:focus={showTooltip}
	on:blur={hideTooltip}
	{...restProps}
>
	{@render children?.()}
	
	{#if isVisible && content}
		<div
			bind:this={tooltipElement}
			class={cn(
				"absolute z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md",
				sideClasses[side],
				animationClasses[side],
				className
			)}
			style="margin-{side}: {sideOffset}px"
			role="tooltip"
		>
			{content}
		</div>
	{/if}
</div>
