<!-- Banter Card Component - Svelte 5 conversion of React BanterCard.jsx -->
<script lang="ts">
	import { cn } from '$lib/utils.js';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import { MessageSquare, ThumbsUp, Clock, Tag, ThumbsDown } from 'lucide-svelte';

	// Props
	let {
		banter,
		onTagClick,
		onBanterClick,
		onUserClick,
		...restProps
	}: {
		banter: any;
		onTagClick?: (event: { tagType: string; tagValue: string }) => void;
		onBanterClick?: (banter: any) => void;
		onUserClick?: (event: { userId: string; userName: string }) => void;
	} = $props();

	// Derived values
	let displayTags = $derived(() => {
		if (!banter?.tags) return [];
		return [
			banter.tags.leader,
			banter.tags.party,
			banter.tags.location,
			banter.tags.topic
		].filter(Boolean).slice(0, 3);
	});

	// Event handlers
	function handleTagClick(e: MouseEvent, tagType: string, tagValue: string) {
		e.preventDefault();
		e.stopPropagation();
		console.log(`Tag clicked: ${tagType} - ${tagValue}`);
		onTagClick?.({ tagType, tagValue });
		// Future: navigate to search with tag filter
	}

	function handleBanterClick() {
		onBanterClick?.(banter);
	}

	function handleUserClick(e: MouseEvent) {
		e.stopPropagation();
		onUserClick?.({ userId: banter.authorId, userName: banter.authorName });
	}

	// Helper functions
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString();
	}

	function getTagType(tagValue: string) {
		if (!banter?.tags) return '';
		return Object.keys(banter.tags).find(key => banter.tags[key] === tagValue) || '';
	}
</script>

{#if banter}
	<div
		class={cn(
			"modern-card overflow-hidden transition-all duration-300 hover:-translate-y-1 hover:shadow-lg cursor-pointer",
			"bg-card border border-border rounded-lg"
		)}
		on:click={handleBanterClick}
		role="button"
		tabindex="0"
		on:keydown={(e) => e.key === 'Enter' && handleBanterClick()}
		{...restProps}
	>
		<!-- Header -->
		<div class="modern-card-header p-4 pb-2">
			<div class="flex items-start space-x-3 mb-2">
				<button
					type="button"
					on:click={handleUserClick}
					class="flex-shrink-0"
				>
					<Avatar 
						src={banter.authorAvatarUrl || `https://avatar.vercel.sh/${banter.authorName || 'anon'}.png?size=40`}
						alt={banter.authorName}
						fallback={banter.authorName ? banter.authorName.substring(0, 1).toUpperCase() : 'A'}
						className="h-10 w-10 border-2 border-primary/20"
					/>
				</button>
				<div class="min-w-0 flex-1">
					<button
						type="button"
						on:click={handleUserClick}
						class="text-sm font-semibold text-primary hover:underline"
					>
						{banter.authorName || "Anonymous User"}
					</button>
					<p class="text-xs text-muted-foreground flex items-center">
						<Clock size={12} class="mr-1" /> 
						{formatDate(banter.createdAt)}
					</p>
				</div>
			</div>
			<h3 class="text-lg md:text-xl font-semibold text-foreground hover:text-primary transition-colors">
				{banter.title}
			</h3>
		</div>

		<!-- Content -->
		<div class="modern-card-content px-4 pb-2">
			<p class="text-sm text-muted-foreground line-clamp-3 mb-3">
				{banter.details}
			</p>
			
			{#if displayTags.length > 0}
				<div class="flex flex-wrap gap-1.5 mb-1">
					{#each displayTags as tag, index (tag)}
						<button 
							type="button"
							on:click={(e) => handleTagClick(e, getTagType(tag), tag)}
							class="px-2 py-0.5 text-xs bg-secondary text-secondary-foreground rounded-full flex items-center hover:bg-primary/20 transition-colors focus:outline-none focus:ring-1 focus:ring-primary"
						>
							<Tag size={10} class="mr-1 opacity-70" /> 
							{tag}
						</button>
					{/each}
				</div>
			{/if}
		</div>

		<!-- Footer -->
		<div class="modern-card-footer px-4 pb-4 flex justify-between items-center text-xs text-muted-foreground">
			<div class="flex items-center space-x-3">
				<span class="flex items-center">
					<ThumbsUp size={14} class="mr-0.5 text-green-500" /> 
					<span class="hidden sm:inline mr-0.5">Valid Points:</span> 
					{banter.upvotes || 0}
				</span>
				<span class="flex items-center">
					<ThumbsDown size={14} class="mr-0.5 text-red-500" /> 
					<span class="hidden sm:inline mr-0.5">Trash:</span> 
					{banter.downvotes || 0}
				</span>
				<span class="flex items-center">
					<MessageSquare size={14} class="mr-0.5 text-blue-500" /> 
					<span class="hidden sm:inline mr-0.5">Replies:</span> 
					{banter.comments?.length || 0}
				</span>
			</div>
			<span class="text-primary hover:underline font-medium">
				Join the Banter →
			</span>
		</div>
	</div>
{/if}
