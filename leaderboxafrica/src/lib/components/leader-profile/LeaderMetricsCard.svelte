<!-- Leader Metrics Card - Placeholder -->
<script>
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';

	export let leader;
</script>

<Card className="modern-card">
	<CardHeader className="modern-card-header pb-2">
		<h3 class="text-xl text-primary font-semibold">Metrics</h3>
	</CardHeader>
	<CardContent className="modern-card-content space-y-3">
		<div class="flex justify-between items-center">
			<span class="text-muted-foreground">Followers:</span>
			<span class="font-semibold text-lg">{(leader.followers || 0).toLocaleString()}</span>
		</div>
		<div class="flex justify-between items-center">
			<span class="text-muted-foreground">Avg. Rating:</span>
			<div class="flex items-center">
				<svg class="h-5 w-5 text-yellow-400 fill-yellow-400 mr-1" viewBox="0 0 20 20">
					<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
				</svg>
				<span class="font-semibold text-lg">{(leader.currentRating || 0).toFixed(1)} ({(leader.totalRatings || 0).toLocaleString()} ratings)</span>
			</div>
		</div>
		<div>
			<span class="text-muted-foreground text-sm mb-1 block">Public Sentiment:</span>
			<div class="w-full bg-muted rounded-full h-2.5 mt-1 overflow-hidden">
				<div 
					class="h-full bg-blue-500"
					style="width: {((leader.currentRating || 0) / 5) * 100}%"
				></div>
			</div>
			<p class="text-sm font-medium mt-1 text-blue-600">
				{leader.publicSentiment || 'Neutral'}
			</p>
		</div>
	</CardContent>
</Card>
