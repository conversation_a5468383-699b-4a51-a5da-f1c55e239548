<!-- Direct Message Dialog - Placeholder -->
<script>
	import Button from '$lib/components/ui/Button.svelte';

	export let isOpen = false;
	export let onClose;
	export let leaderName;
	
	let message = '';
</script>

{#if isOpen}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-white rounded-lg p-6 max-w-md w-full">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">Send Message to {leaderName}</h3>
			
			<div class="mb-6">
				<label class="block text-sm font-medium text-gray-700 mb-2">Your Message</label>
				<textarea
					bind:value={message}
					placeholder="Write your message here..."
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
					rows="4"
				></textarea>
			</div>

			<div class="flex space-x-3">
				<Button disabled={!message.trim()} className="flex-1">
					Send Message
				</Button>
				<Button variant="outline" on:click={onClose} className="flex-1">
					Cancel
				</Button>
			</div>
		</div>
	</div>
{/if}
