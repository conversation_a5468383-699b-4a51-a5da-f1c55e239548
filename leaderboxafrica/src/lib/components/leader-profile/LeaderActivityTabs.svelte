<!-- Leader Activity Tabs - Placeholder -->
<script>
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';

	interface Props {
		leader: any;
		commentsList?: Array<any>;
		user?: any;
	}

	let { leader, commentsList = [], user = null }: Props = $props();
</script>

<Card className="modern-card">
	<CardHeader className="modern-card-header">
		<h3 class="text-xl text-primary font-semibold">Activity & Comments</h3>
	</CardHeader>
	<CardContent className="modern-card-content">
		<div class="text-center py-8">
			<svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
			</svg>
			<p class="text-gray-500">Activity tabs coming soon!</p>
			<p class="text-sm text-gray-400 mt-2">Comments: {commentsList.length}</p>
		</div>
	</CardContent>
</Card>
