<!-- Leader Engagement Card - Placeholder -->
<script>
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Button from '$lib/components/ui/Button.svelte';

	interface Props {
		leaderName: string;
		isFollowing?: boolean;
		onFollowToggle: () => void;
		onRate: () => void;
		canRateAgain?: boolean;
		userRatingDetails?: any;
		onSendMessage: () => void;
	}

	let {
		leaderName,
		isFollowing = false,
		onFollowToggle,
		onRate,
		canRateAgain = true,
		userRatingDetails = null,
		onSendMessage
	}: Props = $props();
</script>

<Card className="modern-card md:col-span-2">
	<CardHeader className="modern-card-header pb-2">
		<h3 class="text-xl text-primary font-semibold">Engage with {leaderName}</h3>
	</CardHeader>
	<CardContent className="modern-card-content space-y-3">
		<div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
			<Button 
				on:click={onFollowToggle} 
				className="w-full transition-colors duration-200 ease-in-out text-base py-3 {isFollowing ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'}"
			>
				<svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					{#if isFollowing}
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
					{:else}
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
					{/if}
				</svg>
				{isFollowing ? 'Unfollow' : 'Follow'}
			</Button>
			
			<Button 
				on:click={onRate} 
				className="w-full bg-yellow-600 hover:bg-yellow-700 text-white text-base py-3"
				disabled={!canRateAgain && !!userRatingDetails}
			>
				<svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
				</svg>
				{userRatingDetails ? (canRateAgain ? 'Update Rating' : 'Rated') : 'Rate Leader'}
			</Button>
			
			<Button 
				on:click={onSendMessage}
				variant="outline"
				className="w-full text-base py-3 border-primary text-primary hover:bg-primary/10 hover:text-primary"
			>
				<svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
				</svg>
				Send Message
			</Button>
		</div>
		
		{#if userRatingDetails && !canRateAgain}
			<p class="text-xs text-center text-muted-foreground">You can update your rating in 24 hours.</p>
		{/if}
		
		{#if userRatingDetails}
			<p class="text-sm text-center text-muted-foreground mt-2">
				Your last rating: {userRatingDetails.rating} stars. 
				{#if userRatingDetails.comment}
					Comment: "{userRatingDetails.comment}"
				{/if}
			</p>
		{/if}
	</CardContent>
</Card>
