<!-- Leader Bio Card - Placeholder -->
<script>
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Button from '$lib/components/ui/Button.svelte';

	interface Props {
		leader: any;
	}

	let { leader }: Props = $props();

	let showDetailedBio = $state(false);
	
	function getYouTubeEmbedUrl(url) {
		if (!url) return null;
		let videoId = null;
		try {
			const urlObj = new URL(url);
			if (urlObj.hostname === 'youtu.be') {
				videoId = urlObj.pathname.slice(1);
			} else if (urlObj.hostname.includes('youtube.com')) {
				if (urlObj.pathname === '/watch') {
					videoId = urlObj.searchParams.get('v');
				} else if (urlObj.pathname.startsWith('/embed/')) {
					videoId = urlObj.pathname.split('/embed/')[1];
				}
			}
		} catch (e) {
			// Handle cases where the URL is just an ID
			const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
			const match = url.match(regex);
			if (match) {
				videoId = match[1];
			}
		}
		return videoId ? `https://www.youtube.com/embed/${videoId}` : null;
	}
	
	let youtubeEmbedUrl = $derived(getYouTubeEmbedUrl(leader.youtubeVideoUrl));
	let detailedBioText = $derived(leader.detailedBio || `This leader has not provided a detailed biography yet. A detailed biography provides deeper insight into a leader's career, accomplishments, and political journey.`);
</script>

<Card className="modern-card">
	<CardHeader className="modern-card-header">
		<h3 class="text-xl text-primary font-semibold">Biography & Profile</h3>
	</CardHeader>
	<CardContent className="modern-card-content space-y-4 text-foreground/90">
		
		{#if youtubeEmbedUrl}
			<div class="mb-6">
				<h4 class="text-lg font-semibold text-foreground mb-2 flex items-center">
					<svg class="mr-2 text-red-600 w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
						<path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
					</svg>
					Profile Video
				</h4>
				<div class="aspect-video rounded-lg overflow-hidden shadow-lg border border-border">
					<iframe
						width="100%"
						height="100%"
						src={youtubeEmbedUrl}
						title="{leader.name} - Profile Video"
						frameborder="0"
						allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
						allowfullscreen
					></iframe>
				</div>
			</div>
		{/if}

		<div>
			<h4 class="text-lg font-semibold text-foreground mb-1">Bio Summary</h4>
			<p class="whitespace-pre-line">{leader.bio || 'N/A'}</p>
		</div>
		
		<Button 
			variant="link" 
			on:click={() => showDetailedBio = !showDetailedBio}
			className="px-0 text-primary hover:text-primary/80"
		>
			{showDetailedBio ? 'Hide Full Bio' : 'View Full Bio'}
			<svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				{#if showDetailedBio}
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
				{:else}
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
				{/if}
			</svg>
		</Button>

		{#if showDetailedBio}
			<div class="pt-3 border-t border-border mt-2">
				<h4 class="text-lg font-semibold text-foreground mb-1">Full Biography</h4>
				<div class="whitespace-pre-line prose prose-sm dark:prose-invert max-w-none">
					{@html detailedBioText.replace(/\n/g, '<br />')}
				</div>
			</div>
		{/if}
		
		<div class="pt-3 border-t border-border mt-2">
			<h4 class="text-lg font-semibold text-foreground mb-1">Education</h4>
			<p>{leader.education || 'N/A'}</p>
		</div>
		
		<div class="pt-3 border-t border-border mt-2">
			<h4 class="text-lg font-semibold text-foreground mb-1">Background</h4>
			<p>{leader.background || 'N/A'}</p>
		</div>
	</CardContent>
</Card>
