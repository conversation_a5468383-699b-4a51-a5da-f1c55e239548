<!-- Main Layout Component - Exact replica of React MainLayout -->
<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/auth.ts';

	// UI Components
	import Button from '$lib/components/ui/Button.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';

	// Get user data from page data (provided by root layout server load)
	let user = $derived($page.data.user);

	// Reactive variables
	let currentPath = $derived($page.url.pathname);

	// Navigation items
	const baseNavItems = [
		{ path: "/", label: "Home" },
		{ path: "/polls", label: "Polls" },
		{ path: "/petitions", label: "Petitions" },
		{ path: "/banter-room", label: "Banter Room" },
		{ path: "/groups", label: "Groups" },
	];

	let desktopNavItems = $derived(user
		? [
			...baseNavItems,
			{ path: "/dashboard", label: "My Feed" },
		]
		: baseNavItems);

	const mobileNavItems = [
		{ path: "/", label: "Home", icon: "home" },
		{ path: "/polls", label: "Polls", icon: "bar-chart" },
		{ path: "/petitions", label: "Petitions", icon: "file-text" },
		{ path: "/banter-room", label: "Banter", icon: "message-circle" },
		{ path: "/groups", label: "Groups", icon: "users" },
	];

	// State
	let showUserDropdown = $state(false);
	let showNotificationDropdown = $state(false);

	// Functions
	function handleLogout() {
		authStore.logout();
		goto('/login');
	}

	function isActiveRoute(path: string): boolean {
		return currentPath === path;
	}

	function toggleUserDropdown() {
		console.log('toggleUserDropdown called, current state:', showUserDropdown);
		showUserDropdown = !showUserDropdown;
		showNotificationDropdown = false; // Close notifications when opening user dropdown
	}

	function toggleNotificationDropdown() {
		console.log('toggleNotificationDropdown called, current state:', showNotificationDropdown);
		showNotificationDropdown = !showNotificationDropdown;
		showUserDropdown = false; // Close user dropdown when opening notifications
	}

	function closeAllDropdowns() {
		showUserDropdown = false;
		showNotificationDropdown = false;
	}

	// Close dropdown when clicking outside
	function handleClickOutside(event: MouseEvent) {
		const target = event.target as HTMLElement;
		if (!target.closest('.dropdown-container')) {
			console.log('Clicking outside, closing dropdowns');
			closeAllDropdowns();
		}
	}
</script>

<svelte:window on:click={handleClickOutside} />

<div class="min-h-screen flex flex-col bg-background text-foreground">
	<!-- Desktop Header -->
	<header class="hidden md:flex header-override p-4 sticky top-0 z-50">
		<div class="container mx-auto flex justify-between items-center">
			<a href="/" class="flex items-center">
				<img src="/logo.png" alt="LeaderBox" class="h-10 w-auto" />
			</a>
			<nav class="flex space-x-6 items-center">
				{#each desktopNavItems as item}
					<a
						href={item.path}
						class="text-sm font-medium transition-colors hover:text-primary {isActiveRoute(item.path) ? 'text-primary' : 'text-muted-foreground'}"
					>
						{item.label}
					</a>
				{/each}
			</nav>
			<div class="flex items-center space-x-3">
				{#if !user}
					<Button variant="ghost" on:click={() => goto('/login')} className="text-primary hover:bg-primary/10">
						<!-- LogIn Icon -->
						<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
						</svg>
						Login
					</Button>
					<Button on:click={() => goto('/register')} className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-full px-6 py-2 text-sm">
						<!-- UserPlus Icon -->
						<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
						</svg>
						Register
					</Button>
				{:else}
					<!-- Notifications -->
					<div class="relative dropdown-container">
						<Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary relative" on:click={toggleNotificationDropdown}>
							<!-- Bell Icon -->
							<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.868 19.462A17.936 17.936 0 0112 21c2.049 0 4.028-.343 5.868-.938M6.343 6.343A8 8 0 0112 4c4.418 0 8 3.582 8 8 0 .468-.04.926-.117 1.37M6.343 6.343L4.93 4.93m1.414 1.414l-1.415 1.414M4.93 4.93l1.414 1.414" />
							</svg>
							<!-- Notification badge -->
							<span class="absolute -top-1 -right-1 flex h-3 w-3">
								<span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
								<span class="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
							</span>
						</Button>

						{#if showNotificationDropdown}
							<div class="absolute right-0 mt-2 w-80 bg-popover border border-border rounded-md shadow-lg z-50">
								<div class="p-3 border-b border-border flex justify-between items-center">
									<span class="font-medium">Notifications</span>
									<span class="text-xs text-primary">(2 new)</span>
								</div>
								<div class="max-h-48 overflow-y-auto">
									<div class="p-3 hover:bg-accent cursor-pointer border-b border-border">
										<div class="flex flex-col">
											<span class="font-medium text-sm">New Update</span>
											<span class="text-muted-foreground text-xs">Your poll has received new votes</span>
										</div>
									</div>
									<div class="p-3 hover:bg-accent cursor-pointer border-b border-border">
										<div class="flex flex-col">
											<span class="font-medium text-sm">Comment Reply</span>
											<span class="text-muted-foreground text-xs">Someone replied to your comment</span>
										</div>
									</div>
								</div>
								<div class="p-2 border-t border-border">
									<button class="w-full text-xs text-center text-muted-foreground hover:bg-accent py-1 rounded">
										Mark all as read
									</button>
								</div>
							</div>
						{/if}
					</div>

					<!-- Messages -->
					<Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary" on:click={() => goto('/messages')}>
						<!-- Mail Icon -->
						<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
						</svg>
					</Button>

					<!-- User Avatar Dropdown -->
					<div class="relative dropdown-container">
						<Button variant="ghost" className="relative h-10 w-10 rounded-full" on:click={toggleUserDropdown}>
							<Avatar
								className="h-9 w-9"
								src={user?.avatarUrl || `https://avatar.vercel.sh/${user?.name || user?.email || 'user'}.png?size=40`}
								alt={user?.name || 'User'}
								fallback={user?.name ? user.name.substring(0,1).toUpperCase() : (user?.email ? user.email.substring(0,1).toUpperCase() : 'U')}
							/>
						</Button>
						
						{#if showUserDropdown}
							<div class="absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50">
								<div class="p-3 border-b border-border">
									<div class="flex flex-col space-y-1">
										<p class="text-sm font-medium leading-none">{user?.name || "User"}</p>
										<p class="text-xs leading-none text-muted-foreground">{user?.email || ""}</p>
									</div>
								</div>
								<div class="py-1">
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto(`/user/${user?.id}`); closeAllDropdowns(); }}>
										My Profile
									</button>
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto('/dashboard'); closeAllDropdowns(); }}>
										My Feed
									</button>
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto('/settings'); closeAllDropdowns(); }}>
										Settings
									</button>
									{#if user?.isAdmin}
										<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto('/admin'); closeAllDropdowns(); }}>
											Admin Panel
										</button>
									{/if}
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { handleLogout(); closeAllDropdowns(); }}>
										Log out
									</button>
								</div>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	</header>

	<!-- Mobile Header -->
	<header class="md:hidden header-override p-3 sticky top-0 z-50">
		<div class="container mx-auto flex justify-between items-center">
			<a href="/" class="flex items-center">
				<img src="/leaderbox-logo.svg" alt="LeaderBox" class="h-8 w-auto" />
			</a>
			<div class="flex items-center space-x-1">
				{#if !user}
					<Button variant="ghost" size="sm" on:click={() => goto('/login')} className="text-primary px-2">
						<!-- LogIn Icon -->
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
						</svg>
					</Button>
					<Button variant="ghost" size="sm" on:click={() => goto('/register')} className="text-accent px-2">
						<!-- UserPlus Icon -->
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
						</svg>
					</Button>
				{:else}
					<!-- Notifications -->
					<div class="relative dropdown-container">
						<Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary relative" on:click={toggleNotificationDropdown}>
							<!-- Bell Icon -->
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.868 19.462A17.936 17.936 0 0112 21c2.049 0 4.028-.343 5.868-.938M6.343 6.343A8 8 0 0112 4c4.418 0 8 3.582 8 8 0 .468-.04.926-.117 1.37M6.343 6.343L4.93 4.93m1.414 1.414l-1.415 1.414M4.93 4.93l1.414 1.414" />
							</svg>
							<!-- Notification badge -->
							<span class="absolute -top-0.5 -right-0.5 flex h-2.5 w-2.5">
								<span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
								<span class="relative inline-flex rounded-full h-2.5 w-2.5 bg-red-500"></span>
							</span>
						</Button>

						{#if showNotificationDropdown}
							<div class="absolute right-0 mt-2 w-64 bg-popover border border-border rounded-md shadow-lg z-50">
								<div class="p-3 border-b border-border">
									<div class="text-sm font-medium">Notifications</div>
								</div>
								<div class="py-1 max-h-[150px] overflow-y-auto">
									<div class="px-3 py-2 text-xs hover:bg-accent cursor-pointer">
										<div class="font-medium">New Update</div>
										<div class="text-muted-foreground">Your poll received votes</div>
									</div>
									<div class="px-3 py-2 text-xs hover:bg-accent cursor-pointer">
										<div class="font-medium">Comment Reply</div>
										<div class="text-muted-foreground">Someone replied to you</div>
									</div>
								</div>
								<div class="border-t border-border">
									<button class="w-full px-3 py-2 text-xs text-center hover:bg-accent hover:text-accent-foreground">
										Mark all as read
									</button>
								</div>
							</div>
						{/if}
					</div>

					<!-- Messages -->
					<Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary" on:click={() => goto('/messages')}>
						<!-- Mail Icon -->
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
						</svg>
					</Button>

					<!-- User Avatar Dropdown -->
					<div class="relative dropdown-container">
						<Button variant="ghost" className="relative h-9 w-9 rounded-full p-0" on:click={toggleUserDropdown}>
							<Avatar
								className="h-8 w-8"
								src={user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`}
								alt={user.name}
								fallback={user.name ? user.name.substring(0,1).toUpperCase() : user.email.substring(0,1).toUpperCase()}
							/>
						</Button>

						{#if showUserDropdown}
							<div class="absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50">
								<div class="p-3 border-b border-border">
									<div class="flex flex-col space-y-1">
										<p class="text-sm font-medium leading-none">{user.name || "User"}</p>
										<p class="text-xs leading-none text-muted-foreground">{user.email}</p>
									</div>
								</div>
								<div class="py-1">
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto(`/user/${user.id}`); closeAllDropdowns(); }}>
										My Profile
									</button>
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto('/dashboard'); closeAllDropdowns(); }}>
										My Feed
									</button>
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto('/settings'); closeAllDropdowns(); }}>
										Settings
									</button>
									{#if user.isAdmin}
										<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { goto('/admin'); closeAllDropdowns(); }}>
											Admin Panel
										</button>
									{/if}
									<button class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground" on:click={() => { handleLogout(); closeAllDropdowns(); }}>
										Log out
									</button>
								</div>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	</header>

	<!-- Main Content -->
	<main class="flex-grow page-container pb-[calc(theme(spacing.24)+60px)] md:pb-8">
		<slot />
	</main>

	<!-- Mobile Bottom Navigation -->
	<nav class="md:hidden fixed bottom-0 left-0 right-0 mobile-nav-override p-2 flex justify-around items-center z-40">
		{#each mobileNavItems as item}
			<a 
				href={item.path}
				class="flex flex-col items-center p-1 rounded-md transition-colors w-1/5 {isActiveRoute(item.path) ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}"
			>
				<!-- Icons for mobile nav -->
				{#if item.icon === 'home'}
					<svg class="h-5 w-5 {isActiveRoute(item.path) ? 'stroke-[2.5]' : 'stroke-2'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
					</svg>
				{:else if item.icon === 'bar-chart'}
					<svg class="h-5 w-5 {isActiveRoute(item.path) ? 'stroke-[2.5]' : 'stroke-2'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
					</svg>
				{:else if item.icon === 'file-text'}
					<svg class="h-5 w-5 {isActiveRoute(item.path) ? 'stroke-[2.5]' : 'stroke-2'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
					</svg>
				{:else if item.icon === 'message-circle'}
					<svg class="h-5 w-5 {isActiveRoute(item.path) ? 'stroke-[2.5]' : 'stroke-2'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
					</svg>
				{:else if item.icon === 'users'}
					<svg class="h-5 w-5 {isActiveRoute(item.path) ? 'stroke-[2.5]' : 'stroke-2'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
					</svg>
				{/if}
				<span class="mt-0.5 text-[11px] text-center leading-tight whitespace-nowrap {isActiveRoute(item.path) ? 'font-semibold' : 'font-normal'}">{item.label}</span>
			</a>
		{/each}
	</nav>

	<!-- Desktop Footer -->
	<footer class="hidden md:block footer-override p-6 text-center">
		<div class="container mx-auto flex flex-col md:flex-row justify-between items-center">
			<p class="text-xs mb-4 md:mb-0">&copy; {new Date().getFullYear()} LeaderBox. All rights reserved.</p>
			<div class="flex flex-wrap justify-center items-center space-x-4">
				<a href="/about" class="text-sm hover:text-primary transition-colors">About</a>
				<a href="/contact" class="text-sm hover:text-primary transition-colors">Contact</a>
				<a href="/terms" class="text-sm hover:text-primary transition-colors">Terms</a>
				<a href="/privacy" class="text-sm hover:text-primary transition-colors">Privacy</a>
				<a href="https://facebook.com" target="_blank" rel="noopener noreferrer" class="text-sm hover:text-primary transition-colors">Facebook</a>
				<a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="text-sm hover:text-primary transition-colors">Twitter</a>
				<a href="https://instagram.com" target="_blank" rel="noopener noreferrer" class="text-sm hover:text-primary transition-colors">Instagram</a>
			</div>
		</div>
	</footer>
</div>
