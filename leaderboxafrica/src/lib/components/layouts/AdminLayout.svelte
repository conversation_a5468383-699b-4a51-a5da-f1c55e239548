<!-- Admin Layout Component - Svelte 5 conversion of React AdminLayout.jsx -->
<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import Button from '$lib/components/ui/Button.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import Sheet from '$lib/components/ui/Sheet.svelte';
	import SheetHeader from '$lib/components/ui/SheetHeader.svelte';
	import SheetTitle from '$lib/components/ui/SheetTitle.svelte';
	import DropdownMenu from '$lib/components/ui/DropdownMenu.svelte';
	import DropdownMenuTrigger from '$lib/components/ui/DropdownMenuTrigger.svelte';
	import DropdownMenuContent from '$lib/components/ui/DropdownMenuContent.svelte';
	import DropdownMenuItem from '$lib/components/ui/DropdownMenuItem.svelte';
	import {
		LayoutDashboard,
		Users,
		UserCheck,
		BarChart2,
		MessageSquare,
		ShieldCheck,
		FileText,
		Settings,
		Menu,
		Bell,
		Mail,
		Lightbulb,
		UserPlus,
		Tag,
		BookOpen,
		Group,
	} from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		user,
		children,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let isMobileMenuOpen = $state(false);

	// Navigation configuration
	const navLinks = [
		{ to: '/admin', icon: LayoutDashboard, text: 'Dashboard' },
		{
			category: 'Content Management',
			links: [
				{ to: '/admin/leaders', icon: UserCheck, text: 'Leaders' },
				{ to: '/admin/suggested-leaders', icon: UserPlus, text: 'Suggested Leaders' },
				{ to: '/admin/polls', icon: BarChart2, text: 'Polls' },
				{ to: '/admin/petitions', icon: BookOpen, text: 'Petitions' },
				{ to: '/admin/banters', icon: MessageSquare, text: 'Banter Room' },
				{ to: '/admin/groups', icon: Group, text: 'Groups' },
				{ to: '/admin/profile-categories', icon: Tag, text: 'Profile Categories' },
			],
		},
		{
			category: 'User & Engagement',
			links: [
				{ to: '/admin/registered-users', icon: Users, text: 'Registered Users' },
				{ to: '/admin/moderation', icon: ShieldCheck, text: 'Moderation' },
				{ to: '/admin/suggested-edits', icon: Lightbulb, text: 'Suggested Edits' },
				{ to: '/admin/metrics', icon: FileText, text: 'Engagement Metrics' },
			],
		},
		{
			category: 'System',
			links: [
				{ to: '/admin/keywords', icon: Tag, text: 'Keyword Management' },
				{ to: '/admin/email-settings', icon: Mail, text: 'Email Settings' },
			],
		},
	];

	// Derived values
	let currentPath = $derived($page.url.pathname);

	// Helper functions
	function isActiveRoute(path: string): boolean {
		if (path === '/admin') {
			return currentPath === '/admin';
		}
		return currentPath === path || currentPath.startsWith(path + '/');
	}

	// Event handlers
	function handleLogout() {
		dispatch('logout');
	}

	function handleNavigate(path: string) {
		goto(path);
		isMobileMenuOpen = false;
	}

	function toggleMobileMenu() {
		isMobileMenuOpen = !isMobileMenuOpen;
	}
</script>

<div class="min-h-screen w-full flex bg-background" {...restProps}>
	<!-- Desktop Sidebar -->
	<aside class="hidden lg:block w-64 bg-card border-r border-border p-4">
		<div class="flex flex-col h-full">
			<!-- Logo -->
			<div class="mb-6">
				<a href="/" class="flex items-center gap-2">
					<img 
						alt="LeaderBox Logo" 
						class="h-8 w-auto" 
						src="https://images.unsplash.com/photo-1485803278843-6547a34a0d1b" 
					/>
					<span class="text-xl font-bold text-primary">LeaderBox Admin</span>
				</a>
			</div>

			<!-- Navigation -->
			<nav class="flex-grow space-y-4 overflow-y-auto">
				{#each navLinks as item}
					{#if item.category}
						<div>
							<h3 class="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
								{item.category}
							</h3>
							<div class="space-y-1">
								{#each item.links as link}
									<button
										type="button"
										on:click={() => handleNavigate(link.to)}
										class={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors ${
											isActiveRoute(link.to)
												? 'bg-primary text-primary-foreground'
												: 'text-muted-foreground hover:text-foreground hover:bg-secondary'
										}`}
									>
										<svelte:component this={link.icon} size={18} />
										{link.text}
									</button>
								{/each}
							</div>
						</div>
					{:else}
						<button
							type="button"
							on:click={() => handleNavigate(item.to)}
							class={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors ${
								isActiveRoute(item.to)
									? 'bg-primary text-primary-foreground'
									: 'text-muted-foreground hover:text-foreground hover:bg-secondary'
							}`}
						>
							<svelte:component this={item.icon} size={18} />
							{item.text}
						</button>
					{/if}
				{/each}
			</nav>
		</div>
	</aside>

	<!-- Main Content Area -->
	<div class="flex flex-col flex-1">
		<!-- Header -->
		<header class="sticky top-0 z-40 flex h-16 items-center justify-between lg:justify-end gap-4 border-b bg-card px-4 md:px-6">
			<!-- Mobile Menu Button -->
			<Sheet bind:open={isMobileMenuOpen}>
				<Button 
					variant="outline" 
					size="icon" 
					class="lg:hidden"
					on:click={toggleMobileMenu}
				>
					<Menu class="h-5 w-5" />
					<span class="sr-only">Toggle navigation menu</span>
				</Button>

				<!-- Mobile Sidebar Content -->
				{#if isMobileMenuOpen}
					<div class="fixed inset-0 z-50 lg:hidden">
						<div class="fixed inset-0 bg-background/80 backdrop-blur-sm" on:click={() => isMobileMenuOpen = false}></div>
						<div class="fixed left-0 top-0 h-full w-64 bg-card border-r border-border p-4">
							<SheetHeader>
								<SheetTitle>Navigation</SheetTitle>
							</SheetHeader>
							<nav class="mt-6 space-y-4">
								{#each navLinks as item}
									{#if item.category}
										<div>
											<h3 class="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
												{item.category}
											</h3>
											<div class="space-y-1">
												{#each item.links as link}
													<button
														type="button"
														on:click={() => handleNavigate(link.to)}
														class={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors ${
															isActiveRoute(link.to)
																? 'bg-primary text-primary-foreground'
																: 'text-muted-foreground hover:text-foreground hover:bg-secondary'
														}`}
													>
														<svelte:component this={link.icon} size={18} />
														{link.text}
													</button>
												{/each}
											</div>
										</div>
									{:else}
										<button
											type="button"
											on:click={() => handleNavigate(item.to)}
											class={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors ${
												isActiveRoute(item.to)
													? 'bg-primary text-primary-foreground'
													: 'text-muted-foreground hover:text-foreground hover:bg-secondary'
											}`}
										>
											<svelte:component this={item.icon} size={18} />
											{item.text}
										</button>
									{/if}
								{/each}
							</nav>
						</div>
					</div>
				{/if}
			</Sheet>

			<!-- Header Actions -->
			<div class="flex items-center gap-4">
				<!-- Notifications -->
				<Button variant="ghost" size="icon" class="rounded-full">
					<Bell class="h-5 w-5" />
					<span class="sr-only">Toggle notifications</span>
				</Button>

				<!-- User Menu -->
				<DropdownMenu>
					<DropdownMenuTrigger>
						<Button variant="ghost" class="relative h-10 w-10 rounded-full">
							<Avatar 
								src={user?.avatarUrl}
								alt={user?.name}
								fallback={user?.name ? user.name.substring(0, 1).toUpperCase() : 'A'}
								className="h-10 w-10"
							/>
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<div class="px-2 py-1.5 text-sm font-medium">My Account</div>
						<div class="h-px bg-border my-1"></div>
						<DropdownMenuItem onClick={() => goto('/settings')}>
							Settings
						</DropdownMenuItem>
						<DropdownMenuItem onClick={() => goto('/')}>
							View Site
						</DropdownMenuItem>
						<div class="h-px bg-border my-1"></div>
						<DropdownMenuItem onClick={handleLogout}>
							Logout
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</div>
		</header>

		<!-- Main Content -->
		<main class="flex-1 p-4 md:p-6 lg:p-8">
			{@render children?.()}
		</main>
	</div>
</div>
