<!-- Register Page - Production Ready with S3 Integration -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/auth.js';
	import { toast } from '$lib/stores/toast.js';
	import AvatarUpload from '$lib/components/ui/AvatarUpload.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Select from '$lib/components/ui/Select.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import { Eye, EyeOff, User, Mail, Phone, MapPin, Users } from 'lucide-svelte';

	// Nigerian states and political parties
	const nigerianStates = [
		"Abia", "Adamawa", "Akwa Ibom", "Anamb<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", 
		"<PERSON><PERSON>", "Cross River", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Enugu", 
		"<PERSON>mbe", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kaduna", "Kano", "Katsina", "Kebbi", 
		"Kogi", "Kwara", "Lagos", "Nasarawa", "Niger", "Ogun", "Ondo", 
		"Osun", "Oyo", "Plateau", "Rivers", "Sokoto", "Taraba", "Yobe", "Zamfara", 
		"Federal Capital Territory"
	];

	const politicalParties = ["APC", "PDP", "LP", "NNPP", "Neutral"];

	// Form state using Svelte 5 runes
	let step = $state(1);
	let loading = $state(false);
	let error = $state('');
	let uploadingAvatar = $state(false);

	// Form data
	let formData = $state({
		email: '',
		password: '',
		confirmPassword: '',
		fullName: '',
		phone: '',
		state: '',
		lga: '', 
		gender: '',
		party: '',
		avatarUrl: '',
		bio: ''
	});

	// Password visibility
	let showPassword = $state(false);
	let showConfirmPassword = $state(false);

	// Validation state
	let validationErrors = $state<Record<string, string>>({});

	// Validation functions
	function validateStep1(): boolean {
		const errors: Record<string, string> = {};
		
		if (!formData.email) {
			errors.email = 'Email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = 'Please enter a valid email address';
		}
		
		if (!formData.password) {
			errors.password = 'Password is required';
		} else if (formData.password.length < 8) {
			errors.password = 'Password must be at least 8 characters';
		}
		
		if (!formData.confirmPassword) {
			errors.confirmPassword = 'Please confirm your password';
		} else if (formData.password !== formData.confirmPassword) {
			errors.confirmPassword = 'Passwords do not match';
		}
		
		if (!formData.fullName) {
			errors.fullName = 'Full name is required';
		}
		
		validationErrors = errors;
		return Object.keys(errors).length === 0;
	}

	function validateStep2(): boolean {
		const errors: Record<string, string> = {};
		
		if (!formData.phone) {
			errors.phone = 'Phone number is required';
		} else if (!/^(\+234|0)[789]\d{9}$/.test(formData.phone)) {
			errors.phone = 'Please enter a valid Nigerian phone number';
		}
		
		if (!formData.state) {
			errors.state = 'State is required';
		}
		
		if (!formData.gender) {
			errors.gender = 'Gender is required';
		}
		
		if (!formData.party) {
			errors.party = 'Political affiliation is required';
		}
		
		validationErrors = errors;
		return Object.keys(errors).length === 0;
	}

	// Avatar upload handlers
	function handleAvatarUpload(url: string) {
		formData.avatarUrl = url;
		uploadingAvatar = false;
		toast.success('Profile picture uploaded successfully!');
	}

	function handleAvatarError(errorMessage: string) {
		uploadingAvatar = false;
		toast.error(`Upload failed: ${errorMessage}`);
	}

	// Navigation functions
	function nextStep() {
		if (step === 1 && validateStep1()) {
			step = 2;
		} else if (step === 2 && validateStep2()) {
			step = 3;
		}
	}

	function prevStep() {
		if (step > 1) {
			step--;
		}
	}

	// Form submission with production database integration
	async function handleSubmit() {
		if (!validateStep2()) {
			return;
		}

		loading = true;
		error = '';

		try {
			// Submit registration to production API
			const response = await fetch('/api/auth/register', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					email: formData.email,
					password: formData.password,
					fullName: formData.fullName,
					phone: formData.phone,
					state: formData.state,
					lga: formData.lga,
					gender: formData.gender,
					party: formData.party,
					avatarUrl: formData.avatarUrl,
					bio: formData.bio
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.message || 'Registration failed');
			}

			// Success
			toast.success('Welcome to LeaderBox! Please check your email to verify your account.');
			
			// Redirect to login after short delay
			setTimeout(() => {
				goto('/login?message=registration-success');
			}, 2000);

		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Registration failed. Please try again.';
			error = errorMessage;
			toast.error(errorMessage);
		} finally {
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>Join LeaderBox - Register</title>
	<meta name="description" content="Create your LeaderBox account" />
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
	<Card className="w-full max-w-lg bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-xl">
		<CardHeader className="text-center space-y-2">
			<h1 class="text-3xl font-bold text-gray-900 dark:text-white">Join LeaderBox</h1>
			<p class="text-gray-600 dark:text-gray-400">Create your account to start connecting with leaders</p>
			
			<!-- Progress indicator -->
			<div class="flex items-center justify-center space-x-2 pt-4">
				{#each Array(3) as _, i}
					<div class="h-2 w-8 rounded-full {i < step ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-600'}"></div>
				{/each}
			</div>
			<p class="text-sm text-gray-500 dark:text-gray-400">Step {step} of 3</p>
		</CardHeader>

		<CardContent className="space-y-6">
			<!-- Error message -->
			{#if error}
				<div class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-sm text-red-600 dark:text-red-400">
					{error}
				</div>
			{/if}

			<form on:submit|preventDefault={handleSubmit} class="space-y-6">
				<!-- Step 1: Email & Password -->
				{#if step === 1}
					<div class="space-y-6">
						<div class="text-center">
							<Mail class="mx-auto h-12 w-12 text-primary mb-2" />
							<p class="text-gray-600 dark:text-gray-400 text-sm">Let's start with your email and password</p>
						</div>
						
						<div class="space-y-4">
							<div>
								<label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address</label>
								<Input
									id="email"
									type="email"
									placeholder="<EMAIL>"
									bind:value={formData.email}
									className="w-full"
								/>
								{#if validationErrors.email}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.email}</p>
								{/if}
							</div>
							
							<div>
								<label for="fullName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Full Name</label>
								<Input
									id="fullName"
									type="text"
									placeholder="Your full name"
									bind:value={formData.fullName}
									className="w-full"
								/>
								{#if validationErrors.fullName}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.fullName}</p>
								{/if}
							</div>
							
							<div class="relative">
								<label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password</label>
								<Input
									id="password"
									type={showPassword ? "text" : "password"}
									placeholder="••••••••"
									bind:value={formData.password}
									className="w-full pr-10"
								/>
								<button
									type="button"
									onclick={() => showPassword = !showPassword}
									class="absolute right-3 top-9 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
								>
									{#if showPassword}
										<EyeOff size={20} />
									{:else}
										<Eye size={20} />
									{/if}
								</button>
								{#if validationErrors.password}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.password}</p>
								{/if}
							</div>
							
							<div class="relative">
								<label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Confirm Password</label>
								<Input
									id="confirmPassword"
									type={showConfirmPassword ? "text" : "password"}
									placeholder="••••••••"
									bind:value={formData.confirmPassword}
									className="w-full pr-10"
								/>
								<button
									type="button"
									onclick={() => showConfirmPassword = !showConfirmPassword}
									class="absolute right-3 top-9 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
								>
									{#if showConfirmPassword}
										<EyeOff size={20} />
									{:else}
										<Eye size={20} />
									{/if}

				<!-- Step 2: Location & Contact -->
				{#if step === 2}
					<div class="space-y-6">
						<div class="text-center">
							<MapPin class="mx-auto h-12 w-12 text-primary mb-2" />
							<p class="text-gray-600 dark:text-gray-400 text-sm">Tell us where you're from and how to reach you</p>
						</div>

						<div class="space-y-4">
							<div>
								<label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number</label>
								<Input
									id="phone"
									type="tel"
									placeholder="+234 xxx xxx xxxx"
									bind:value={formData.phone}
									className="w-full"
								/>
								{#if validationErrors.phone}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.phone}</p>
								{/if}
							</div>

							<div>
								<label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">State</label>
								<Select
									bind:value={formData.state}
									options={nigerianStates.map(state => ({ value: state, label: state }))}
									placeholder="Select your state"
									className="w-full"
								/>
								{#if validationErrors.state}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.state}</p>
								{/if}
							</div>

							<div>
								<label for="lga" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Local Government Area</label>
								<Input
									id="lga"
									type="text"
									placeholder="Your LGA"
									bind:value={formData.lga}
									className="w-full"
								/>
							</div>

							<div>
								<label for="gender" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Gender</label>
								<Select
									bind:value={formData.gender}
									options={[
										{ value: 'male', label: 'Male' },
										{ value: 'female', label: 'Female' },
										{ value: 'other', label: 'Other' },
										{ value: 'prefer-not-to-say', label: 'Prefer not to say' }
									]}
									placeholder="Select gender"
									className="w-full"
								/>
								{#if validationErrors.gender}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.gender}</p>
								{/if}
							</div>

							<div>
								<label for="party" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Political Affiliation</label>
								<Select
									bind:value={formData.party}
									options={politicalParties.map(party => ({ value: party, label: party }))}
									placeholder="Select political affiliation"
									className="w-full"
								/>
								{#if validationErrors.party}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.party}</p>
								{/if}
							</div>
						</div>
					</div>
				{/if}

				<!-- Step 3: Profile Picture & Bio -->
				{#if step === 3}
					<div class="space-y-6">
						<div class="text-center">
							<User class="mx-auto h-12 w-12 text-primary mb-2" />
							<p class="text-gray-600 dark:text-gray-400 text-sm">Complete your profile with a picture and bio</p>
						</div>

						<div class="space-y-4">
							<div>
								<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Profile Picture</label>
								<AvatarUpload
									currentAvatarUrl={formData.avatarUrl}
									userName={formData.fullName}
									onUpload={handleAvatarUpload}
									onError={handleAvatarError}
									size="xl"
									className="mx-auto"
								/>
								<p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
									Upload a profile picture to help others recognize you
								</p>
							</div>

							<div>
								<label for="bio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bio (Optional)</label>
								<textarea
									id="bio"
									placeholder="Tell us a bit about yourself..."
									bind:value={formData.bio}
									rows="3"
									class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
								></textarea>
								<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
									Share your interests, background, or what brings you to LeaderBox
								</p>
							</div>
						</div>
					</div>
				{/if}

				<!-- Navigation buttons -->
				<div class="flex justify-between pt-6">
					{#if step > 1}
						<Button
							type="button"
							variant="outline"
							onclick={prevStep}
							className="flex items-center gap-2"
						>
							← Back
						</Button>
					{:else}
						<div></div>
					{/if}

					{#if step < 3}
						<Button
							type="button"
							onclick={nextStep}
							className="flex items-center gap-2"
						>
							Next →
						</Button>
					{:else}
						<Button
							type="submit"
							disabled={loading || uploadingAvatar}
							className="flex items-center gap-2"
						>
							{#if loading}
								<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
								Creating Account...
							{:else}
								Create Account
							{/if}
						</Button>
					{/if}
				</div>
			</form>

			<div class="text-center mt-6">
				<p class="text-gray-600 dark:text-gray-400 text-sm">
					Already have an account?
					<a href="/login" class="text-primary hover:underline font-medium">Sign in here</a>
				</p>
			</div>
		</CardContent>
	</Card>
</div>
								</button>
								{#if validationErrors.confirmPassword}
									<p class="text-sm text-red-600 dark:text-red-400 mt-1">{validationErrors.confirmPassword}</p>
								{/if}
							</div>
						</div>
					</div>
				{/if}
