<!-- Register Page - Migrated from React RegisterPage.jsx -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/auth.js';

	// Nigerian states and political parties
	const nigerianStates = [
		"Abia", "Adamawa", "Akwa Ibom", "Anambra", "Bauchi", "Bayelsa", "Benue", 
		"Borno", "Cross River", "Delta", "Ebonyi", "Edo", "Ekiti", "Enugu", 
		"Gombe", "Imo", "Jigawa", "Kaduna", "Kano", "Katsina", "Kebbi", 
		"Kogi", "Kwara", "Lagos", "Nasarawa", "Niger", "Ogun", "Ondo", 
		"Osun", "Oyo", "Plateau", "Rivers", "Sokoto", "Taraba", "Yobe", "Zamfara", 
		"Federal Capital Territory"
	];

	const politicalParties = ["APC", "PDP", "LP", "NNPP", "Neutral"];

	// Form state
	let step = 1;
	let loading = false;
	let error = '';

	// Form data
	let formData = {
		email: '',
		password: '',
		confirmPassword: '',
		fullName: '',
		phone: '',
		state: '',
		lga: '', 
		gender: '',
		party: '',
		avatarFile: null as File | null,
		avatarPreview: ''
	};

	// Password visibility
	let showPassword = false;
	let showConfirmPassword = false;

	// Input classes for consistent styling
	const inputClasses = "w-full px-4 py-3 bg-gray-200 text-black placeholder:text-gray-600 focus:bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200";

	function handleChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const { name, value } = target;
		formData = { ...formData, [name]: value };
	}

	function handleSelectChange(name: string, value: string) {
		formData = { ...formData, [name]: value };
	}

	function handleAvatarChange(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files[0]) {
			const file = target.files[0];
			formData.avatarFile = file;
			formData.avatarPreview = URL.createObjectURL(file);
		}
	}

	function showToast(title: string, description: string, variant: 'success' | 'error' = 'error') {
		error = variant === 'error' ? description : '';
		// In a real app, you'd use a toast library here
		console.log(`${title}: ${description}`);
	}

	async function handleNextStep() {
		error = '';

		if (step === 1) { // Email & Password
			if (!formData.email || !formData.password || !formData.confirmPassword) {
				showToast("Hold Up!", "Gotta fill in your email and password deets!");
				return;
			}
			if (formData.password !== formData.confirmPassword) {
				showToast("Oops!", "Your passwords aren't matching. Try again!");
				return;
			}
			if (!/\S+@\S+\.\S+/.test(formData.email)) {
				showToast("Hmm...", "That email looks a bit off. Check it?");
				return;
			}
		}
		if (step === 2) { // Full Name & Phone
			if (!formData.fullName.trim()) {
				showToast("Just a sec!", "We need your full name to know who you are!");
				return;
			}
		}
		step = step + 1;
	}

	function handlePrevStep() {
		step = step - 1;
	}

	async function handleSubmit(event: Event) {
		event.preventDefault();
		
		if (step === 3 && (!formData.state || !formData.lga.trim())) {
			showToast("Almost there!", "Where you dey represent? (State & LGA)");
			return;
		}
		if (step === 4 && (!formData.gender || !formData.party)) {
			showToast("Last few things!", "Please select your gender and party affiliation.");
			return;
		}

		loading = true;
		error = '';

		try {
			const registeredUser = await authStore.register({ 
				email: formData.email, 
				password: formData.password,
				name: formData.fullName
			});
			
			if (registeredUser.success) {
				await authStore.completeOnboarding({
					name: formData.fullName,
					phone: formData.phone,
					state: formData.state,
					lga: formData.lga,
					gender: formData.gender,
					party: formData.party,
					avatarUrl: formData.avatarPreview || '', 
				});
				
				showToast("You're In!", "Welcome to the LeaderBox fam! Get ready to connect.", "success");
				
				if(formData.avatarPreview){
					goto('/dashboard');
				} else {
					goto('/welcome/follow-suggestions');
				}
			} else {
				showToast("Registration Error", registeredUser.error || "Something went wrong. Please try again.");
			}
		} catch (err) {
			showToast("Registration Error", "Something went wrong. Please try again.");
		} finally {
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>Join LeaderBox - Register</title>
	<meta name="description" content="Create your LeaderBox account" />
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-900 p-4" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;">
	<div class="w-full max-w-md bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl shadow-2xl p-8 text-white">
		<div class="text-center mb-4">
			<h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-green-400">Join LeaderBox</h1>
			<p class="text-gray-400 mt-2">Create your account to start connecting.</p>
		</div>
		
		<div class="flex items-center my-6">
			<div class="flex-1 border-t border-gray-700"></div>
			<span class="px-4 text-xs text-gray-500 uppercase">Step {step} of 5</span>
			<div class="flex-1 border-t border-gray-700"></div>
		</div>

		<!-- Error message -->
		{#if error}
			<div class="mb-4 p-3 bg-red-500/20 border border-red-500/50 rounded-md text-sm text-red-300">
				{error}
			</div>
		{/if}

		<form on:submit={handleSubmit} class="space-y-6">
			<!-- Step 1: Email & Password -->
			{#if step === 1}
				<div class="space-y-6">
					<p class="text-center text-gray-300 text-sm">First up, let's get your login sorted. Secure and simple!</p>
					<div>
						<label for="email" class="block text-sm font-medium text-gray-300 mb-1">Your Email Address</label>
						<input
							id="email"
							name="email"
							type="email"
							placeholder="<EMAIL>"
							bind:value={formData.email}
							on:input={handleChange}
							class={inputClasses}
							required
						/>
					</div>
					<div class="relative">
						<label for="password" class="block text-sm font-medium text-gray-300 mb-1">Choose a Password</label>
						<input
							id="password"
							name="password"
							type={showPassword ? "text" : "password"}
							placeholder="••••••••"
							bind:value={formData.password}
							on:input={handleChange}
							class={inputClasses}
							required
						/>
						<button
							type="button"
							on:click={() => showPassword = !showPassword}
							class="absolute bottom-1 right-1 h-7 w-7 text-gray-500 hover:text-black rounded p-1"
						>
							{#if showPassword}
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
								</svg>
							{:else}
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
								</svg>
							{/if}
						</button>
					</div>
					<div class="relative">
						<label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-1">Confirm Your Password</label>
						<input
							id="confirmPassword"
							name="confirmPassword"
							type={showConfirmPassword ? "text" : "password"}
							placeholder="••••••••"
							bind:value={formData.confirmPassword}
							on:input={handleChange}
							class={inputClasses}
							required
						/>
						<button
							type="button"
							on:click={() => showConfirmPassword = !showConfirmPassword}
							class="absolute bottom-1 right-1 h-7 w-7 text-gray-500 hover:text-black rounded p-1"
						>
							{#if showConfirmPassword}
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
								</svg>
							{:else}
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
								</svg>
							{/if}
						</button>
					</div>
				</div>
			{/if}

			<!-- Step 2: Full Name & Phone -->
			{#if step === 2}
				<div class="space-y-6">
					<p class="text-center text-gray-300 text-sm">Great! Now, let's get to know you a bit better.</p>
					<div>
						<label for="fullName" class="block text-sm font-medium text-gray-300 mb-1">Full Name</label>
						<input
							id="fullName"
							name="fullName"
							type="text"
							placeholder="e.g., Bisi Adekunle"
							bind:value={formData.fullName}
							on:input={handleChange}
							class={inputClasses}
							required
						/>
					</div>
					<div>
						<label for="phone" class="block text-sm font-medium text-gray-300 mb-1">Phone Number (Optional)</label>
						<input
							id="phone"
							name="phone"
							type="tel"
							placeholder="08012345678"
							bind:value={formData.phone}
							on:input={handleChange}
							class={inputClasses}
						/>
					</div>
				</div>
			{/if}

			<!-- Step 3: Location -->
			{#if step === 3}
				<div class="space-y-6">
					<p class="text-center text-gray-300 text-sm">Your location helps us tailor content for you.</p>
					<div>
						<label for="state" class="block text-sm font-medium text-gray-300 mb-1">State of Residence</label>
						<select
							id="state"
							name="state"
							bind:value={formData.state}
							on:change={handleChange}
							class={inputClasses}
							required
						>
							<option value="">Select your state</option>
							{#each nigerianStates as state}
								<option value={state}>{state}</option>
							{/each}
						</select>
					</div>
					<div>
						<label for="lga" class="block text-sm font-medium text-gray-300 mb-1">LGA (Local Government Area)</label>
						<input
							id="lga"
							name="lga"
							type="text"
							placeholder="e.g., Ikeja"
							bind:value={formData.lga}
							on:input={handleChange}
							class={inputClasses}
							required
						/>
					</div>
				</div>
			{/if}

			<!-- Step 4: Gender & Party -->
			{#if step === 4}
				<div class="space-y-6">
					<p class="text-center text-gray-300 text-sm">Just a couple more details to complete your profile.</p>
					<div>
						<label for="gender" class="block text-sm font-medium text-gray-300 mb-1">Gender</label>
						<select
							id="gender"
							name="gender"
							bind:value={formData.gender}
							on:change={handleChange}
							class={inputClasses}
							required
						>
							<option value="">Select your gender</option>
							<option value="Male">Male</option>
							<option value="Female">Female</option>
							<option value="Prefer not to say">Prefer not to say</option>
						</select>
					</div>
					<div>
						<label for="party" class="block text-sm font-medium text-gray-300 mb-1">Political Affiliation</label>
						<select
							id="party"
							name="party"
							bind:value={formData.party}
							on:change={handleChange}
							class={inputClasses}
							required
						>
							<option value="">Select your party</option>
							{#each politicalParties as party}
								<option value={party}>{party}</option>
							{/each}
						</select>
					</div>
				</div>
			{/if}

			<!-- Step 5: Avatar -->
			{#if step === 5}
				<div class="space-y-6 flex flex-col items-center">
					<p class="text-center text-gray-300 text-sm">Last step! Add a profile picture to stand out.</p>
					<div class="relative">
						<div class="w-32 h-32 border-4 border-blue-400/30 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
							{#if formData.avatarPreview}
								<img src={formData.avatarPreview} alt="Avatar Preview" class="w-full h-full object-cover" />
							{:else}
								<span class="text-4xl text-gray-400">
									{formData.fullName.split(' ').map(n => n[0]).join('') || 'U'}
								</span>
							{/if}
						</div>
						<label for="avatar-upload" class="absolute -bottom-1 -right-1 bg-blue-600 text-white rounded-full p-2 cursor-pointer hover:bg-blue-700 transition-colors">
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
							</svg>
							<input
								id="avatar-upload"
								type="file"
								accept="image/*"
								class="hidden"
								on:change={handleAvatarChange}
							/>
						</label>
					</div>
					<button
						type="button"
						on:click={handleSubmit}
						class="text-sm text-gray-400 hover:text-white underline"
					>
						Skip for now
					</button>
				</div>
			{/if}

			<!-- Navigation buttons -->
			<div class="flex items-center justify-between mt-8">
				{#if step > 1}
					<button
						type="button"
						on:click={handlePrevStep}
						class="bg-transparent border border-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
					>
						<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
						</svg>
						Back
					</button>
				{:else}
					<div></div>
				{/if}
				
				{#if step < 5}
					<button
						type="button"
						on:click={handleNextStep}
						disabled={loading}
						class="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-2 rounded-lg transition-all disabled:opacity-50"
					>
						Next
					</button>
				{:else}
					<button
						type="submit"
						disabled={loading}
						class="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-2 rounded-lg transition-all disabled:opacity-50 flex items-center"
					>
						{#if loading}
							<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
							</svg>
							Creating...
						{:else}
							Finish & Create Account
						{/if}
					</button>
				{/if}
			</div>
		</form>

		<p class="text-center text-sm text-gray-400 mt-8">
			Already have an account?
			<a href="/login" class="font-medium text-blue-400 hover:underline">
				Log in here
			</a>
		</p>
	</div>
</div>
