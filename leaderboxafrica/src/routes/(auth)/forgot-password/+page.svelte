<!-- Forgot Password Page -->
<script lang="ts">
	import { goto } from '$app/navigation';

	let email = '';
	let loading = false;
	let success = false;
	let error = '';

	async function handleSubmit(event: Event) {
		event.preventDefault();
		
		if (!email) {
			error = 'Please enter your email address.';
			return;
		}

		if (!/\S+@\S+\.\S+/.test(email)) {
			error = 'Please enter a valid email address.';
			return;
		}

		loading = true;
		error = '';

		try {
			const response = await fetch('/api/auth/forgot-password', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ email })
			});

			const data = await response.json();

			if (data.success) {
				success = true;
			} else {
				error = data.error || 'Failed to send reset email. Please try again.';
			}
		} catch (err) {
			error = 'An unexpected error occurred. Please try again.';
			console.error('Forgot password error:', err);
		} finally {
			loading = false;
		}
	}

	function goBack() {
		goto('/login');
	}
</script>

<svelte:head>
	<title>Forgot Password - LeaderBox</title>
	<meta name="description" content="Reset your LeaderBox password" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-100 via-gray-50 to-blue-100 flex flex-col justify-center items-center p-4 text-gray-900">
	<div class="w-full max-w-md p-8 bg-white/80 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200 relative">
		<!-- Back button -->
		<button 
			type="button"
			on:click={goBack}
			class="absolute top-4 left-4 text-blue-600 hover:text-blue-500 p-2 rounded-full hover:bg-blue-50 transition-colors"
		>
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
		</button>

		{#if !success}
			<!-- Header -->
			<div class="text-center mb-8">
				<div class="mx-auto w-16 h-16 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center mb-4">
					<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
					</svg>
				</div>
				<h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
					Forgot Password?
				</h1>
				<p class="text-gray-600 mt-2">No worries! Enter your email and we'll send you a reset link.</p>
			</div>

			<!-- Error message -->
			{#if error}
				<div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
					{error}
				</div>
			{/if}

			<!-- Form -->
			<form on:submit={handleSubmit} class="space-y-6">
				<!-- Email field -->
				<div>
					<label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
					<div class="relative">
						<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
						</svg>
						<input
							id="email"
							type="email"
							placeholder="Enter your email address"
							bind:value={email}
							required
							disabled={loading}
							class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
						/>
					</div>
				</div>

				<!-- Submit button -->
				<button
					type="submit"
					disabled={loading}
					class="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
				>
					{#if loading}
						<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						Sending Reset Link...
					{:else}
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
						</svg>
						Send Reset Link
					{/if}
				</button>
			</form>

			<!-- Back to login link -->
			<div class="mt-6 text-center">
				<p class="text-sm text-gray-500">
					Remember your password?
					<a href="/login" class="font-medium text-blue-600 hover:text-blue-500 hover:underline">
						Back to Login
					</a>
				</p>
			</div>
		{:else}
			<!-- Success state -->
			<div class="text-center">
				<div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
					<svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
					</svg>
				</div>
				<h1 class="text-3xl font-bold text-gray-900 mb-4">Check Your Email!</h1>
				<p class="text-gray-600 mb-6">
					We've sent a password reset link to <strong>{email}</strong>
				</p>
				<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
					<div class="flex items-start">
						<svg class="w-5 h-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
						</svg>
						<div class="text-sm text-blue-700">
							<p class="font-medium">What's next?</p>
							<ul class="mt-1 list-disc list-inside space-y-1">
								<li>Check your email inbox (and spam folder)</li>
								<li>Click the reset link in the email</li>
								<li>The link will expire in 1 hour for security</li>
							</ul>
						</div>
					</div>
				</div>
				<div class="space-y-3">
					<button
						type="button"
						on:click={() => { success = false; email = ''; }}
						class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
					>
						Send Another Email
					</button>
					<button
						type="button"
						on:click={goBack}
						class="w-full text-blue-600 hover:text-blue-500 font-medium py-2 px-4 rounded-lg transition-colors"
					>
						Back to Login
					</button>
				</div>
			</div>
		{/if}
	</div>
</div>
