<!-- <PERSON>gin Page - Migrated from React LoginPage.jsx -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { authStore } from '$lib/stores/auth.js';
	import { onMount } from 'svelte';

	let email = '';
	let password = '';
	let showPassword = false;
	let loading = false;
	let error = '';

	async function handleSubmit(event: Event) {
		event.preventDefault();
		
		if (!email || !password) {
			error = 'Please enter both email and password.';
			return;
		}

		if (!/\S+@\S+\.\S+/.test(email)) {
			error = 'Invalid email address.';
			return;
		}

		loading = true;
		error = '';

		try {
			const result = await authStore.login(email, password);
			
			if (result.success) {
				// Redirect based on user role
				const user = $authStore.user;
				if (user?.isAdmin) {
					goto('/admin');
				} else {
					goto('/dashboard');
				}
			} else {
				error = result.error || '<PERSON><PERSON> failed. Please try again.';
			}
		} catch (err) {
			error = 'An unexpected error occurred. Please try again.';
			console.error('Login error:', err);
		} finally {
			loading = false;
		}
	}

	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}

	function goBack() {
		history.back();
	}

	function handleGoogleLogin() {
		// Placeholder for Google login
		alert('Google login will be available in a future update.');
	}
</script>

<svelte:head>
	<title>Login - LeaderBox</title>
	<meta name="description" content="Log in to your LeaderBox account" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-100 via-gray-50 to-blue-100 flex flex-col justify-center items-center p-4 text-gray-900">
	<div class="w-full max-w-md p-8 bg-white/80 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200 relative">
		<!-- Back button for mobile -->
		<button 
			type="button"
			on:click={goBack}
			class="absolute top-4 left-4 text-blue-600 hover:text-blue-500 md:hidden p-2 rounded-full hover:bg-blue-50 transition-colors"
		>
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
		</button>

		<!-- Header -->
		<div class="text-center mb-8">
			<h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
				Welcome Back!
			</h1>
			<p class="text-gray-600 mt-2">Log in to continue to LeaderBox.</p>
		</div>



		<!-- Error message -->
		{#if error}
			<div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
				{error}
			</div>
		{/if}

		<!-- Login form -->
		<form on:submit={handleSubmit} class="space-y-6">
			<!-- Email field -->
			<div>
				<label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
				<div class="relative">
					<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
					</svg>
					<input
						id="email"
						type="email"
						placeholder="<EMAIL>"
						bind:value={email}
						required
						disabled={loading}
						class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
					/>
				</div>
			</div>

			<!-- Password field -->
			<div>
				<label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
				<div class="relative">
					<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
					</svg>
					<input
						id="password"
						type={showPassword ? 'text' : 'password'}
						placeholder="••••••••"
						bind:value={password}
						required
						disabled={loading}
						class="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
					/>
					<button
						type="button"
						on:click={togglePasswordVisibility}
						class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
						disabled={loading}
					>
						{#if showPassword}
							<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
							</svg>
						{:else}
							<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
							</svg>
						{/if}
					</button>
				</div>
			</div>

			<!-- Forgot password link -->
			<div class="flex items-center justify-between">
				<a href="/forgot-password" class="text-sm text-blue-600 hover:underline hover:text-blue-500">
					Forgot password?
				</a>
			</div>

			<!-- Submit button -->
			<button
				type="submit"
				disabled={loading}
				class="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
			>
				{#if loading}
					<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
						<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
					</svg>
					Logging in...
				{:else}
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
					</svg>
					Log In
				{/if}
			</button>
		</form>

		<!-- Divider -->
		<div class="my-6 flex items-center">
			<div class="flex-grow border-t border-gray-300"></div>
			<span class="mx-4 text-gray-500">OR</span>
			<div class="flex-grow border-t border-gray-300"></div>
		</div>

		<!-- Google login button -->
		<button
			type="button"
			on:click={handleGoogleLogin}
			disabled={loading}
			class="w-full border border-gray-300 text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200 py-3 rounded-lg flex items-center justify-center disabled:opacity-50"
		>
			<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
				<path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
				<path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
				<path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
				<path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
				<path d="M1 1h22v22H1z" fill="none"/>
			</svg>
			Continue with Google
		</button>

		<!-- Sign up link -->
		<p class="mt-8 text-center text-sm text-gray-500">
			Don't have an account?
			<a href="/register" class="font-medium text-blue-600 hover:text-blue-500 hover:underline">
				Sign up
			</a>
		</p>
	</div>
</div>
