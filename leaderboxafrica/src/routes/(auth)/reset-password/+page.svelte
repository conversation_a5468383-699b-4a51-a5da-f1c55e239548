<!-- Reset Password Page -->
<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';

	let token = '';
	let password = '';
	let confirmPassword = '';
	let showPassword = false;
	let showConfirmPassword = false;
	let loading = false;
	let validatingToken = true;
	let tokenValid = false;
	let userEmail = '';
	let success = false;
	let error = '';

	// Get token from URL
	onMount(() => {
		token = $page.url.searchParams.get('token') || '';
		if (token) {
			validateToken();
		} else {
			validatingToken = false;
			error = 'Invalid reset link. Please request a new password reset.';
		}
	});

	async function validateToken() {
		try {
			const response = await fetch(`/api/auth/reset-password?token=${encodeURIComponent(token)}`);
			const data = await response.json();

			if (data.valid) {
				tokenValid = true;
				userEmail = data.user.email;
			} else {
				error = data.error || 'Invalid or expired reset token.';
			}
		} catch (err) {
			error = 'Failed to validate reset token. Please try again.';
			console.error('Token validation error:', err);
		} finally {
			validatingToken = false;
		}
	}

	async function handleSubmit(event: Event) {
		event.preventDefault();
		
		if (!password || !confirmPassword) {
			error = 'Please fill in both password fields.';
			return;
		}

		if (password !== confirmPassword) {
			error = 'Passwords do not match.';
			return;
		}

		if (password.length < 8) {
			error = 'Password must be at least 8 characters long.';
			return;
		}

		loading = true;
		error = '';

		try {
			const response = await fetch('/api/auth/reset-password', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ token, password })
			});

			const data = await response.json();

			if (data.success) {
				success = true;
			} else {
				error = data.error || 'Failed to reset password. Please try again.';
			}
		} catch (err) {
			error = 'An unexpected error occurred. Please try again.';
			console.error('Reset password error:', err);
		} finally {
			loading = false;
		}
	}

	function goToLogin() {
		goto('/login');
	}

	function requestNewReset() {
		goto('/forgot-password');
	}
</script>

<svelte:head>
	<title>Reset Password - LeaderBox</title>
	<meta name="description" content="Reset your LeaderBox password" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-100 via-gray-50 to-blue-100 flex flex-col justify-center items-center p-4 text-gray-900">
	<div class="w-full max-w-md p-8 bg-white/80 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200">
		
		{#if validatingToken}
			<!-- Loading state -->
			<div class="text-center">
				<div class="mx-auto w-16 h-16 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center mb-4">
					<svg class="animate-spin h-8 w-8 text-white" fill="none" viewBox="0 0 24 24">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
						<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
					</svg>
				</div>
				<h1 class="text-2xl font-bold text-gray-900 mb-2">Validating Reset Link...</h1>
				<p class="text-gray-600">Please wait while we verify your reset token.</p>
			</div>

		{:else if !tokenValid}
			<!-- Invalid token state -->
			<div class="text-center">
				<div class="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
					<svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
					</svg>
				</div>
				<h1 class="text-2xl font-bold text-gray-900 mb-2">Invalid Reset Link</h1>
				<p class="text-gray-600 mb-6">{error}</p>
				<div class="space-y-3">
					<button
						type="button"
						on:click={requestNewReset}
						class="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 rounded-lg transition-all"
					>
						Request New Reset Link
					</button>
					<button
						type="button"
						on:click={goToLogin}
						class="w-full text-blue-600 hover:text-blue-500 font-medium py-2 transition-colors"
					>
						Back to Login
					</button>
				</div>
			</div>

		{:else if success}
			<!-- Success state -->
			<div class="text-center">
				<div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
					<svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
					</svg>
				</div>
				<h1 class="text-3xl font-bold text-gray-900 mb-4">Password Reset Successful!</h1>
				<p class="text-gray-600 mb-6">
					Your password has been successfully reset. You can now log in with your new password.
				</p>
				<button
					type="button"
					on:click={goToLogin}
					class="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-105"
				>
					Continue to Login
				</button>
			</div>

		{:else}
			<!-- Reset form -->
			<div class="text-center mb-8">
				<div class="mx-auto w-16 h-16 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center mb-4">
					<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
					</svg>
				</div>
				<h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
					Reset Your Password
				</h1>
				<p class="text-gray-600 mt-2">Enter a new password for <strong>{userEmail}</strong></p>
			</div>

			<!-- Error message -->
			{#if error}
				<div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
					{error}
				</div>
			{/if}

			<!-- Form -->
			<form on:submit={handleSubmit} class="space-y-6">
				<!-- New password field -->
				<div>
					<label for="password" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
					<div class="relative">
						<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
						</svg>
						<input
							id="password"
							type={showPassword ? 'text' : 'password'}
							placeholder="Enter your new password"
							bind:value={password}
							required
							disabled={loading}
							class="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
						/>
						<button
							type="button"
							on:click={() => showPassword = !showPassword}
							class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
							disabled={loading}
						>
							{#if showPassword}
								<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
								</svg>
							{:else}
								<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
								</svg>
							{/if}
						</button>
					</div>
					<p class="mt-1 text-xs text-gray-500">Must be at least 8 characters long</p>
				</div>

				<!-- Confirm password field -->
				<div>
					<label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
					<div class="relative">
						<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
						</svg>
						<input
							id="confirmPassword"
							type={showConfirmPassword ? 'text' : 'password'}
							placeholder="Confirm your new password"
							bind:value={confirmPassword}
							required
							disabled={loading}
							class="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
						/>
						<button
							type="button"
							on:click={() => showConfirmPassword = !showConfirmPassword}
							class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
							disabled={loading}
						>
							{#if showConfirmPassword}
								<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
								</svg>
							{:else}
								<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
								</svg>
							{/if}
						</button>
					</div>
				</div>

				<!-- Submit button -->
				<button
					type="submit"
					disabled={loading}
					class="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
				>
					{#if loading}
						<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						Resetting Password...
					{:else}
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
						</svg>
						Reset Password
					{/if}
				</button>
			</form>

			<!-- Back to login link -->
			<div class="mt-6 text-center">
				<p class="text-sm text-gray-500">
					Remember your password?
					<button
						type="button"
						on:click={goToLogin}
						class="font-medium text-blue-600 hover:text-blue-500 hover:underline"
					>
						Back to Login
					</button>
				</p>
			</div>
		{/if}
	</div>
</div>
