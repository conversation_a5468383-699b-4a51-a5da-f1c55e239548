<!-- Individual Poll Page - Exact replica of React PollDetailPage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { fly, fade } from 'svelte/transition';
	import { authStore } from '$lib/stores/auth.js';
	import { pollsStore, pollActions } from '$lib/stores/polls.js';
	
	// Layout
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';
	
	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	
	// Poll Components
	import CommentAfterVoteDialog from '$lib/components/polls/CommentAfterVoteDialog.svelte';

	// Reactive variables
	$: user = $authStore.user;
	$: pollId = $page.params.id;
	$: poll = $pollsStore.find(p => p.id === pollId);

	// State
	let loading = true;
	let showCommentDialog = false;
	let selectedOptionId = null;
	let newComment = '';
	let showComments = true;

	// Computed values
	$: userHasVoted = user && poll?.userVotes && poll.userVotes[user.id];
	$: userVotedOption = userHasVoted ? poll.userVotes[user.id] : null;

	// Functions
	function handleVote(optionId) {
		if (!user) {
			goto('/login');
			return;
		}
		selectedOptionId = optionId;
		showCommentDialog = true;
	}

	function submitVoteWithComment(comment) {
		if (selectedOptionId && poll) {
			pollActions.voteOnPoll(poll.id, selectedOptionId, comment);
			showCommentDialog = false;
			selectedOptionId = null;
		}
	}

	function addComment() {
		if (!user) {
			goto('/login');
			return;
		}
		if (newComment.trim() && poll) {
			pollActions.addCommentToPoll(poll.id, newComment.trim());
			newComment = '';
		}
	}

	function getPercentage(votes, total) {
		if (total === 0) return 0;
		return Math.round((votes / total) * 100);
	}

	function formatDate(dateString) {
		return new Date(dateString).toLocaleDateString();
	}

	function formatTimeAgo(dateString) {
		const date = new Date(dateString);
		const now = new Date();
		const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
		
		if (diffInHours < 1) {
			return 'Just now';
		} else if (diffInHours < 24) {
			return `${diffInHours}h ago`;
		} else {
			const diffInDays = Math.floor(diffInHours / 24);
			return `${diffInDays}d ago`;
		}
	}

	function getOptionColor(optionId) {
		if (!userHasVoted) return 'bg-secondary';
		if (userVotedOption === optionId) return 'bg-primary';
		return 'bg-muted';
	}

	function getOptionTextColor(optionId) {
		if (!userHasVoted) return 'text-foreground';
		if (userVotedOption === optionId) return 'text-primary-foreground';
		return 'text-muted-foreground';
	}

	onMount(() => {
		loading = false;
		if (!poll) {
			goto('/polls');
		}
	});
</script>

<svelte:head>
	<title>{poll ? poll.topic : 'Poll'} - LeaderBox</title>
	<meta name="description" content={poll ? poll.description : 'View and participate in this community poll'} />
</svelte:head>

<MainLayout>
	{#if loading}
		<div class="flex items-center justify-center min-h-[400px]">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
		</div>
	{:else if !poll}
		<div class="text-center py-16">
			<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
			</svg>
			<h1 class="mt-4 text-2xl font-bold text-gray-900">Poll Not Found</h1>
			<p class="mt-2 text-gray-600">The poll you're looking for doesn't exist or has been removed.</p>
			<Button on:click={() => goto('/polls')} className="mt-4">
				Back to Polls
			</Button>
		</div>
	{:else}
		<div 
			class="space-y-8"
			in:fade={{ duration: 500 }}
		>
			<!-- Back Button -->
			<Button variant="ghost" on:click={() => goto('/polls')} className="mb-4">
				<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
				</svg>
				Back to Polls
			</Button>

			<!-- Poll Content -->
			<Card className="modern-card">
				<CardHeader>
					<div class="space-y-4">
						<!-- Header Info -->
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<h1 class="text-2xl md:text-3xl font-bold text-foreground mb-3">
									{poll.topic}
								</h1>
								<div class="flex items-center space-x-2 text-sm text-muted-foreground">
									<span>by {poll.source}</span>
									<span>•</span>
									<span>{formatTimeAgo(poll.createdAt)}</span>
									<span>•</span>
									<span>{poll.totalVotes} votes</span>
								</div>
							</div>
						</div>

						<!-- Description -->
						{#if poll.description}
							<p class="text-muted-foreground leading-relaxed">
								{poll.description}
							</p>
						{/if}

						<!-- Tags -->
						{#if poll.generalTags && poll.generalTags.length > 0}
							<div class="flex flex-wrap gap-2">
								{#each poll.generalTags as tag}
									<Badge variant="secondary">
										{tag}
									</Badge>
								{/each}
							</div>
						{/if}

						<!-- Tagged Leader/Location -->
						{#if poll.taggedLeader || poll.taggedLocation}
							<div class="flex items-center space-x-4 text-sm text-muted-foreground">
								{#if poll.taggedLeader}
									<div class="flex items-center">
										<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
										</svg>
										Tagged: {poll.taggedLeader}
									</div>
								{/if}
								{#if poll.taggedLocation}
									<div class="flex items-center">
										<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
										</svg>
										Location: {poll.taggedLocation}
									</div>
								{/if}
							</div>
						{/if}
					</div>
				</CardHeader>

				<CardContent className="space-y-6">
					<!-- Poll Options -->
					<div class="space-y-3">
						<h3 class="text-lg font-semibold text-foreground">Poll Options</h3>
						{#each poll.options as option}
							<div class="relative">
								<button
									class="w-full text-left p-4 rounded-lg border border-border hover:border-primary/50 transition-all duration-200 relative overflow-hidden {getOptionTextColor(option.id)}"
									on:click={() => handleVote(option.id)}
									disabled={userHasVoted}
								>
									<!-- Progress bar background -->
									<div 
										class="absolute inset-0 {getOptionColor(option.id)} opacity-20 transition-all duration-300"
										style="width: {getPercentage(option.votes, poll.totalVotes)}%"
									></div>
									
									<!-- Option content -->
									<div class="relative flex items-center justify-between">
										<span class="font-medium text-base">{option.text}</span>
										<div class="flex items-center space-x-3">
											<span class="text-lg font-bold">
												{getPercentage(option.votes, poll.totalVotes)}%
											</span>
											<span class="text-sm text-muted-foreground">
												({option.votes} votes)
											</span>
											{#if userVotedOption === option.id}
												<svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
													<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
												</svg>
											{/if}
										</div>
									</div>
								</button>
							</div>
						{/each}
					</div>

					<!-- Vote Status -->
					{#if userHasVoted}
						<div class="text-center py-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
							<Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
								<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
								You have voted in this poll
							</Badge>
						</div>
					{:else if user}
						<div class="text-center py-4 bg-primary/5 rounded-lg">
							<p class="text-primary font-medium">Click an option above to cast your vote</p>
						</div>
					{:else}
						<div class="text-center py-4 bg-secondary/50 rounded-lg">
							<p class="text-muted-foreground mb-3">You must be logged in to vote</p>
							<Button on:click={() => goto('/login')}>
								Login to Vote
							</Button>
						</div>
					{/if}
				</CardContent>
			</Card>

			<!-- Comments Section -->
			<Card className="modern-card">
				<CardHeader>
					<div class="flex items-center justify-between">
						<h3 class="text-xl font-semibold text-foreground">
							Comments ({poll.commentsCount || 0})
						</h3>
						<Button 
							variant="ghost" 
							size="sm" 
							on:click={() => showComments = !showComments}
						>
							{showComments ? 'Hide' : 'Show'} Comments
						</Button>
					</div>
				</CardHeader>

				{#if showComments}
					<CardContent className="space-y-6">
						<!-- Add Comment -->
						{#if user}
							<div class="border border-border rounded-lg p-4">
								<div class="flex items-start space-x-3">
									<Avatar 
										className="h-8 w-8"
										src={user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`}
										alt={user.name}
										fallback={user.name ? user.name.substring(0,1).toUpperCase() : user.email.substring(0,1).toUpperCase()}
									/>
									<div class="flex-1">
										<textarea
											bind:value={newComment}
											placeholder="Share your thoughts on this poll..."
											rows="3"
											class="w-full px-3 py-2 border border-border rounded-md focus:ring-primary focus:border-primary resize-none"
										></textarea>
										<div class="flex justify-end mt-2">
											<Button 
												on:click={addComment}
												disabled={!newComment.trim()}
												size="sm"
											>
												Post Comment
											</Button>
										</div>
									</div>
								</div>
							</div>
						{:else}
							<div class="text-center py-4 bg-secondary/30 rounded-lg">
								<p class="text-muted-foreground mb-3">Login to join the discussion</p>
								<Button variant="outline" on:click={() => goto('/login')}>
									Login
								</Button>
							</div>
						{/if}

						<!-- Comments List -->
						{#if poll.comments && poll.comments.length > 0}
							<div class="space-y-4">
								{#each poll.comments as comment}
									<div class="border border-border rounded-lg p-4">
										<div class="flex items-start space-x-3">
											<Avatar 
												className="h-8 w-8"
												src={comment.userAvatarUrl}
												alt={comment.userName}
												fallback={comment.userName.substring(0,1).toUpperCase()}
											/>
											<div class="flex-1">
												<div class="flex items-center space-x-2 mb-2">
													<span class="font-medium text-foreground">{comment.userName}</span>
													<span class="text-sm text-muted-foreground">{formatTimeAgo(comment.timestamp)}</span>
												</div>
												<p class="text-foreground leading-relaxed">{comment.text}</p>
											</div>
										</div>
									</div>
								{/each}
							</div>
						{:else}
							<div class="text-center py-8 text-muted-foreground">
								<svg class="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
								</svg>
								<p>No comments yet. Be the first to share your thoughts!</p>
							</div>
						{/if}
					</CardContent>
				{/if}
			</Card>
		</div>
	{/if}

	<!-- Comment After Vote Dialog -->
	{#if showCommentDialog && poll}
		<CommentAfterVoteDialog
			isOpen={showCommentDialog}
			pollTopic={poll.topic}
			onSubmitComment={submitVoteWithComment}
			onClose={() => {
				showCommentDialog = false;
				selectedOptionId = null;
			}}
		/>
	{/if}
</MainLayout>

<style>
	.modern-card {
		@apply bg-card border border-border shadow-sm hover:shadow-md transition-shadow duration-200;
	}
</style>
