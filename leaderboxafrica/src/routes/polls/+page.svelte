<!-- Polls Page - Exact replica of React PollsPage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { fly, fade } from 'svelte/transition';
	import { authStore } from '$lib/stores/auth.js';
	import { pollsStore, trendingPolls, newPolls, pollActions } from '$lib/stores/polls.js';
	
	// Layout
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';
	
	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardFooter from '$lib/components/ui/CardFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	
	// Poll Components
	import CreatePollDialog from '$lib/components/polls/CreatePollDialog.svelte';
	import CommentAfterVoteDialog from '$lib/components/polls/CommentAfterVoteDialog.svelte';
	import PollCard from '$lib/components/polls/PollCard.svelte';

	// Reactive variables using Svelte 5 runes
	let user = $derived($authStore.user);
	let polls = $derived($pollsStore);
	let trending = $derived($trendingPolls);
	let newest = $derived($newPolls);

	// State using Svelte 5 runes
	let showCreatePollDialog = $state(false);
	let pollForCommentModal = $state(null);
	let selectedOptionForCommentModal = $state(null);
	let visiblePollsCount = $state(20);
	let activeTab = $state('trending');

	// Constants
	const POLLS_PER_PAGE = 20;

	// Computed using Svelte 5 runes
	let pollsToDisplay = $derived(activeTab === 'trending' ? trending.slice(0, visiblePollsCount) : newest.slice(0, visiblePollsCount));
	let totalPollsInCurrentTab = $derived(activeTab === 'trending' ? trending.length : newest.length);

	// Functions
	function handleCreatePoll(pollData) {
		if (!user) {
			// TODO: Replace with toast notification
			alert('Please log in to create a poll.');
			goto('/login');
			return;
		}
		
		const newPoll = pollActions.createPoll(pollData);
		if (newPoll) {
			// TODO: Replace with toast notification
			alert('Poll created successfully!');
			goto(`/poll/${newPoll.id}`);
		} else {
			// TODO: Replace with toast notification
			alert('Could not create poll.');
		}
	}

	function handleVoteFromCard(poll, optionId) {
		if (!user) {
			// TODO: Replace with toast notification
			alert('Please log in to vote.');
			goto('/login');
			return;
		}
		selectedOptionForCommentModal = optionId;
		pollForCommentModal = poll;
	}

	function submitVoteWithOptionalComment(pollId, optionId, comment) {
		pollActions.voteOnPoll(pollId, optionId, comment);
		// TODO: Replace with toast notification
		alert('Vote cast successfully!');
		pollForCommentModal = null;
		selectedOptionForCommentModal = null;
	}

	function loadMorePolls() {
		visiblePollsCount += POLLS_PER_PAGE;
	}

	function setActiveTab(tab) {
		activeTab = tab;
		visiblePollsCount = POLLS_PER_PAGE; // Reset count when switching tabs
	}
</script>

<svelte:head>
	<title>Community Polls - LeaderBox</title>
	<meta name="description" content="Voice your opinion, see what others think, and engage in political discussions through community polls." />
</svelte:head>

<MainLayout>
	<div 
		class="space-y-8"
		in:fade={{ duration: 500 }}
	>
		<!-- Header -->
		<div class="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 bg-gradient-to-r from-primary/10 via-card to-accent/10 rounded-xl shadow border border-border">
			<div>
				<h1 class="text-3xl md:text-4xl font-extrabold gradient-text">Community Polls</h1>
				<p class="text-muted-foreground mt-1">Voice your opinion, see what others think, and engage in discussions.</p>
			</div>
			<Button 
				on:click={() => user ? showCreatePollDialog = true : goto('/login')} 
				className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-full px-6 py-3 text-base self-start sm:self-center"
			>
				<svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
				</svg>
				Create Poll
			</Button>
		</div>

		<!-- Tabs -->
		<div class="w-full">
			<!-- Tab Navigation -->
			<div class="flex space-x-1 bg-secondary/50 rounded-lg p-1 mb-6">
				<button
					class="flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 {activeTab === 'trending' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground hover:bg-background/50'}"
					on:click={() => setActiveTab('trending')}
				>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
					</svg>
					Trending
				</button>
				<button
					class="flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 {activeTab === 'new' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground hover:bg-background/50'}"
					on:click={() => setActiveTab('new')}
				>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
					</svg>
					New
				</button>
			</div>

			<!-- Tab Content -->
			{#key activeTab}
				<div
					in:fly={{ y: 10, duration: 200, delay: 100 }}
					out:fly={{ y: -10, duration: 200 }}
				>
					{#if pollsToDisplay.length > 0}
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{#each pollsToDisplay as poll (poll.id)}
								<div in:fly={{ y: 20, duration: 300, delay: 50 }}>
									<PollCard {poll} {user} onVoteClick={handleVoteFromCard} />
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-muted-foreground text-center py-8">
							No {activeTab} polls right now. Be the first to create one!
						</p>
					{/if}
				</div>
			{/key}
		</div>

		<!-- Load More Button -->
		{#if totalPollsInCurrentTab > visiblePollsCount}
			<div class="text-center mt-8">
				<Button 
					variant="outline" 
					on:click={loadMorePolls}
					className="border-primary text-primary hover:bg-primary hover:text-primary-foreground"
				>
					Load More Polls
				</Button>
			</div>
		{/if}
	</div>

	<!-- Create Poll Dialog -->
	<CreatePollDialog 
		bind:isOpen={showCreatePollDialog}
		onCreatePoll={handleCreatePoll}
	/>

	<!-- Comment After Vote Dialog -->
	{#if pollForCommentModal && selectedOptionForCommentModal}
		<CommentAfterVoteDialog
			isOpen={!!pollForCommentModal}
			pollTopic={pollForCommentModal.topic}
			onSubmitComment={(comment) => submitVoteWithOptionalComment(pollForCommentModal.id, selectedOptionForCommentModal, comment)}
			onClose={() => {
				pollForCommentModal = null;
				selectedOptionForCommentModal = null;
			}}
		/>
	{/if}
</MainLayout>

<style>
	.gradient-text {
		background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}
</style>
