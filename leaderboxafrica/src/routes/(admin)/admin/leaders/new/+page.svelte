<!-- Add New Leader Form -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	
	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Select from '$lib/components/ui/Select.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';

	// Form data
	let formData = {
		name: '',
		position: '',
		party: '',
		state: '',
		bio: '',
		avatarUrl: '',
		socialMedia: {
			twitter: '',
			facebook: '',
			instagram: ''
		},
		achievements: ['']
	};

	let loading = false;
	let errors = {};

	// Options
	const parties = [
		{ value: 'APC', label: 'APC' },
		{ value: 'PDP', label: 'PDP' },
		{ value: 'LP', label: 'LP' },
		{ value: 'NNPP', label: 'NNPP' },
		{ value: 'APGA', label: 'APGA' },
		{ value: 'ADC', label: 'ADC' },
		{ value: 'SDP', label: 'SDP' }
	];

	const states = [
		{ value: 'Abia', label: 'Abia' },
		{ value: 'Adamawa', label: 'Adamawa' },
		{ value: 'Akwa Ibom', label: 'Akwa Ibom' },
		{ value: 'Anambra', label: 'Anambra' },
		{ value: 'Bauchi', label: 'Bauchi' },
		{ value: 'Bayelsa', label: 'Bayelsa' },
		{ value: 'Benue', label: 'Benue' },
		{ value: 'Borno', label: 'Borno' },
		{ value: 'Cross River', label: 'Cross River' },
		{ value: 'Delta', label: 'Delta' },
		{ value: 'Ebonyi', label: 'Ebonyi' },
		{ value: 'Edo', label: 'Edo' },
		{ value: 'Ekiti', label: 'Ekiti' },
		{ value: 'Enugu', label: 'Enugu' },
		{ value: 'FCT', label: 'FCT (Abuja)' },
		{ value: 'Gombe', label: 'Gombe' },
		{ value: 'Imo', label: 'Imo' },
		{ value: 'Jigawa', label: 'Jigawa' },
		{ value: 'Kaduna', label: 'Kaduna' },
		{ value: 'Kano', label: 'Kano' },
		{ value: 'Katsina', label: 'Katsina' },
		{ value: 'Kebbi', label: 'Kebbi' },
		{ value: 'Kogi', label: 'Kogi' },
		{ value: 'Kwara', label: 'Kwara' },
		{ value: 'Lagos', label: 'Lagos' },
		{ value: 'Nasarawa', label: 'Nasarawa' },
		{ value: 'Niger', label: 'Niger' },
		{ value: 'Ogun', label: 'Ogun' },
		{ value: 'Ondo', label: 'Ondo' },
		{ value: 'Osun', label: 'Osun' },
		{ value: 'Oyo', label: 'Oyo' },
		{ value: 'Plateau', label: 'Plateau' },
		{ value: 'Rivers', label: 'Rivers' },
		{ value: 'Sokoto', label: 'Sokoto' },
		{ value: 'Taraba', label: 'Taraba' },
		{ value: 'Yobe', label: 'Yobe' },
		{ value: 'Zamfara', label: 'Zamfara' }
	];

	// Functions
	function addAchievement() {
		formData.achievements = [...formData.achievements, ''];
	}

	function removeAchievement(index) {
		formData.achievements = formData.achievements.filter((_, i) => i !== index);
	}

	function validateForm() {
		errors = {};
		
		if (!formData.name.trim()) {
			errors.name = 'Name is required';
		}
		
		if (!formData.position.trim()) {
			errors.position = 'Position is required';
		}
		
		if (!formData.party) {
			errors.party = 'Party is required';
		}
		
		if (!formData.state) {
			errors.state = 'State is required';
		}
		
		if (!formData.bio.trim()) {
			errors.bio = 'Biography is required';
		}

		return Object.keys(errors).length === 0;
	}

	async function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		try {
			loading = true;
			
			// Clean up achievements (remove empty ones)
			const cleanedAchievements = formData.achievements.filter(a => a.trim() !== '');
			
			const leaderData = {
				...formData,
				achievements: cleanedAchievements,
				isActive: true,
				currentRating: 0,
				totalRatings: 0,
				followers: 0
			};

			// TODO: Replace with actual API call
			// const response = await fetch('/api/admin/leaders', {
			//   method: 'POST',
			//   headers: { 'Content-Type': 'application/json' },
			//   body: JSON.stringify(leaderData)
			// });

			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 1000));
			
			console.log('Created leader:', leaderData);
			
			// Redirect to leaders list
			goto('/admin/leaders');
			
		} catch (error) {
			console.error('Failed to create leader:', error);
			// TODO: Show error toast
		} finally {
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>Add New Leader - Admin Panel</title>
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold text-gray-900">Add New Leader</h1>
			<p class="text-gray-600 mt-1">Create a new political leader profile</p>
		</div>
		<Button variant="outline" on:click={() => goto('/admin/leaders')}>
			<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
			</svg>
			Back to Leaders
		</Button>
	</div>

	<form on:submit|preventDefault={handleSubmit} class="space-y-6">
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Main Form -->
			<div class="lg:col-span-2 space-y-6">
				<!-- Basic Information -->
				<Card>
					<CardHeader>
						<h2 class="text-xl font-semibold">Basic Information</h2>
					</CardHeader>
					<CardContent className="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
								<Input
									bind:value={formData.name}
									placeholder="e.g., Bola Ahmed Tinubu"
									className={errors.name ? 'border-red-500' : ''}
								/>
								{#if errors.name}
									<p class="text-red-500 text-sm mt-1">{errors.name}</p>
								{/if}
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-2">Position *</label>
								<Input
									bind:value={formData.position}
									placeholder="e.g., President, Governor, Senator"
									className={errors.position ? 'border-red-500' : ''}
								/>
								{#if errors.position}
									<p class="text-red-500 text-sm mt-1">{errors.position}</p>
								{/if}
							</div>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-2">Political Party *</label>
								<Select
									bind:value={formData.party}
									options={parties}
									placeholder="Select party"
									className={errors.party ? 'border-red-500' : ''}
								/>
								{#if errors.party}
									<p class="text-red-500 text-sm mt-1">{errors.party}</p>
								{/if}
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-2">State *</label>
								<Select
									bind:value={formData.state}
									options={states}
									placeholder="Select state"
									className={errors.state ? 'border-red-500' : ''}
								/>
								{#if errors.state}
									<p class="text-red-500 text-sm mt-1">{errors.state}</p>
								{/if}
							</div>
						</div>

						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Biography *</label>
							<textarea
								bind:value={formData.bio}
								placeholder="Write a brief biography of the leader..."
								rows="4"
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary {errors.bio ? 'border-red-500' : ''}"
							></textarea>
							{#if errors.bio}
								<p class="text-red-500 text-sm mt-1">{errors.bio}</p>
							{/if}
						</div>
					</CardContent>
				</Card>

				<!-- Achievements -->
				<Card>
					<CardHeader>
						<div class="flex items-center justify-between">
							<h2 class="text-xl font-semibold">Achievements</h2>
							<Button type="button" variant="outline" size="sm" on:click={addAchievement}>
								<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
								</svg>
								Add Achievement
							</Button>
						</div>
					</CardHeader>
					<CardContent className="space-y-3">
						{#each formData.achievements as achievement, index}
							<div class="flex items-center space-x-3">
								<Input
									bind:value={formData.achievements[index]}
									placeholder="e.g., Governor of Lagos State (1999-2007)"
									className="flex-1"
								/>
								{#if formData.achievements.length > 1}
									<Button type="button" variant="ghost" size="sm" on:click={() => removeAchievement(index)} className="text-red-600 hover:text-red-700">
										<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
										</svg>
									</Button>
								{/if}
							</div>
						{/each}
					</CardContent>
				</Card>

				<!-- Social Media -->
				<Card>
					<CardHeader>
						<h2 class="text-xl font-semibold">Social Media (Optional)</h2>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Twitter Handle</label>
							<Input
								bind:value={formData.socialMedia.twitter}
								placeholder="@username"
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Facebook Page</label>
							<Input
								bind:value={formData.socialMedia.facebook}
								placeholder="Facebook page name"
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Instagram Handle</label>
							<Input
								bind:value={formData.socialMedia.instagram}
								placeholder="@username"
							/>
						</div>
					</CardContent>
				</Card>
			</div>

			<!-- Sidebar -->
			<div class="space-y-6">
				<!-- Avatar -->
				<Card>
					<CardHeader>
						<h3 class="text-lg font-semibold">Profile Picture</h3>
					</CardHeader>
					<CardContent className="text-center space-y-4">
						<Avatar 
							className="w-24 h-24 mx-auto"
							src={formData.avatarUrl}
							alt={formData.name || 'Leader'}
							fallback={formData.name ? formData.name.split(' ').map(n => n[0]).join('') : 'L'}
						/>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Avatar URL</label>
							<Input
								bind:value={formData.avatarUrl}
								placeholder="https://example.com/avatar.jpg"
							/>
							<p class="text-xs text-gray-500 mt-1">Enter a URL to an image or leave blank for initials</p>
						</div>
					</CardContent>
				</Card>

				<!-- Actions -->
				<Card>
					<CardHeader>
						<h3 class="text-lg font-semibold">Actions</h3>
					</CardHeader>
					<CardContent className="space-y-3">
						<Button type="submit" disabled={loading} className="w-full">
							{#if loading}
								<svg class="animate-spin -ml-1 mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24">
									<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
									<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
								</svg>
								Creating...
							{:else}
								<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
								</svg>
								Create Leader
							{/if}
						</Button>
						<Button type="button" variant="outline" on:click={() => goto('/admin/leaders')} className="w-full">
							Cancel
						</Button>
					</CardContent>
				</Card>
			</div>
		</div>
	</form>
</div>
