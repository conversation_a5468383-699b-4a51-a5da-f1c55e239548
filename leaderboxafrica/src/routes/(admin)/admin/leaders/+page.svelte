<!-- Admin Leaders Management - CRUD Operations -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/auth.js';

	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';
	import Select from '$lib/components/ui/Select.svelte';
	import AvatarUpload from '$lib/components/ui/AvatarUpload.svelte';
	import FileUpload from '$lib/components/ui/FileUpload.svelte';

	// Reactive variables
	$: user = $authStore.user;

	// State
	let leaders = [];
	let loading = true;
	let searchTerm = '';
	let filterParty = 'all';
	let filterState = 'all';
	let selectedLeaders = new Set();
	let showDeleteDialog = false;
	let leaderToDelete = null;
	let showEditDialog = false;
	let editingLeader = null;
	let saving = false;

	// Pagination
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalItems = 0;

	// API Functions
	async function fetchLeaders() {
		try {
			loading = true;
			const params = new URLSearchParams({
				page: currentPage.toString(),
				limit: itemsPerPage.toString(),
				search: searchTerm,
				party: filterParty,
				state: filterState
			});

			const response = await fetch(`/api/admin/leaders?${params}`);
			if (!response.ok) {
				throw new Error('Failed to fetch leaders');
			}

			const data = await response.json();
			leaders = data.leaders;
			totalItems = data.pagination.totalCount;
		} catch (error) {
			console.error('Error fetching leaders:', error);
			alert('Failed to load leaders. Please try again.');
		} finally {
			loading = false;
		}
	}

	// Filter options
	const parties = ['all', 'APC', 'PDP', 'LP', 'NNPP', 'APGA'];
	const states = ['all', 'Lagos', 'Abuja', 'Kano', 'Rivers', 'Ogun', 'Kaduna', 'Anambra', 'Adamawa'];

	// Reactive updates for search and filters (debounced)
	let searchTimeout;
	$: {
		if (searchTimeout) clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			if (searchTerm !== undefined || filterParty !== undefined || filterState !== undefined) {
				currentPage = 1; // Reset to first page when filtering
				fetchLeaders();
			}
		}, 300); // 300ms debounce
	}

	// Computed for pagination (now using server-side pagination)
	$: totalPages = Math.ceil(totalItems / itemsPerPage);
	$: paginatedLeaders = leaders; // Leaders are already paginated from server

	// Handle pagination changes
	function handlePageChange(newPage) {
		if (newPage !== currentPage && newPage >= 1 && newPage <= totalPages) {
			currentPage = newPage;
			fetchLeaders();
		}
	}

	// Functions
	async function loadLeaders() {
		await fetchLeaders();
	}

	function handleSelectLeader(leaderId, checked) {
		if (checked) {
			selectedLeaders.add(leaderId);
		} else {
			selectedLeaders.delete(leaderId);
		}
		selectedLeaders = selectedLeaders; // Trigger reactivity
	}

	function handleSelectAll(checked) {
		if (checked) {
			selectedLeaders = new Set(paginatedLeaders.map(l => l.id));
		} else {
			selectedLeaders = new Set();
		}
	}

	function handleDeleteLeader(leader) {
		leaderToDelete = leader;
		showDeleteDialog = true;
	}

	async function confirmDelete() {
		if (!leaderToDelete) return;

		try {
			const response = await fetch(`/api/admin/leaders/${leaderToDelete.id}`, {
				method: 'DELETE'
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Failed to delete leader');
			}

			alert(`Successfully deleted ${leaderToDelete.name}`);

			// Refresh the leaders list
			await fetchLeaders();
		} catch (error) {
			console.error('Failed to delete leader:', error);
			alert(error.message || 'Failed to delete leader. Please try again.');
		} finally {
			showDeleteDialog = false;
			leaderToDelete = null;
		}
	}

	async function handleBulkDelete() {
		if (selectedLeaders.size === 0) return;

		const count = selectedLeaders.size;
		if (!confirm(`Are you sure you want to delete ${count} selected leader${count > 1 ? 's' : ''}? This action cannot be undone.`)) {
			return;
		}

		try {
			// TODO: Implement bulk delete API call
			// const idsToDelete = Array.from(selectedLeaders);
			// await fetch('/api/admin/leaders/bulk-delete', {
			//   method: 'POST',
			//   body: JSON.stringify({ ids: idsToDelete })
			// });

			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 1000));

			const idsToDelete = Array.from(selectedLeaders);
			leaders = leaders.filter(l => !idsToDelete.includes(l.id));
			selectedLeaders = new Set();
			console.log(`Deleted ${idsToDelete.length} leaders`);

			// Show success message
			alert(`Successfully deleted ${count} leader${count > 1 ? 's' : ''}`);
		} catch (error) {
			console.error('Failed to delete leaders:', error);
			alert('Failed to delete leaders. Please try again.');
		}
	}

	function handleEditLeader(leader) {
		editingLeader = { ...leader };
		showEditDialog = true;
	}

	function handleAddLeader() {
		editingLeader = {
			id: '',
			name: '',
			position: '',
			party: '',
			state: '',
			lga: '',
			bio: '',
			detailedBio: '',
			avatarUrl: '',
			coverImageUrl: '',
			publicSentiment: 'Neutral',
			youtubeVideoUrl: '',
			education: '',
			background: '',
			popularityScore: 75,
			isVerified: false,
			followers: 0,
			currentRating: 0,
			totalRatings: 0,
			isActive: true,
			createdAt: new Date().toISOString().split('T')[0],
			lastActive: new Date().toISOString().split('T')[0]
		};
		showEditDialog = true;
	}

	async function handleSaveLeader() {
		if (!editingLeader || saving) return;

		// Basic validation
		if (!editingLeader.name.trim()) {
			alert('Leader name is required');
			return;
		}
		if (!editingLeader.position.trim()) {
			alert('Position is required');
			return;
		}
		if (!editingLeader.party.trim()) {
			alert('Party is required');
			return;
		}
		if (!editingLeader.state.trim()) {
			alert('State is required');
			return;
		}
		if (!editingLeader.bio.trim()) {
			alert('Bio is required');
			return;
		}

		saving = true;

		try {
			let response;

			if (editingLeader.id) {
				// Update existing leader
				response = await fetch(`/api/admin/leaders/${editingLeader.id}`, {
					method: 'PUT',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(editingLeader)
				});
			} else {
				// Create new leader
				response = await fetch('/api/admin/leaders', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(editingLeader)
				});
			}

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Failed to save leader');
			}

			const data = await response.json();
			alert(`Successfully ${editingLeader.id ? 'updated' : 'added'} ${editingLeader.name}`);

			// Refresh the leaders list
			await fetchLeaders();

			showEditDialog = false;
			editingLeader = null;
		} catch (error) {
			console.error('Failed to save leader:', error);
			alert(error.message || 'Failed to save leader. Please try again.');
		} finally {
			saving = false;
		}
	}

	function handleCancelEdit() {
		showEditDialog = false;
		editingLeader = null;
		saving = false;
	}

	// File upload handlers
	function handleAvatarUpload(event) {
		if (editingLeader) {
			editingLeader.avatarUrl = event.detail.fileUrl;
		}
	}

	function handleCoverImageUpload(event) {
		if (editingLeader) {
			editingLeader.coverImageUrl = event.detail.fileUrl;
		}
	}

	function handleFileUploadError(event) {
		console.error('File upload error:', event.detail.error);
		alert(`Upload failed: ${event.detail.error}`);
	}



	onMount(() => {
		loadLeaders();
	});
</script>

<svelte:head>
	<title>Leaders Management - Admin Panel</title>
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold text-gray-900">Leaders Management</h1>
			<p class="text-gray-600 mt-1">Manage political leaders and their profiles</p>
		</div>
		<div class="flex space-x-3">
			<Button variant="outline" on:click={loadLeaders}>
				<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
				</svg>
				Refresh
			</Button>
			<Button on:click={handleAddLeader}>
				<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
				</svg>
				Add Leader
			</Button>
		</div>
	</div>

	<!-- Filters and Search -->
	<Card>
		<CardContent className="p-6">
			<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div>
					<label for="search-leaders" class="block text-sm font-medium text-gray-700 mb-2">Search Leaders</label>
					<Input
						id="search-leaders"
						type="search"
						placeholder="Search by name..."
						bind:value={searchTerm}
						className="w-full"
					/>
				</div>
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">Party</label>
					<Select
						value={filterParty}
						options={parties.map(party => ({ value: party, label: party === 'all' ? 'All Parties' : party }))}
						on:change={(e) => filterParty = e.detail}
						className="w-full"
					/>
				</div>
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-2">State</label>
					<Select
						value={filterState}
						options={states.map(state => ({ value: state, label: state === 'all' ? 'All States' : state }))}
						on:change={(e) => filterState = e.detail}
						className="w-full"
					/>
				</div>
				<div class="flex items-end">
					{#if selectedLeaders.size > 0}
						<Button variant="destructive" on:click={handleBulkDelete} className="w-full">
							<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
							</svg>
							Delete Selected ({selectedLeaders.size})
						</Button>
					{:else}
						<Button variant="outline" on:click={() => { searchTerm = ''; filterParty = 'all'; filterState = 'all'; }} className="w-full">
							Clear Filters
						</Button>
					{/if}
				</div>
			</div>
		</CardContent>
	</Card>

	<!-- Leaders Table -->
	<Card>
		<CardHeader>
			<div class="flex items-center justify-between">
				<h3 class="text-lg font-semibold">Leaders ({totalItems})</h3>
				<div class="text-sm text-gray-500">
					Page {currentPage} of {totalPages}
				</div>
			</div>
		</CardHeader>
		<CardContent className="p-0">
			{#if loading}
				<!-- Loading State -->
				<div class="p-6">
					<div class="space-y-4">
						{#each Array(5) as _}
							<div class="flex items-center space-x-4 animate-pulse">
								<div class="w-4 h-4 bg-gray-200 rounded"></div>
								<div class="w-12 h-12 bg-gray-200 rounded-full"></div>
								<div class="flex-1 space-y-2">
									<div class="h-4 bg-gray-200 rounded w-1/4"></div>
									<div class="h-3 bg-gray-200 rounded w-1/3"></div>
								</div>
								<div class="w-16 h-6 bg-gray-200 rounded"></div>
								<div class="w-12 h-6 bg-gray-200 rounded"></div>
								<div class="w-16 h-6 bg-gray-200 rounded"></div>
								<div class="w-20 h-8 bg-gray-200 rounded"></div>
							</div>
						{/each}
					</div>
				</div>
			{:else if paginatedLeaders.length === 0}
				<!-- Empty State -->
				<div class="p-12 text-center">
					<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
					</svg>
					<h3 class="mt-4 text-lg font-medium text-gray-900">No leaders found</h3>
					<p class="mt-2 text-gray-500">Try adjusting your search or filter criteria.</p>
				</div>
			{:else}
				<!-- Table -->
				<div class="overflow-x-auto">
					<table class="min-w-full divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th class="px-6 py-3 text-left">
									<input
										type="checkbox"
										checked={selectedLeaders.size === paginatedLeaders.length && paginatedLeaders.length > 0}
										on:change={(e) => handleSelectAll(e.target.checked)}
										class="rounded border-gray-300 text-primary focus:ring-primary"
									/>
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leader</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Followers</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
							</tr>
						</thead>
						<tbody class="bg-white divide-y divide-gray-200">
							{#each paginatedLeaders as leader}
								<tr class="hover:bg-gray-50">
									<td class="px-6 py-4">
										<input
											type="checkbox"
											checked={selectedLeaders.has(leader.id)}
											on:change={(e) => handleSelectLeader(leader.id, e.target.checked)}
											class="rounded border-gray-300 text-primary focus:ring-primary"
										/>
									</td>
									<td class="px-6 py-4">
										<div class="flex items-center">
											<Avatar
												className="h-10 w-10 mr-3"
												src={leader.avatarUrl}
												alt={leader.name}
												fallback={leader.name.split(' ').map(n => n[0]).join('')}
											/>
											<div>
												<div class="text-sm font-medium text-gray-900">{leader.name}</div>
												<div class="text-sm text-gray-500">{leader.position} - {leader.party}</div>
											</div>
										</div>
									</td>
									<td class="px-6 py-4 text-sm text-gray-900">{leader.state}</td>
									<td class="px-6 py-4">
										<div class="flex items-center">
											<svg class="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
												<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
											</svg>
											<span class="text-sm text-gray-900">{leader.currentRating}</span>
										</div>
									</td>
									<td class="px-6 py-4 text-sm text-gray-900">{leader.followers.toLocaleString()}</td>
									<td class="px-6 py-4">
										<Badge variant={leader.isActive ? 'default' : 'secondary'}>
											{leader.isActive ? 'Active' : 'Inactive'}
										</Badge>
									</td>
									<td class="px-6 py-4">
										<div class="flex space-x-2">
											<Button variant="ghost" size="sm" on:click={() => goto(`/leaders/${leader.id}`)}>
												<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
												</svg>
											</Button>
											<Button variant="ghost" size="sm" on:click={() => handleEditLeader(leader)} title="Edit Leader">
												<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
												</svg>
											</Button>
											<Button variant="ghost" size="sm" on:click={() => handleDeleteLeader(leader)} className="text-red-600 hover:text-red-700">
												<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
												</svg>
											</Button>
										</div>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				{#if totalPages > 1}
					<div class="px-6 py-4 border-t border-gray-200">
						<div class="flex items-center justify-between">
							<div class="text-sm text-gray-500">
								Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} results
							</div>
							<div class="flex space-x-2">
								<Button
									variant="outline"
									size="sm"
									disabled={currentPage === 1}
									on:click={() => handlePageChange(currentPage - 1)}
								>
									Previous
								</Button>
								{#each Array(Math.min(5, totalPages)) as _, i}
									{@const page = i + 1}
									<Button
										variant={currentPage === page ? 'default' : 'outline'}
										size="sm"
										on:click={() => handlePageChange(page)}
									>
										{page}
									</Button>
								{/each}
								<Button
									variant="outline"
									size="sm"
									disabled={currentPage === totalPages}
									on:click={() => handlePageChange(currentPage + 1)}
								>
									Next
								</Button>
							</div>
						</div>
					</div>
				{/if}
			{/if}
		</CardContent>
	</Card>
</div>

<!-- Edit Leader Dialog -->
{#if showEditDialog && editingLeader}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
			<div class="flex items-center justify-between mb-6">
				<h3 class="text-lg font-semibold text-gray-900">
					{editingLeader.id ? 'Edit Leader' : 'Add New Leader'}
				</h3>
				<Button variant="ghost" size="icon" on:click={handleCancelEdit}>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</Button>
			</div>

			<form on:submit|preventDefault={handleSaveLeader} class="space-y-4">
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<!-- Name -->
					<div>
						<label for="leader-name" class="block text-sm font-medium text-gray-700 mb-1">
							Full Name *
						</label>
						<Input
							id="leader-name"
							bind:value={editingLeader.name}
							placeholder="e.g., Bola Ahmed Tinubu"
							required
						/>
					</div>

					<!-- Position -->
					<div>
						<label for="leader-position" class="block text-sm font-medium text-gray-700 mb-1">
							Position *
						</label>
						<Input
							id="leader-position"
							bind:value={editingLeader.position}
							placeholder="e.g., President, Governor, Senator"
							required
						/>
					</div>

					<!-- Party -->
					<div>
						<label for="leader-party" class="block text-sm font-medium text-gray-700 mb-1">
							Political Party *
						</label>
						<select
							id="leader-party"
							bind:value={editingLeader.party}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
							required
						>
							<option value="">Select Party</option>
							<option value="APC">APC</option>
							<option value="PDP">PDP</option>
							<option value="LP">LP</option>
							<option value="NNPP">NNPP</option>
							<option value="APGA">APGA</option>
							<option value="ADC">ADC</option>
							<option value="Other">Other</option>
						</select>
					</div>

					<!-- State -->
					<div>
						<label for="leader-state" class="block text-sm font-medium text-gray-700 mb-1">
							State *
						</label>
						<select
							id="leader-state"
							bind:value={editingLeader.state}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
							required
						>
							<option value="">Select State</option>
							<option value="Abia">Abia</option>
							<option value="Adamawa">Adamawa</option>
							<option value="Akwa Ibom">Akwa Ibom</option>
							<option value="Anambra">Anambra</option>
							<option value="Bauchi">Bauchi</option>
							<option value="Bayelsa">Bayelsa</option>
							<option value="Benue">Benue</option>
							<option value="Borno">Borno</option>
							<option value="Cross River">Cross River</option>
							<option value="Delta">Delta</option>
							<option value="Ebonyi">Ebonyi</option>
							<option value="Edo">Edo</option>
							<option value="Ekiti">Ekiti</option>
							<option value="Enugu">Enugu</option>
							<option value="FCT">FCT</option>
							<option value="Gombe">Gombe</option>
							<option value="Imo">Imo</option>
							<option value="Jigawa">Jigawa</option>
							<option value="Kaduna">Kaduna</option>
							<option value="Kano">Kano</option>
							<option value="Katsina">Katsina</option>
							<option value="Kebbi">Kebbi</option>
							<option value="Kogi">Kogi</option>
							<option value="Kwara">Kwara</option>
							<option value="Lagos">Lagos</option>
							<option value="Nasarawa">Nasarawa</option>
							<option value="Niger">Niger</option>
							<option value="Ogun">Ogun</option>
							<option value="Ondo">Ondo</option>
							<option value="Osun">Osun</option>
							<option value="Oyo">Oyo</option>
							<option value="Plateau">Plateau</option>
							<option value="Rivers">Rivers</option>
							<option value="Sokoto">Sokoto</option>
							<option value="Taraba">Taraba</option>
							<option value="Yobe">Yobe</option>
							<option value="Zamfara">Zamfara</option>
						</select>
					</div>

					<!-- LGA -->
					<div>
						<label for="leader-lga" class="block text-sm font-medium text-gray-700 mb-1">
							LGA (Optional)
						</label>
						<Input
							id="leader-lga"
							bind:value={editingLeader.lga}
							placeholder="e.g., Ikeja, Surulere"
						/>
					</div>

					<!-- Bio -->
					<div class="md:col-span-2">
						<label for="leader-bio" class="block text-sm font-medium text-gray-700 mb-1">
							Bio *
						</label>
						<textarea
							id="leader-bio"
							bind:value={editingLeader.bio}
							placeholder="Brief biography of the leader..."
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
							rows="3"
							required
						></textarea>
					</div>

					<!-- Detailed Bio -->
					<div class="md:col-span-2">
						<label for="leader-detailed-bio" class="block text-sm font-medium text-gray-700 mb-1">
							Detailed Bio (Optional)
						</label>
						<textarea
							id="leader-detailed-bio"
							bind:value={editingLeader.detailedBio}
							placeholder="Detailed biography with more information..."
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
							rows="4"
						></textarea>
					</div>

					<!-- Avatar Upload -->
					<div>
						<AvatarUpload
							bind:currentAvatarUrl={editingLeader.avatarUrl}
							userName={editingLeader.name}
							size="lg"
							on:upload={handleAvatarUpload}
							on:error={handleFileUploadError}
						/>
					</div>

					<!-- Cover Image Upload -->
					<div>
						<FileUpload
							uploadType="cover"
							bind:currentFileUrl={editingLeader.coverImageUrl}
							label="Cover Image"
							accept="image/*"
							previewSize="lg"
							on:upload={handleCoverImageUpload}
							on:error={handleFileUploadError}
						/>
					</div>

					<!-- Current Rating -->
					<div>
						<label for="leader-rating" class="block text-sm font-medium text-gray-700 mb-1">
							Current Rating (0-5)
						</label>
						<Input
							id="leader-rating"
							bind:value={editingLeader.currentRating}
							type="number"
							min="0"
							max="5"
							step="0.1"
							placeholder="4.2"
						/>
					</div>

					<!-- Public Sentiment -->
					<div>
						<label for="leader-sentiment" class="block text-sm font-medium text-gray-700 mb-1">
							Public Sentiment
						</label>
						<select
							id="leader-sentiment"
							bind:value={editingLeader.publicSentiment}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
						>
							<option value="Positive">Positive</option>
							<option value="Neutral">Neutral</option>
							<option value="Negative">Negative</option>
						</select>
					</div>

					<!-- YouTube Video URL -->
					<div>
						<label for="leader-youtube" class="block text-sm font-medium text-gray-700 mb-1">
							YouTube Video URL (Optional)
						</label>
						<Input
							id="leader-youtube"
							bind:value={editingLeader.youtubeVideoUrl}
							placeholder="https://youtube.com/watch?v=..."
							type="url"
						/>
					</div>

					<!-- Education -->
					<div>
						<label for="leader-education" class="block text-sm font-medium text-gray-700 mb-1">
							Education (Optional)
						</label>
						<Input
							id="leader-education"
							bind:value={editingLeader.education}
							placeholder="e.g., B.Sc. Business Administration"
						/>
					</div>

					<!-- Background -->
					<div>
						<label for="leader-background" class="block text-sm font-medium text-gray-700 mb-1">
							Background (Optional)
						</label>
						<Input
							id="leader-background"
							bind:value={editingLeader.background}
							placeholder="e.g., Former Governor of Lagos State"
						/>
					</div>

					<!-- Popularity Score -->
					<div>
						<label for="leader-popularity" class="block text-sm font-medium text-gray-700 mb-1">
							Popularity Score (0-100)
						</label>
						<Input
							id="leader-popularity"
							bind:value={editingLeader.popularityScore}
							type="number"
							min="0"
							max="100"
							placeholder="75"
						/>
					</div>

					<!-- Followers -->
					<div>
						<label for="leader-followers" class="block text-sm font-medium text-gray-700 mb-1">
							Followers Count
						</label>
						<Input
							id="leader-followers"
							bind:value={editingLeader.followers}
							type="number"
							min="0"
							placeholder="1250"
						/>
					</div>

					<!-- Status and Verification -->
					<div class="md:col-span-2 space-y-3">
						<label class="flex items-center space-x-2">
							<input
								type="checkbox"
								bind:checked={editingLeader.isActive}
								class="rounded border-gray-300 text-primary focus:ring-primary"
							/>
							<span class="text-sm font-medium text-gray-700">Active Leader</span>
						</label>
						<p class="text-xs text-gray-500">
							Inactive leaders will not appear in public listings
						</p>

						<label class="flex items-center space-x-2">
							<input
								type="checkbox"
								bind:checked={editingLeader.isVerified}
								class="rounded border-gray-300 text-primary focus:ring-primary"
							/>
							<span class="text-sm font-medium text-gray-700">Verified Leader</span>
						</label>
						<p class="text-xs text-gray-500">
							Verified leaders get a verification badge
						</p>
					</div>
				</div>

				<!-- Actions -->
				<div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
					<Button type="button" variant="outline" on:click={handleCancelEdit} disabled={saving}>
						Cancel
					</Button>
					<Button
						type="submit"
						className="bg-primary text-primary-foreground hover:bg-primary/90"
						disabled={saving}
					>
						{#if saving}
							<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
							</svg>
							{editingLeader.id ? 'Updating...' : 'Adding...'}
						{:else}
							{editingLeader.id ? 'Update Leader' : 'Add Leader'}
						{/if}
					</Button>
				</div>
			</form>
		</div>
	</div>
{/if}

<!-- Delete Confirmation Dialog -->
{#if showDeleteDialog && leaderToDelete}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-white rounded-lg p-6 max-w-md w-full">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">Delete Leader</h3>
			<p class="text-gray-600 mb-6">
				Are you sure you want to delete <strong>{leaderToDelete.name}</strong>? This action cannot be undone.
			</p>
			<div class="flex space-x-3">
				<Button variant="destructive" on:click={confirmDelete} className="flex-1">
					Delete
				</Button>
				<Button variant="outline" on:click={() => { showDeleteDialog = false; leaderToDelete = null; }} className="flex-1">
					Cancel
				</Button>
			</div>
		</div>
	</div>
{/if}
