<!-- Admin Layout - Dashboard with sidebar navigation -->
<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { authStore, authInitialized } from '$lib/stores/auth.js';
	import { onMount } from 'svelte';
	
	// UI Components
	import Button from '$lib/components/ui/Button.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';

	// Reactive variables using Svelte 5 runes
	let user = $derived($authStore.user);
	let initialized = $derived($authInitialized);
	let currentPath = $derived($page.url.pathname);

	// Admin navigation items - matching React structure
	const adminNavItems = [
		{
			path: '/admin',
			label: 'Dashboard',
			icon: 'dashboard',
			description: 'Overview and statistics'
		},
		{
			category: 'Content Management',
			items: [
				{
					path: '/admin/leaders',
					label: 'Leaders',
					icon: 'users',
					description: 'Manage political leaders'
				},
				{
					path: '/admin/suggested-leaders',
					label: 'Suggested Leaders',
					icon: 'user-plus',
					description: 'Review leader suggestions'
				},
				{
					path: '/admin/polls',
					label: 'Polls',
					icon: 'chart-bar',
					description: 'Manage polls and voting'
				},
				{
					path: '/admin/petitions',
					label: 'Petitions',
					icon: 'document-text',
					description: 'Manage petitions'
				},
				{
					path: '/admin/banters',
					label: 'Banter Room',
					icon: 'message-square',
					description: 'Manage discussions'
				},
				{
					path: '/admin/groups',
					label: 'Groups',
					icon: 'group',
					description: 'Manage user groups'
				},
				{
					path: '/admin/profile-categories',
					label: 'Profile Categories',
					icon: 'tag',
					description: 'Manage categories'
				}
			]
		},
		{
			category: 'User & Engagement',
			items: [
				{
					path: '/admin/registered-users',
					label: 'Registered Users',
					icon: 'user-group',
					description: 'Manage user accounts'
				},
				{
					path: '/admin/moderation',
					label: 'Moderation',
					icon: 'shield',
					description: 'Content moderation'
				},
				{
					path: '/admin/suggested-edits',
					label: 'Suggested Edits',
					icon: 'lightbulb',
					description: 'Review edit suggestions'
				},
				{
					path: '/admin/metrics',
					label: 'Engagement Metrics',
					icon: 'chart-line',
					description: 'Analytics and insights'
				}
			]
		},
		{
			category: 'System',
			items: [
				{
					path: '/admin/keywords',
					label: 'Keyword Management',
					icon: 'tag',
					description: 'Manage keywords'
				},
				{
					path: '/admin/email-settings',
					label: 'Email Settings',
					icon: 'mail',
					description: 'Configure email'
				}
			]
		}
	];

	// State
	let sidebarCollapsed = false;
	let mobileMenuOpen = false;

	// Functions - make it reactive to currentPath changes using Svelte 5 runes
	let isActiveRoute = $derived((path: string): boolean => {
		if (path === '/admin') {
			return currentPath === '/admin';
		}
		return currentPath === path || currentPath.startsWith(path + '/');
	});

	function toggleSidebar() {
		sidebarCollapsed = !sidebarCollapsed;
	}

	function handleLogout() {
		authStore.logout();
		goto('/login');
	}

	// Initialize auth store
	onMount(() => {
		authStore.initialize();
	});

	// Redirect non-admin users (only after auth is initialized) using Svelte 5 effect
	$effect(() => {
		if (initialized && (!user || !user.isAdmin)) {
			goto('/');
		}
	});
</script>

<svelte:head>
	<title>Admin Panel - LeaderBox</title>
</svelte:head>

{#if initialized && user && user.isAdmin}
	<div class="min-h-screen bg-gray-50">
		<!-- Desktop Sidebar -->
		<aside class="hidden lg:flex admin-sidebar {sidebarCollapsed ? 'w-16' : 'w-64'} bg-white shadow-lg transition-all duration-300 ease-in-out fixed left-0 top-0 z-30">
			<div class="flex flex-col h-full">
				<!-- Header -->
				<div class="flex-shrink-0 flex items-center justify-between p-4 border-b border-gray-200">
					{#if !sidebarCollapsed}
						<div class="flex items-center space-x-3">
							<div class="w-8 h-8 rounded-lg flex items-center justify-center">
								<img src="/logo-icon-leader.png" alt="LeaderBox" class="w-8 h-8 object-contain" />
							</div>
							<h1 class="text-lg font-bold text-gray-900">Admin Panel</h1>
						</div>
					{/if}
					<Button variant="ghost" size="icon" on:click={toggleSidebar} className="text-gray-500 hover:text-gray-700">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					</Button>
				</div>

				<!-- Navigation -->
				<nav class="flex-1 p-4 space-y-4 overflow-y-auto min-h-0 max-h-full">
					<!-- Dashboard -->
					<a
						href="/admin"
						class="admin-nav-item {isActiveRoute('/admin') ? 'active' : ''} {sidebarCollapsed ? 'collapsed' : ''}"
						title={sidebarCollapsed ? 'Dashboard' : ''}
					>
						<div class="flex items-center space-x-3">
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4" />
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v4" />
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4" />
							</svg>

							{#if !sidebarCollapsed}
								<div class="flex-1">
									<div class="font-medium text-sm">Dashboard</div>
									<div class="text-xs text-gray-500">Overview and statistics</div>
								</div>
							{/if}
						</div>
					</a>

					<!-- Categories -->
					{#each adminNavItems.slice(1) as category}
						{#if !sidebarCollapsed}
							<div>
								<h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
									{category.category}
								</h3>
								<div class="space-y-1">
									{#each category.items as item}
										<a
											href={item.path}
											class="admin-nav-item {isActiveRoute(item.path) ? 'active' : ''}"
											title={item.label}
										>
											<div class="flex items-center space-x-3">
												<!-- Icons -->
												{#if item.icon === 'users'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
													</svg>
												{:else if item.icon === 'user-plus'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
													</svg>
												{:else if item.icon === 'chart-bar'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
													</svg>
												{:else if item.icon === 'document-text'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
													</svg>
												{:else if item.icon === 'message-square'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
													</svg>
												{:else if item.icon === 'group'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
													</svg>
												{:else if item.icon === 'tag'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
													</svg>
												{:else if item.icon === 'user-group'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
													</svg>
												{:else if item.icon === 'shield'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
													</svg>
												{:else if item.icon === 'lightbulb'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
													</svg>
												{:else if item.icon === 'chart-line'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
													</svg>
												{:else if item.icon === 'mail'}
													<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
													</svg>
												{/if}

												<div class="flex-1">
													<div class="font-medium text-sm">{item.label}</div>
													<div class="text-xs text-gray-500">{item.description}</div>
												</div>
											</div>
										</a>
									{/each}
								</div>
							</div>
						{:else}
							<!-- Collapsed view - show only icons -->
							{#each category.items as item}
								<a
									href={item.path}
									class="admin-nav-item {isActiveRoute(item.path) ? 'active' : ''} collapsed"
									title={item.label}
								>
									<div class="flex items-center justify-center">
										<!-- Same icons as above but centered -->
										{#if item.icon === 'users'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
											</svg>
										{:else if item.icon === 'user-plus'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
											</svg>
										{:else if item.icon === 'chart-bar'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
											</svg>
										{:else if item.icon === 'document-text'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
											</svg>
										{:else if item.icon === 'message-square'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
											</svg>
										{:else if item.icon === 'group'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
											</svg>
										{:else if item.icon === 'tag'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
											</svg>
										{:else if item.icon === 'user-group'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
											</svg>
										{:else if item.icon === 'shield'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
											</svg>
										{:else if item.icon === 'lightbulb'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
											</svg>
										{:else if item.icon === 'chart-line'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
											</svg>
										{:else if item.icon === 'mail'}
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
											</svg>
										{/if}
									</div>
								</a>
							{/each}
						{/if}
					{/each}
				</nav>

				<!-- User Info -->
				<div class="flex-shrink-0 p-4 border-t border-gray-200">
					{#if !sidebarCollapsed}
						<div class="flex items-center space-x-3 mb-3">
							<Avatar 
								className="h-8 w-8"
								src={user.avatarUrl}
								alt={user.name}
								fallback={user.name ? user.name.substring(0,1).toUpperCase() : 'A'}
							/>
							<div class="flex-1 min-w-0">
								<div class="text-sm font-medium text-gray-900 truncate">{user.name || 'Admin'}</div>
								<div class="text-xs text-gray-500 truncate">{user.email}</div>
							</div>
						</div>
						<div class="space-y-1">
							<Button variant="ghost" size="sm" className="w-full justify-start text-xs" on:click={() => goto('/')}>
								<svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
								</svg>
								Back to Site
							</Button>
							<Button variant="ghost" size="sm" className="w-full justify-start text-xs" on:click={handleLogout}>
								<svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
								</svg>
								Logout
							</Button>
						</div>
					{:else}
						<div class="flex flex-col space-y-2">
							<Button variant="ghost" size="icon" on:click={() => goto('/')} title="Back to Site">
								<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
								</svg>
							</Button>
							<Button variant="ghost" size="icon" on:click={handleLogout} title="Logout">
								<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
								</svg>
							</Button>
						</div>
					{/if}
				</div>
			</div>
		</aside>

		<!-- Mobile Header -->
		<div class="lg:hidden fixed top-0 left-0 right-0 bg-white shadow-sm border-b border-gray-200 z-40">
			<div class="flex items-center justify-between p-4">
				<div class="flex items-center space-x-3">
					<Button variant="ghost" size="icon" on:click={() => mobileMenuOpen = true} className="text-gray-500 hover:text-gray-700">
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					</Button>
					<h1 class="text-lg font-bold text-gray-900">Admin Panel</h1>
				</div>
				<div class="flex items-center space-x-2">
					<Button variant="ghost" size="icon" on:click={() => goto('/')} className="text-gray-500 hover:text-gray-700">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
						</svg>
					</Button>
					<Button variant="ghost" size="icon" on:click={handleLogout} className="text-gray-500 hover:text-gray-700">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
						</svg>
					</Button>
				</div>
			</div>
		</div>

		<!-- Mobile Sidebar Overlay -->
		{#if mobileMenuOpen}
			<div class="lg:hidden fixed inset-0 z-50 flex">
				<!-- Backdrop -->
				<div class="fixed inset-0 bg-black bg-opacity-50" on:click={() => mobileMenuOpen = false}></div>

				<!-- Mobile Sidebar -->
				<aside class="relative w-64 bg-white shadow-lg h-full overflow-y-auto">
					<div class="flex flex-col h-full">
						<!-- Mobile Header -->
						<div class="flex items-center justify-between p-4 border-b border-gray-200">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 rounded-lg flex items-center justify-center">
									<img src="/logo-icon-leader.png" alt="LeaderBox" class="w-8 h-8 object-contain" />
								</div>
								<h1 class="text-lg font-bold text-gray-900">Admin Panel</h1>
							</div>
							<Button variant="ghost" size="icon" on:click={() => mobileMenuOpen = false} className="text-gray-500 hover:text-gray-700">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
								</svg>
							</Button>
						</div>

						<!-- Mobile Navigation -->
						<nav class="flex-1 p-4 space-y-4">
							<!-- Dashboard -->
							<a
								href="/admin"
								class="admin-nav-item {isActiveRoute('/admin') ? 'active' : ''}"
								on:click={() => mobileMenuOpen = false}
							>
								<div class="flex items-center space-x-3">
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4" />
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v4" />
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4" />
									</svg>
									<div class="flex-1">
										<div class="font-medium text-sm">Dashboard</div>
										<div class="text-xs text-gray-500">Overview and statistics</div>
									</div>
								</div>
							</a>

							<!-- Categories -->
							{#each adminNavItems.slice(1) as category}
								<div>
									<h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
										{category.category}
									</h3>
									<div class="space-y-1">
										{#each category.items as item}
											<a
												href={item.path}
												class="admin-nav-item {isActiveRoute(item.path) ? 'active' : ''}"
												on:click={() => mobileMenuOpen = false}
											>
												<div class="flex items-center space-x-3">
													<!-- Icons for mobile -->
													{#if item.icon === 'users'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
														</svg>
													{:else if item.icon === 'user-plus'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
														</svg>
													{:else if item.icon === 'chart-bar'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
														</svg>
													{:else if item.icon === 'document-text'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
														</svg>
													{:else if item.icon === 'message-square'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
														</svg>
													{:else if item.icon === 'group'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
														</svg>
													{:else if item.icon === 'tag'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
														</svg>
													{:else if item.icon === 'user-group'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
														</svg>
													{:else if item.icon === 'shield'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
														</svg>
													{:else if item.icon === 'lightbulb'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
														</svg>
													{:else if item.icon === 'chart-line'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
														</svg>
													{:else if item.icon === 'mail'}
														<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
														</svg>
													{/if}

													<div class="flex-1">
														<div class="font-medium text-sm">{item.label}</div>
														<div class="text-xs text-gray-500">{item.description}</div>
													</div>
												</div>
											</a>
										{/each}
									</div>
								</div>
							{/each}
						</nav>

						<!-- Mobile User Info -->
						<div class="p-4 border-t border-gray-200">
							<div class="flex items-center space-x-3 mb-3">
								<Avatar
									className="h-8 w-8"
									src={user.avatarUrl}
									alt={user.name}
									fallback={user.name ? user.name.substring(0,1).toUpperCase() : 'A'}
								/>
								<div class="flex-1 min-w-0">
									<div class="text-sm font-medium text-gray-900 truncate">{user.name || 'Admin'}</div>
									<div class="text-xs text-gray-500 truncate">{user.email}</div>
								</div>
							</div>
							<div class="space-y-1">
								<Button variant="ghost" size="sm" className="w-full justify-start text-xs" on:click={() => { goto('/'); mobileMenuOpen = false; }}>
									<svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
									</svg>
									Back to Site
								</Button>
								<Button variant="ghost" size="sm" className="w-full justify-start text-xs" on:click={handleLogout}>
									<svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
									</svg>
									Logout
								</Button>
							</div>
						</div>
					</div>
				</aside>
			</div>
		{/if}

		<!-- Main Content -->
		<main class="min-h-screen overflow-auto {sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'} transition-all duration-300 ease-in-out">
			<div class="p-4 lg:p-6 pt-20 lg:pt-6">
				<slot />
			</div>
		</main>
	</div>
{:else}
	<!-- Loading or unauthorized -->
	<div class="min-h-screen flex items-center justify-center bg-gray-50">
		<div class="text-center">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
			<p class="text-gray-600">
				{#if !initialized}
					Loading...
				{:else}
					Checking permissions...
				{/if}
			</p>
		</div>
	</div>
{/if}

<style>
	.admin-nav-item {
		@apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
		@apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
	}

	.admin-nav-item.active {
		@apply bg-primary/10 text-primary;
	}

	.admin-nav-item.collapsed {
		@apply justify-center px-2;
	}

	.admin-sidebar {
		@apply h-screen flex-col;
		position: fixed;
		left: 0;
		top: 0;
		z-index: 30;
	}

	/* Only show on desktop */
	@media (min-width: 1024px) {
		.admin-sidebar {
			display: flex;
		}
	}

	/* Ensure proper scrolling behavior */
	.admin-sidebar nav {
		scrollbar-width: thin;
		scrollbar-color: #cbd5e1 transparent;
		flex: 1 1 0%;
		min-height: 0;
		overflow-y: auto;
	}

	.admin-sidebar nav::-webkit-scrollbar {
		width: 6px;
	}

	.admin-sidebar nav::-webkit-scrollbar-track {
		background: transparent;
	}

	.admin-sidebar nav::-webkit-scrollbar-thumb {
		background-color: #cbd5e1;
		border-radius: 3px;
	}

	.admin-sidebar nav::-webkit-scrollbar-thumb:hover {
		background-color: #94a3b8;
	}

	/* Ensure user info stays at bottom */
	.admin-sidebar > div {
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	/* Ensure main content doesn't overlap with sidebar */
	@media (min-width: 1024px) {
		main {
			transition: margin-left 300ms ease-in-out;
		}
	}
</style>
