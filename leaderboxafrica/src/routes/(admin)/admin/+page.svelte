<!-- Admin Dashboard - Overview and Statistics -->
<script lang="ts">
	import { onMount } from 'svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Button from '$lib/components/ui/Button.svelte';

	// Mock data - TODO: Replace with real API calls
	let stats = {
		totalLeaders: 0,
		totalUsers: 0,
		totalPolls: 0,
		totalPetitions: 0,
		activeUsers: 0,
		pendingReports: 0
	};

	let recentActivity = [];
	let loading = true;

	// Load dashboard data
	async function loadDashboardData() {
		try {
			// TODO: Replace with actual API calls
			// Simulate API delay
			await new Promise(resolve => setTimeout(resolve, 1000));
			
			// Mock data
			stats = {
				totalLeaders: 1247,
				totalUsers: 15623,
				totalPolls: 89,
				totalPetitions: 156,
				activeUsers: 2341,
				pendingReports: 12
			};

			recentActivity = [
				{
					id: 1,
					type: 'user_registered',
					message: 'New user registered: <PERSON>',
					timestamp: '2 minutes ago',
					icon: 'user-plus'
				},
				{
					id: 2,
					type: 'leader_added',
					message: 'New leader added: Senator Jane Smith',
					timestamp: '15 minutes ago',
					icon: 'user-check'
				},
				{
					id: 3,
					type: 'poll_created',
					message: 'New poll created: "Budget Allocation 2024"',
					timestamp: '1 hour ago',
					icon: 'chart-bar'
				},
				{
					id: 4,
					type: 'report_submitted',
					message: 'Content report submitted for review',
					timestamp: '2 hours ago',
					icon: 'flag'
				}
			];
		} catch (error) {
			console.error('Failed to load dashboard data:', error);
		} finally {
			loading = false;
		}
	}

	onMount(() => {
		loadDashboardData();
	});
</script>

<svelte:head>
	<title>Admin Dashboard - LeaderBox</title>
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
			<p class="text-gray-600 mt-1">Welcome to the LeaderBox admin panel</p>
		</div>
		<div class="flex space-x-3">
			<Button variant="outline" on:click={loadDashboardData}>
				<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
				</svg>
				Refresh
			</Button>
		</div>
	</div>

	{#if loading}
		<!-- Loading State -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			{#each Array(6) as _}
				<Card className="animate-pulse">
					<CardHeader>
						<div class="h-4 bg-gray-200 rounded w-3/4"></div>
					</CardHeader>
					<CardContent>
						<div class="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
						<div class="h-3 bg-gray-200 rounded w-full"></div>
					</CardContent>
				</Card>
			{/each}
		</div>
	{:else}
		<!-- Statistics Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			<!-- Total Leaders -->
			<Card className="hover:shadow-lg transition-shadow">
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<h3 class="text-sm font-medium text-gray-600">Total Leaders</h3>
					<svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
					</svg>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{stats.totalLeaders.toLocaleString()}</div>
					<p class="text-xs text-gray-500 mt-1">Political leaders registered</p>
				</CardContent>
			</Card>

			<!-- Total Users -->
			<Card className="hover:shadow-lg transition-shadow">
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<h3 class="text-sm font-medium text-gray-600">Total Users</h3>
					<svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
					</svg>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</div>
					<p class="text-xs text-gray-500 mt-1">Registered citizens</p>
				</CardContent>
			</Card>

			<!-- Active Users -->
			<Card className="hover:shadow-lg transition-shadow">
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<h3 class="text-sm font-medium text-gray-600">Active Users</h3>
					<svg class="h-4 w-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
					</svg>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{stats.activeUsers.toLocaleString()}</div>
					<p class="text-xs text-gray-500 mt-1">Active in last 24h</p>
				</CardContent>
			</Card>

			<!-- Total Polls -->
			<Card className="hover:shadow-lg transition-shadow">
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<h3 class="text-sm font-medium text-gray-600">Total Polls</h3>
					<svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
					</svg>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{stats.totalPolls.toLocaleString()}</div>
					<p class="text-xs text-gray-500 mt-1">Active polls</p>
				</CardContent>
			</Card>

			<!-- Total Petitions -->
			<Card className="hover:shadow-lg transition-shadow">
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<h3 class="text-sm font-medium text-gray-600">Total Petitions</h3>
					<svg class="h-4 w-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
					</svg>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{stats.totalPetitions.toLocaleString()}</div>
					<p class="text-xs text-gray-500 mt-1">Active petitions</p>
				</CardContent>
			</Card>

			<!-- Pending Reports -->
			<Card className="hover:shadow-lg transition-shadow">
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<h3 class="text-sm font-medium text-gray-600">Pending Reports</h3>
					<svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 2H21l-3 6 3 6h-8.5l-1-2H5a2 2 0 00-2 2zm9-13.5V9" />
					</svg>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{stats.pendingReports.toLocaleString()}</div>
					<p class="text-xs text-gray-500 mt-1">Require attention</p>
				</CardContent>
			</Card>
		</div>

		<!-- Recent Activity -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Recent Activity Feed -->
			<Card>
				<CardHeader>
					<h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
					<p class="text-sm text-gray-500">Latest system activities</p>
				</CardHeader>
				<CardContent>
					<div class="space-y-4">
						{#each recentActivity as activity}
							<div class="flex items-start space-x-3">
								<div class="flex-shrink-0">
									{#if activity.icon === 'user-plus'}
										<div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
											</svg>
										</div>
									{:else if activity.icon === 'user-check'}
										<div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
											</svg>
										</div>
									{:else if activity.icon === 'chart-bar'}
										<div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
											</svg>
										</div>
									{:else if activity.icon === 'flag'}
										<div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
											<svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 2H21l-3 6 3 6h-8.5l-1-2H5a2 2 0 00-2 2zm9-13.5V9" />
											</svg>
										</div>
									{/if}
								</div>
								<div class="flex-1 min-w-0">
									<p class="text-sm text-gray-900">{activity.message}</p>
									<p class="text-xs text-gray-500">{activity.timestamp}</p>
								</div>
							</div>
						{/each}
					</div>
				</CardContent>
			</Card>

			<!-- Quick Actions -->
			<Card>
				<CardHeader>
					<h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
					<p class="text-sm text-gray-500">Common administrative tasks</p>
				</CardHeader>
				<CardContent>
					<div class="grid grid-cols-2 gap-3">
						<Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" on:click={() => window.location.href = '/admin/leaders'}>
							<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
							</svg>
							<span class="text-sm font-medium">Add Leader</span>
						</Button>
						<Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" on:click={() => window.location.href = '/admin/users'}>
							<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
							</svg>
							<span class="text-sm font-medium">Manage Users</span>
						</Button>
						<Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" on:click={() => window.location.href = '/admin/reports'}>
							<svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 2H21l-3 6 3 6h-8.5l-1-2H5a2 2 0 00-2 2zm9-13.5V9" />
							</svg>
							<span class="text-sm font-medium">Review Reports</span>
						</Button>
						<Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2" on:click={() => window.location.href = '/admin/settings'}>
							<svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
							</svg>
							<span class="text-sm font-medium">Settings</span>
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	{/if}
</div>
