<!-- Banter Room Page - Svelte conversion of React BanterRoomPage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { fly } from 'svelte/transition';
	import type { PageData } from './$types';

	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardFooter from '$lib/components/ui/CardFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import { MessageSquare, Search, PlusCircle, TrendingUp, Sparkles, UserCircle, Heart, MessageCircle } from 'lucide-svelte';
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';

	// Get server-provided data
	let { data }: { data: PageData } = $props();
	let user = data.user;

	// State
	let searchTerm = $state('');
	let activeTab = $state('hot');
	let showStartBanterDialog = $state(false);
	let visibleBantersCount = $state(20);
	let likingBanterId = $state<number | null>(null);
	let isStartingBanter = $state(false);

	// Mock banters data (same structure as React version)
	const mockBanters = [
		{
			id: 1,
			title: 'What do you think about the new education policy?',
			content: 'The government just announced changes to the education curriculum. Are these changes beneficial for our students?',
			author: 'Adebayo Ogundimu',
			authorAvatar: 'https://avatar.vercel.sh/adebayo.png?size=40',
			timestamp: '2024-01-15T10:30:00Z',
			likes: 24,
			comments: 12,
			category: 'Education',
			isHot: true
		},
		{
			id: 2,
			title: 'Healthcare infrastructure needs urgent attention',
			content: 'Rural areas are still lacking basic healthcare facilities. What can we do to pressure the government for action?',
			author: 'Fatima Abdullahi',
			authorAvatar: 'https://avatar.vercel.sh/fatima.png?size=40',
			timestamp: '2024-01-15T08:15:00Z',
			likes: 18,
			comments: 8,
			category: 'Healthcare',
			isHot: true
		},
		{
			id: 3,
			title: 'Youth participation in politics',
			content: 'How can we encourage more young people to get involved in the political process?',
			author: 'Chinedu Okoro',
			authorAvatar: 'https://avatar.vercel.sh/chinedu.png?size=40',
			timestamp: '2024-01-14T16:45:00Z',
			likes: 31,
			comments: 15,
			category: 'Politics',
			isHot: false
		}
	];

	// Derived values
	let filteredBanters = $derived(() => {
		let banters = [...mockBanters];

		// Filter by tab
		if (activeTab === 'hot') {
			banters = banters.filter(b => b.isHot).sort((a, b) => b.likes - a.likes);
		} else if (activeTab === 'new') {
			banters = banters.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
		} else if (activeTab === 'my-banters' && user) {
			banters = banters.filter(b => b.author === user?.name);
		}

		// Filter by search term
		if (searchTerm) {
			banters = banters.filter(b =>
				b.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
				b.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
				b.author.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}

		return banters;
	});



	let bantersToShow = $derived(filteredBanters().slice(0, visibleBantersCount));

	// Functions
	function handleStartBanter() {
		if (!user) {
			goto('/login');
			return;
		}
		showStartBanterDialog = true;
	}

	function handleBanterClick(banterId: number) {
		goto(`/banter/${banterId}`);
	}

	async function handleLikeBanter(banterId: number) {
		if (!user) {
			goto('/login');
			return;
		}

		likingBanterId = banterId;

		try {
			// Mock like logic with delay
			await new Promise(resolve => setTimeout(resolve, 500));
			console.log('Liked banter:', banterId);
			// In real app: update banter like count
		} catch (error) {
			console.error('Failed to like banter:', error);
		} finally {
			likingBanterId = null;
		}
	}

	function loadMoreBanters() {
		visibleBantersCount += 20;
	}

	// Onboarding messages
	const onboardingMessages = [
		"Welcome to the Banter Room – Nigeria's political talkspace. Start a banter. Drop your take. Keep it respectful.",
		"Use this space to debate, discuss, and laugh through politics – without hate."
	];
</script>

<MainLayout>
	<div
		class="max-w-5xl mx-auto space-y-6 py-8 px-4"
		in:fly={{ opacity: 0, duration: 500 }}
	>
	<!-- Header -->
	<header class="text-center py-6 space-y-2">
		<h1 class="text-4xl font-extrabold gradient-text from-primary to-accent">Banter Room</h1>
		{#each onboardingMessages as msg}
			<p class="text-muted-foreground text-sm md:text-base">{msg}</p>
		{/each}
	</header>

	<!-- Search and Create -->
	<div class="flex flex-col sm:flex-row gap-4 items-center sticky top-[70px] md:top-[80px] z-30 bg-background/80 backdrop-blur-sm py-3 px-2 -mx-2 rounded-b-lg shadow-sm">
		<div class="relative flex-grow w-full sm:w-auto">
			<Input
				type="search"
				placeholder="Search banters by title, topic, or author..."
				bind:value={searchTerm}
				className="modern-input pl-4 pr-10 text-base h-11"
			/>
			<Search class="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
		</div>
		<Button onclick={handleStartBanter} className="btn-primary rounded-full px-6 py-2.5 text-base w-full sm:w-auto h-11">
			<PlusCircle size={20} class="mr-2" /> Start a Banter
		</Button>
	</div>

	<!-- Tabs -->
	<div class="flex justify-center">
		<div class="flex bg-secondary/50 rounded-lg p-1 gap-1">
			<button
				class="px-4 py-2 text-sm font-medium rounded-md transition-all flex items-center gap-2 {activeTab === 'hot' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground'}"
				onclick={() => activeTab = 'hot'}
			>
				<TrendingUp size={18} /> Hot
			</button>
			<button
				class="px-4 py-2 text-sm font-medium rounded-md transition-all flex items-center gap-2 {activeTab === 'new' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground'}"
				onclick={() => activeTab = 'new'}
			>
				<Sparkles size={18} /> New
			</button>
			<button
				class="px-4 py-2 text-sm font-medium rounded-md transition-all flex items-center gap-2 {activeTab === 'my-banters' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground'}"
				onclick={() => activeTab = 'my-banters'}
			>
				<UserCircle size={18} /> My Banters
			</button>
		</div>
	</div>

	<!-- Banters List -->
	<div class="space-y-4">
		{#if bantersToShow.length > 0}
			{#each bantersToShow as banter (banter.id)}
				<Card class="modern-card hover:shadow-md transition-shadow cursor-pointer">
					<CardContent className="p-4">
						<div class="flex items-start gap-3">
							<Avatar className="h-10 w-10" src={banter.authorAvatar} alt={banter.author} fallback={banter.author.substring(0,1)} />
							<div class="flex-1 min-w-0">
								<div class="flex items-center gap-2 mb-1">
									<span class="font-medium text-sm">{banter.author}</span>
									<span class="text-xs text-muted-foreground">•</span>
									<span class="text-xs text-muted-foreground">{new Date(banter.timestamp).toLocaleDateString()}</span>
									<span class="text-xs bg-secondary px-2 py-1 rounded-full">{banter.category}</span>
								</div>
								<button
									class="font-semibold text-foreground mb-2 hover:text-primary transition-colors text-left w-full"
									onclick={() => handleBanterClick(banter.id)}
								>
									{banter.title}
								</button>
								<p class="text-sm text-muted-foreground line-clamp-2 mb-3">{banter.content}</p>
								<div class="flex items-center gap-4 text-sm text-muted-foreground">
									<button
										class="flex items-center gap-1 hover:text-primary transition-colors disabled:opacity-50"
										disabled={likingBanterId === banter.id}
										onclick={() => handleLikeBanter(banter.id)}
									>
										{#if likingBanterId === banter.id}
											<div class="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
										{:else}
											<Heart size={16} />
										{/if}
										{banter.likes}
									</button>
									<button
										class="flex items-center gap-1 hover:text-primary transition-colors"
										onclick={() => handleBanterClick(banter.id)}
									>
										<MessageCircle size={16} />
										{banter.comments}
									</button>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			{/each}
		{:else}
			<div class="text-center py-16 modern-card bg-secondary/30">
				<MessageSquare size={48} class="mx-auto text-muted-foreground/50 mb-4" />
				<p class="text-xl font-semibold">No banters found</p>
				<p class="text-muted-foreground mt-2">
					{#if searchTerm}
						No banters match "{searchTerm}".
					{:else}
						There are no banters in this category yet.
					{/if}
				</p>
			</div>
		{/if}
	</div>

	<!-- Load More -->
	{#if filteredBanters().length > visibleBantersCount}
		<div class="text-center mt-8">
			<Button onclick={loadMoreBanters} className="btn-outline-primary">
				Load More Banters
			</Button>
		</div>
	{/if}

	<!-- Start Banter Modal (placeholder) -->
	{#if showStartBanterDialog}
		<div
			class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
			role="dialog"
			aria-modal="true"
			onclick={() => showStartBanterDialog = false}
		>
			<div class="bg-background p-6 rounded-lg max-w-md w-full mx-4" onclick={(e) => e.stopPropagation()}>
				<h3 class="text-lg font-semibold mb-4">Start a Banter</h3>
				<p class="text-muted-foreground mb-4">Banter creation functionality coming soon!</p>
				<Button onclick={() => showStartBanterDialog = false} className="w-full">Close</Button>
			</div>
		</div>
	{/if}
	</div>
</MainLayout>
