<!-- Custom Error Page - 404 and other errors -->
<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';

	// Get error details using Svelte 5 runes
	let status = $derived($page.status);
	let error = $derived($page.error);
	let currentPath = $derived($page.url.pathname);

	// Define routes that are "coming soon" vs truly 404
	const comingSoonRoutes = [
		'/polls',
		'/petitions', 
		'/banter-room',
		'/groups',
		'/messages',
		'/settings',
		'/about',
		'/contact',
		'/terms',
		'/privacy'
	];

	// Check if current path matches a coming soon route

	let isComingSoon = $derived(comingSoonRoutes.some(route => 
    currentPath === route || 
    currentPath.startsWith(route + '/') ||
    (route === '/polls' && currentPath.startsWith('/poll/')) ||
    (route === '/petitions' && currentPath.startsWith('/petition/')) ||
    (route === '/banter-room' && currentPath.startsWith('/banter/')) ||
    (route === '/groups' && currentPath.startsWith('/group/'))
));

	// Get appropriate content based on error type
	let pageContent = $derived(getPageContent(status, isComingSoon()));

	function getPageContent(status: number, isComingSoon: boolean) {
		if (status === 404) {
			if (isComingSoon) {
				return {
					title: "Feature Coming Soon!",
					subtitle: "We're working hard to bring you this feature",
					description: "This section is currently under development and will be available in the next phase of LeaderBox. Stay tuned for exciting updates!",
					icon: "construction",
					buttonText: "Back to Home",
					showProgress: true
				};
			} else {
				return {
					title: "Page Not Found",
					subtitle: "The page you're looking for doesn't exist",
					description: "The page you are trying to access may have been moved, deleted, or you may have entered an incorrect URL.",
					icon: "not-found",
					buttonText: "Go Home",
					showProgress: false
				};
			}
		} else {
			return {
				title: "Something went wrong",
				subtitle: `Error ${status}`,
				description: error?.message || "An unexpected error occurred. Please try again later.",
				icon: "error",
				buttonText: "Try Again",
				showProgress: false
			};
		}
	}

	function getIconSvg(iconType: string) {
		const icons = {
			construction: {
				color: "text-orange-500",
				path: "M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
			},
			"not-found": {
				color: "text-gray-400", 
				path: "M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
			},
			error: {
				color: "text-red-500",
				path: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
			}
		};
		return icons[iconType] || icons["not-found"];
	}

	let iconData = $derived(getIconSvg(pageContent.icon));
</script>

<svelte:head>
	<title>{pageContent.title} - LeaderBox</title>
	<meta name="description" content={pageContent.description} />
</svelte:head>

<MainLayout>
	<div class="min-h-[calc(100vh-200px)] flex items-center justify-center p-4">
		<div class="max-w-md w-full text-center">
			<Card className="p-8">
				<CardContent className="space-y-6">
					<!-- Icon -->
					<div class="flex justify-center">
						<div class="w-20 h-20 rounded-full bg-secondary/50 flex items-center justify-center">
							<svg class="w-10 h-10 {iconData.color}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={iconData.path} />
							</svg>
						</div>
					</div>

					<!-- Content -->
					<div class="space-y-3">
						<h1 class="text-2xl font-bold text-foreground">{pageContent.title}</h1>
						<p class="text-lg text-muted-foreground">{pageContent.subtitle}</p>
						<p class="text-sm text-muted-foreground leading-relaxed">{pageContent.description}</p>
					</div>

					<!-- Progress indicator for coming soon features -->
					{#if pageContent.showProgress}
						<div class="space-y-3">
							<div class="text-sm font-medium text-foreground">Development Progress</div>
							<div class="w-full bg-secondary rounded-full h-2">
								<div class="bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-1000" style="width: 75%"></div>
							</div>
							<div class="text-xs text-muted-foreground">Phase 2A - Coming Soon!</div>
						</div>

						<!-- Feature preview -->
						<div class="bg-secondary/30 rounded-lg p-4 text-left">
							<h3 class="text-sm font-semibold text-foreground mb-2">What's Coming:</h3>
							<ul class="text-xs text-muted-foreground space-y-1">
								{#if currentPath.includes('poll')}
									<li>• Create and vote on political polls</li>
									<li>• Real-time voting results</li>
									<li>• Poll analytics and insights</li>
								{:else if currentPath.includes('petition')}
									<li>• Start and sign petitions</li>
									<li>• Track petition progress</li>
									<li>• Share with your network</li>
								{:else if currentPath.includes('banter') || currentPath.includes('groups')}
									<li>• Political discussions and forums</li>
									<li>• Join interest-based groups</li>
									<li>• Connect with like-minded citizens</li>
								{:else if currentPath.includes('message')}
									<li>• Direct messaging with leaders</li>
									<li>• Group conversations</li>
									<li>• Real-time notifications</li>
								{:else}
									<li>• Enhanced user experience</li>
									<li>• New interactive features</li>
									<li>• Improved functionality</li>
								{/if}
							</ul>
						</div>
					{/if}

					<!-- Actions -->
					<div class="space-y-3">
						<Button on:click={() => goto('/')} className="w-full">
							<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
							</svg>
							{pageContent.buttonText}
						</Button>

						{#if pageContent.showProgress}
							<Button variant="outline" on:click={() => goto('/dashboard')} className="w-full">
								<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v4" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4" />
								</svg>
								Go to Dashboard
							</Button>
						{/if}

						{#if !pageContent.showProgress}
							<Button variant="ghost" on:click={() => history.back()} className="w-full">
								<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
								</svg>
								Go Back
							</Button>
						{/if}
					</div>

					<!-- Help text -->
					<div class="text-xs text-muted-foreground">
						{#if pageContent.showProgress}
							Want to be notified when this feature launches? 
							<a href="/dashboard" class="text-primary hover:underline">Follow us on your dashboard</a>
						{:else}
							Need help? <a href="/" class="text-primary hover:underline">Visit our homepage</a> or try searching for what you need.
						{/if}
					</div>
				</CardContent>
			</Card>
		</div>
	</div>
</MainLayout>
