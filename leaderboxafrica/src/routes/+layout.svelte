<!-- Root Layout -->
<!-- Global layout that applies to all routes -->

<script lang="ts">
	import '$lib/styles/app.css';
	import { authStore } from '$lib/stores/auth.js';
	import Toast from '$lib/components/ui/Toast.svelte';
	import type { LayoutData } from './$types';

	// Get server-provided auth data
	let { data }: { data: LayoutData } = $props();

	// Use $effect to initialize and keep the store in sync.
	// This runs instantly on page load with the server-provided data.
	$effect(() => {
		authStore.initializeFromServer(data.user, data.session);
	});
</script>

<slot />

<!-- Global Toast Notifications -->
<Toast />
