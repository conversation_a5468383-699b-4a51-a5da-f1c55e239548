// File Deletion API Endpoint
// Handles deletion of files from S3

import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { deleteFileFromS3, extractS3KeyFromUrl } from '$lib/server/s3.js';

export const DELETE: RequestHandler = async ({ request, locals }) => {
	try {
		// Check authentication
		if (!locals.user) {
			return json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		// Check admin permissions (only admins can delete files)
		if (!locals.user.isAdmin) {
			return json(
				{ success: false, error: 'Admin permissions required' },
				{ status: 403 }
			);
		}

		const body = await request.json();
		const { fileUrl, key } = body;

		// Validate required fields
		if (!fileUrl && !key) {
			return json(
				{ 
					success: false, 
					error: 'Either fileUrl or key is required' 
				},
				{ status: 400 }
			);
		}

		// Extract S3 key from URL if not provided
		let s3Key = key;
		if (!s3Key && fileUrl) {
			s3Key = extractS3KeyFromUrl(fileUrl);
		}

		if (!s3Key) {
			return json(
				{ 
					success: false, 
					error: 'Could not extract S3 key from file URL' 
				},
				{ status: 400 }
			);
		}

		// Delete file from S3
		const deleted = await deleteFileFromS3(s3Key);

		if (!deleted) {
			return json(
				{ success: false, error: 'Failed to delete file from S3' },
				{ status: 500 }
			);
		}

		return json({
			success: true,
			message: 'File deleted successfully',
			data: {
				key: s3Key,
				fileUrl
			}
		});

	} catch (error) {
		console.error('Error deleting file:', error);
		return json(
			{ success: false, error: 'Failed to delete file' },
			{ status: 500 }
		);
	}
};
