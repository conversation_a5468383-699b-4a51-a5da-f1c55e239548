// Presigned URL API Endpoint
// Generates presigned URLs for direct S3 uploads

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { 
	generatePresignedUploadUrl, 
	validateFile, 
	UPLOAD_CONFIGS,
	type FileUploadConfig 
} from '$lib/server/s3.js';

export const POST: RequestHandler = async ({ request, locals }) => {
	try {
		// Check authentication
		if (!locals.user) {
			return json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const { fileName, fileType, fileSize, uploadType } = body;

		// Validate required fields
		if (!fileName || !fileType || !fileSize || !uploadType) {
			return json(
				{ 
					success: false, 
					error: 'Missing required fields: fileName, fileType, fileSize, uploadType' 
				},
				{ status: 400 }
			);
		}

		// Get upload configuration
		const config = UPLOAD_CONFIGS[uploadType as keyof typeof UPLOAD_CONFIGS];
		if (!config) {
			return json(
				{ 
					success: false, 
					error: `Invalid upload type: ${uploadType}. Allowed types: ${Object.keys(UPLOAD_CONFIGS).join(', ')}` 
				},
				{ status: 400 }
			);
		}

		// Validate file
		const validation = validateFile({ name: fileName, type: fileType, size: fileSize }, config);
		if (!validation.valid) {
			return json(
				{ success: false, error: validation.error },
				{ status: 400 }
			);
		}

		// Generate presigned URL
		const { uploadUrl, fileUrl, key } = await generatePresignedUploadUrl(
			fileName,
			fileType,
			config
		);

		return json({
			success: true,
			data: {
				uploadUrl,
				fileUrl,
				key,
				config: {
					maxSize: config.maxSize,
					allowedTypes: config.allowedTypes
				}
			}
		});

	} catch (error) {
		console.error('Error generating presigned URL:', error);
		return json(
			{ success: false, error: 'Failed to generate upload URL' },
			{ status: 500 }
		);
	}
};

// GET endpoint to retrieve upload configurations
export const GET: RequestHandler = async ({ url, locals }) => {
	try {
		// Check authentication
		if (!locals.user) {
			return json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const uploadType = url.searchParams.get('type');

		if (uploadType) {
			// Return specific configuration
			const config = UPLOAD_CONFIGS[uploadType as keyof typeof UPLOAD_CONFIGS];
			if (!config) {
				return json(
					{ success: false, error: `Invalid upload type: ${uploadType}` },
					{ status: 400 }
				);
			}

			return json({
				success: true,
				data: {
					[uploadType]: config
				}
			});
		} else {
			// Return all configurations
			return json({
				success: true,
				data: UPLOAD_CONFIGS
			});
		}

	} catch (error) {
		console.error('Error retrieving upload configurations:', error);
		return json(
			{ success: false, error: 'Failed to retrieve upload configurations' },
			{ status: 500 }
		);
	}
};
