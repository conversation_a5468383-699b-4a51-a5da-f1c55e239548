// Health check API endpoint
// Tests database connection and returns system status

import { json } from '@sveltejs/kit';
import { checkDatabaseConnection, getDatabaseStats } from '$lib/server/db-utils.js';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
	try {
		// Check database connection
		const dbStatus = await checkDatabaseConnection();
		
		let stats = null;
		if (dbStatus.status === 'connected') {
			try {
				stats = await getDatabaseStats();
			} catch (error) {
				// Stats are optional, don't fail the health check
				console.warn('Failed to get database stats:', error);
			}
		}

		return json({
			status: 'ok',
			timestamp: new Date().toISOString(),
			database: dbStatus,
			stats,
			version: '1.0.0'
		});
	} catch (error) {
		return json({
			status: 'error',
			timestamp: new Date().toISOString(),
			error: error instanceof Error ? error.message : 'Unknown error',
			version: '1.0.0'
		}, { status: 500 });
	}
};
