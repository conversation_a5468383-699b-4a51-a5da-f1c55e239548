// Reset Password API Endpoint
// Handles password reset with token validation

import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { hashPassword } from '$lib/server/auth.js';
import { prisma } from '$lib/server/database.js';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();
		const { token, password } = body;

		// Validate required fields
		if (!token || !password) {
			return json(
				{ success: false, error: 'Reset token and new password are required' },
				{ status: 400 }
			);
		}

		// Validate password strength
		if (password.length < 8) {
			return json(
				{ success: false, error: 'Password must be at least 8 characters long' },
				{ status: 400 }
			);
		}

		// Find user with valid reset token
		const user = await prisma.user.findFirst({
			where: {
				resetToken: token,
				resetTokenExpiresAt: {
					gt: new Date() // Token must not be expired
				}
			}
		});

		if (!user) {
			return json(
				{ success: false, error: 'Invalid or expired reset token' },
				{ status: 400 }
			);
		}

		// Hash the new password
		const passwordHash = await hashPassword(password);

		// Update user password and clear reset token
		await prisma.user.update({
			where: { id: user.id },
			data: {
				passwordHash,
				resetToken: null,
				resetTokenExpiresAt: null
			}
		});

		// Invalidate all existing sessions for security
		await prisma.session.deleteMany({
			where: { userId: user.id }
		});

		console.log('Password reset successful for user:', user.email);

		return json({
			success: true,
			message: 'Password has been reset successfully. Please log in with your new password.'
		});

	} catch (error) {
		console.error('Reset password error:', error);
		return json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
};

// GET endpoint to validate reset token
export const GET: RequestHandler = async ({ url }) => {
	try {
		const token = url.searchParams.get('token');

		if (!token) {
			return json(
				{ valid: false, error: 'Reset token is required' },
				{ status: 400 }
			);
		}

		// Check if token exists and is not expired
		const user = await prisma.user.findFirst({
			where: {
				resetToken: token,
				resetTokenExpiresAt: {
					gt: new Date()
				}
			},
			select: {
				id: true,
				email: true,
				name: true
			}
		});

		if (!user) {
			return json({
				valid: false,
				error: 'Invalid or expired reset token'
			});
		}

		return json({
			valid: true,
			user: {
				email: user.email,
				name: user.name
			}
		});

	} catch (error) {
		console.error('Token validation error:', error);
		return json(
			{ valid: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
};
