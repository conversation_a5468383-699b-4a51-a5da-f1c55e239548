// User Login API Endpoint
// Handles user authentication with email and password

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getUserByEmail, verifyPassword, createSession, createSessionCookie } from '$lib/server/auth.js';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();
		const { email, password } = body;

		// Validate required fields
		if (!email || !password) {
			return json(
				{ success: false, error: 'Email and password are required' },
				{ status: 400 }
			);
		}

		// Get user by email
		const user = await getUserByEmail(email);
		if (!user) {
			return json(
				{ success: false, error: 'Invalid email or password' },
				{ status: 401 }
			);
		}

		// Check if user has a password hash (should always be true for registered users)
		if (!user.passwordHash) {
			return json(
				{ success: false, error: 'Account not properly configured' },
				{ status: 401 }
			);
		}

		// Verify password
		const isValidPassword = await verifyPassword(user.passwordHash, password);
		if (!isValidPassword) {
			return json(
				{ success: false, error: 'Invalid email or password' },
				{ status: 401 }
			);
		}

		// Check if user account is active
		if (user.status !== 'active') {
			return json(
				{ success: false, error: 'Account is not active' },
				{ status: 403 }
			);
		}

		// Create session
		const session = await createSession(user.id);

		// Set session cookie
		const sessionCookie = createSessionCookie(session.token);
		cookies.set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

		// Return success response (exclude sensitive data)
		return json({
			success: true,
			user: {
				id: user.id,
				email: user.email,
				name: user.name,
				role: user.role,
				isAdmin: user.isAdmin,
				state: user.state,
				lga: user.lga,
				gender: user.gender,
				party: user.party,
				avatarUrl: user.avatarUrl,
				onboardingComplete: user.onboardingComplete,
				status: user.status
			}
		});

	} catch (error) {
		console.error('Login error:', error);
		return json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
};
