// Session Validation API Endpoint
// Validates current user session and returns user data

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals }) => {
	try {
		const user = locals.user;
		const session = locals.session;

		if (!user || !session) {
			return json({
				user: null,
				session: null
			});
		}

		// Return user data (exclude sensitive information)
		return json({
			user: {
				id: user.id,
				email: user.email,
				name: user.name,
				role: user.role,
				isAdmin: user.isAdmin,
				state: user.state,
				lga: user.lga,
				gender: user.gender,
				party: user.party,
				avatarUrl: user.avatarUrl,
				onboardingComplete: user.onboardingComplete,
				status: user.status
			},
			session: {
				id: session.id,
				expiresAt: session.expiresAt
			}
		});

	} catch (error) {
		console.error('Session validation error:', error);
		return json(
			{ user: null, session: null },
			{ status: 500 }
		);
	}
};
