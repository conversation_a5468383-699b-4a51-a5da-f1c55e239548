// Forgot Password API Endpoint
// Handles password reset requests and sends reset emails

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getUserByEmail } from '$lib/server/auth.js';
import { getEmailClient, emailTemplates, isEmailConfigured } from '$lib/server/email.js';
import { prisma } from '$lib/server/database.js';
import { env } from '$lib/server/env.js';
import crypto from 'crypto';

export const POST: RequestHandler = async ({ request }) => {
	try {
		// Check if email is configured
		if (!isEmailConfigured()) {
			return json(
				{ 
					success: false, 
					error: 'Email service is not configured. Please contact support.' 
				},
				{ status: 503 }
			);
		}

		const body = await request.json();
		const { email } = body;

		// Validate email
		if (!email) {
			return json(
				{ success: false, error: 'Email address is required' },
				{ status: 400 }
			);
		}

		if (!/\S+@\S+\.\S+/.test(email)) {
			return json(
				{ success: false, error: 'Invalid email address format' },
				{ status: 400 }
			);
		}

		// Check rate limiting
		if (isRateLimited(email)) {
			return json(
				{
					success: false,
					error: 'Too many reset attempts. Please try again in 15 minutes.'
				},
				{ status: 429 }
			);
		}

		// Check if user exists
		const user = await getUserByEmail(email);
		
		// Always return success to prevent email enumeration attacks
		// But only send email if user actually exists
		if (user) {
			try {
				// Generate secure reset token
				const resetToken = crypto.randomBytes(32).toString('hex');
				const resetTokenExpiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

				// Save reset token to database
				await prisma.user.update({
					where: { id: user.id },
					data: {
						resetToken,
						resetTokenExpiresAt
					}
				});

				// Create reset URL
				const baseUrl = env.APP_URL || env.ORIGIN;
				const resetUrl = `${baseUrl}/reset-password?token=${resetToken}`;

				// Send reset email
				const emailClient = getEmailClient();
				const template = emailTemplates.passwordReset(resetUrl, user.name || 'User');

				const emailSent = await emailClient.sendEmail(
					{ email: user.email, name: user.name || undefined },
					template
				);

				if (!emailSent) {
					console.error('Failed to send password reset email to:', email);
					// Don't reveal email sending failure to prevent enumeration
				} else {
					console.log('Password reset email sent successfully to:', email);
				}

			} catch (error) {
				console.error('Error processing password reset for:', email, error);
				// Don't reveal internal errors to prevent enumeration
			}
		} else {
			console.log('Password reset requested for non-existent email:', email);
			// Still return success to prevent enumeration
		}

		// Always return success response
		return json({
			success: true,
			message: 'If an account with that email exists, we\'ve sent a password reset link to it.'
		});

	} catch (error) {
		console.error('Forgot password error:', error);
		return json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
};

// Rate limiting helper (simple in-memory implementation)
const resetAttempts = new Map<string, { count: number; lastAttempt: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const MAX_ATTEMPTS = 3;

function isRateLimited(email: string): boolean {
	const now = Date.now();
	const attempts = resetAttempts.get(email);

	if (!attempts) {
		resetAttempts.set(email, { count: 1, lastAttempt: now });
		return false;
	}

	// Reset counter if window has passed
	if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW) {
		resetAttempts.set(email, { count: 1, lastAttempt: now });
		return false;
	}

	// Check if limit exceeded
	if (attempts.count >= MAX_ATTEMPTS) {
		return true;
	}

	// Increment counter
	attempts.count++;
	attempts.lastAttempt = now;
	resetAttempts.set(email, attempts);

	return false;
}
