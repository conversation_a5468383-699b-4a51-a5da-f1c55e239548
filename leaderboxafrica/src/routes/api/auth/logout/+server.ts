// User Logout API Endpoint
// Handles user logout and session cleanup

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { deleteSession, createBlankSessionCookie } from '$lib/server/auth.js';

export const POST: RequestHandler = async ({ cookies, locals }) => {
	try {
		// Get current session
		const session = locals.session;
		
		if (session) {
			// Delete session from database
			await deleteSession(session.id);
		}

		// Clear session cookie
		const blankCookie = createBlankSessionCookie();
		cookies.set(blankCookie.name, blankCookie.value, blankCookie.attributes);

		return json({ success: true });

	} catch (error) {
		console.error('Logout error:', error);
		
		// Even if there's an error, clear the cookie
		const blankCookie = createBlankSessionCookie();
		cookies.set(blankCookie.name, blankCookie.value, blankCookie.attributes);
		
		return json({ success: true }); // Always return success for logout
	}
};
