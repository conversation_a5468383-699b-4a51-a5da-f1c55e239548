// User Profile Update API Endpoint
// Handles user profile updates

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { updateUser } from '$lib/server/auth.js';

export const PATCH: RequestHandler = async ({ request, locals }) => {
	try {
		const user = locals.user;
		
		if (!user) {
			return json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const { name, state, lga, gender, party, avatarUrl, onboardingComplete } = body;

		// Validate input data
		const updateData: any = {};
		
		if (name !== undefined) {
			if (typeof name !== 'string' || name.trim().length === 0) {
				return json(
					{ success: false, error: 'Name must be a non-empty string' },
					{ status: 400 }
				);
			}
			updateData.name = name.trim();
		}

		if (state !== undefined) {
			updateData.state = typeof state === 'string' ? state.trim() : null;
		}

		if (lga !== undefined) {
			updateData.lga = typeof lga === 'string' ? lga.trim() : null;
		}

		if (gender !== undefined) {
			updateData.gender = typeof gender === 'string' ? gender.trim() : null;
		}

		if (party !== undefined) {
			updateData.party = typeof party === 'string' ? party.trim() : null;
		}

		if (avatarUrl !== undefined) {
			updateData.avatarUrl = typeof avatarUrl === 'string' ? avatarUrl.trim() : null;
		}

		if (onboardingComplete !== undefined) {
			updateData.onboardingComplete = Boolean(onboardingComplete);
		}

		// Update user
		const updatedUser = await updateUser(user.id, updateData);

		// Return updated user data (exclude sensitive information)
		return json({
			success: true,
			user: {
				id: updatedUser.id,
				email: updatedUser.email,
				name: updatedUser.name,
				role: updatedUser.role,
				isAdmin: updatedUser.isAdmin,
				state: updatedUser.state,
				lga: updatedUser.lga,
				gender: updatedUser.gender,
				party: updatedUser.party,
				avatarUrl: updatedUser.avatarUrl,
				onboardingComplete: updatedUser.onboardingComplete,
				status: updatedUser.status
			}
		});

	} catch (error) {
		console.error('Profile update error:', error);
		return json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
};
