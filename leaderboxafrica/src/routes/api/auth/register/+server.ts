// User Registration API Endpoint
// Handles new user registration with email and password

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { createUser, hashPassword, createSession, createSessionCookie, getUserByEmail } from '$lib/server/auth.js';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();
		const { email, password, name, state, lga, gender, party } = body;

		// Validate required fields
		if (!email || !password) {
			return json(
				{ success: false, error: 'Email and password are required' },
				{ status: 400 }
			);
		}

		// Validate email format
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			return json(
				{ success: false, error: 'Invalid email format' },
				{ status: 400 }
			);
		}

		// Validate password strength
		if (password.length < 8) {
			return json(
				{ success: false, error: 'Password must be at least 8 characters long' },
				{ status: 400 }
			);
		}

		// Check if user already exists
		const existingUser = await getUserByEmail(email);
		if (existingUser) {
			return json(
				{ success: false, error: 'User with this email already exists' },
				{ status: 409 }
			);
		}

		// Hash password
		const passwordHash = await hashPassword(password);

		// Create user
		const user = await createUser({
			email,
			passwordHash,
			name,
			state,
			lga,
			gender,
			party
		});

		// Create session
		const session = await createSession(user.id);

		// Set session cookie
		const sessionCookie = createSessionCookie(session.token);
		cookies.set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

		// Return success response (exclude sensitive data)
		return json({
			success: true,
			user: {
				id: user.id,
				email: user.email,
				name: user.name,
				role: user.role,
				isAdmin: user.isAdmin,
				state: user.state,
				lga: user.lga,
				gender: user.gender,
				party: user.party,
				avatarUrl: user.avatarUrl,
				onboardingComplete: user.onboardingComplete,
				status: user.status
			}
		});

	} catch (error) {
		console.error('Registration error:', error);
		return json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
};
