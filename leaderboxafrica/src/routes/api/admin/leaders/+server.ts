// Admin Leaders API - CRUD operations for leaders management
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma.js';
import type { RequestHandler } from './$types';

// GET - List all leaders with pagination and filtering
export const GET: RequestHandler = async ({ url, locals }) => {
	try {
		// Check admin authentication
		if (!locals.user || !locals.user.isAdmin) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Parse query parameters
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = parseInt(url.searchParams.get('limit') || '10');
		const search = url.searchParams.get('search') || '';
		const party = url.searchParams.get('party') || '';
		const state = url.searchParams.get('state') || '';
		const sortBy = url.searchParams.get('sortBy') || 'createdAt';
		const sortOrder = url.searchParams.get('sortOrder') || 'desc';

		// Build where clause
		const where: any = {};
		
		if (search) {
			where.OR = [
				{ name: { contains: search, mode: 'insensitive' } },
				{ position: { contains: search, mode: 'insensitive' } },
				{ party: { contains: search, mode: 'insensitive' } },
				{ state: { contains: search, mode: 'insensitive' } }
			];
		}

		if (party && party !== 'all') {
			where.party = party;
		}

		if (state && state !== 'all') {
			where.state = state;
		}

		// Calculate offset
		const offset = (page - 1) * limit;

		// Get leaders with pagination
		const [leaders, totalCount] = await Promise.all([
			prisma.leader.findMany({
				where,
				orderBy: { [sortBy]: sortOrder },
				skip: offset,
				take: limit,
				include: {
					_count: {
						select: {
							followers: true,
							ratings: true,
							comments: true
						}
					}
				}
			}),
			prisma.leader.count({ where })
		]);

		// Format response to match React app structure
		const formattedLeaders = leaders.map(leader => ({
			id: leader.id,
			name: leader.name,
			position: leader.position,
			party: leader.party,
			state: leader.state,
			lga: leader.lga,
			bio: leader.bio,
			detailedBio: leader.detailedBio,
			avatarUrl: leader.avatarUrl,
			coverImageUrl: leader.coverImageUrl,
			followers: leader.followersCount,              // React app uses 'followers'
			currentRating: Number(leader.ratingAverage),   // React app uses 'currentRating'
			totalRatings: leader.ratingCount,              // React app uses 'totalRatings'
			publicSentiment: leader.publicSentiment,
			youtubeVideoUrl: leader.youtubeVideoUrl,
			education: leader.education,
			background: leader.background,
			popularityScore: leader.popularityScore,
			isVerified: leader.isVerified,
			isFollowed: false,                             // Computed field, set to false for admin
			commentsCount: leader._count.comments,
			isActive: true,                                // For admin purposes
			createdAt: leader.createdAt.toISOString().split('T')[0],
			lastActive: leader.updatedAt.toISOString().split('T')[0]
		}));

		return json({
			leaders: formattedLeaders,
			pagination: {
				page,
				limit,
				totalCount,
				totalPages: Math.ceil(totalCount / limit)
			}
		});

	} catch (error) {
		console.error('Error fetching leaders:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

// POST - Create a new leader
export const POST: RequestHandler = async ({ request, locals }) => {
	try {
		// Check admin authentication
		if (!locals.user || !locals.user.isAdmin) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		const data = await request.json();

		// Validate required fields
		if (!data.name || !data.position) {
			return json({ error: 'Name and position are required' }, { status: 400 });
		}

		// Create leader
		const leader = await prisma.leader.create({
			data: {
				name: data.name.trim(),
				position: data.position.trim(),
				party: data.party?.trim() || null,
				state: data.state?.trim() || null,
				lga: data.lga?.trim() || null,
				bio: data.bio?.trim() || null,
				detailedBio: data.detailedBio?.trim() || null,
				avatarUrl: data.avatarUrl?.trim() || null,
				coverImageUrl: data.coverImageUrl?.trim() || null,
				publicSentiment: data.publicSentiment?.trim() || 'Neutral',
				youtubeVideoUrl: data.youtubeVideoUrl?.trim() || null,
				education: data.education?.trim() || null,
				background: data.background?.trim() || null,
				popularityScore: data.popularityScore || 75,
				isVerified: data.isVerified || false
			},
			include: {
				_count: {
					select: {
						followers: true,
						ratings: true,
						comments: true
					}
				}
			}
		});

		// Format response to match React app structure
		const formattedLeader = {
			id: leader.id,
			name: leader.name,
			position: leader.position,
			party: leader.party,
			state: leader.state,
			lga: leader.lga,
			bio: leader.bio,
			detailedBio: leader.detailedBio,
			avatarUrl: leader.avatarUrl,
			coverImageUrl: leader.coverImageUrl,
			followers: leader.followersCount,
			currentRating: Number(leader.ratingAverage),
			totalRatings: leader.ratingCount,
			publicSentiment: leader.publicSentiment,
			youtubeVideoUrl: leader.youtubeVideoUrl,
			education: leader.education,
			background: leader.background,
			popularityScore: leader.popularityScore,
			isVerified: leader.isVerified,
			isFollowed: false,
			commentsCount: leader._count.comments,
			isActive: true,
			createdAt: leader.createdAt.toISOString().split('T')[0],
			lastActive: leader.updatedAt.toISOString().split('T')[0]
		};

		return json({ leader: formattedLeader }, { status: 201 });

	} catch (error) {
		console.error('Error creating leader:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
