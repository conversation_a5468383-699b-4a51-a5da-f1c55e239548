// Individual Leader API - Update and Delete operations
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma.js';
import type { RequestHandler } from './$types';

// GET - Get a specific leader by ID
export const GET: RequestHandler = async ({ params, locals }) => {
	try {
		// Check admin authentication
		if (!locals.user || !locals.user.isAdmin) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		const leader = await prisma.leader.findUnique({
			where: { id: params.id },
			include: {
				_count: {
					select: {
						followers: true,
						ratings: true,
						comments: true
					}
				}
			}
		});

		if (!leader) {
			return json({ error: 'Leader not found' }, { status: 404 });
		}

		// Format response to match React app structure
		const formattedLeader = {
			id: leader.id,
			name: leader.name,
			position: leader.position,
			party: leader.party,
			state: leader.state,
			lga: leader.lga,
			bio: leader.bio,
			detailedBio: leader.detailedBio,
			avatarUrl: leader.avatarUrl,
			coverImageUrl: leader.coverImageUrl,
			followers: leader.followersCount,
			currentRating: Number(leader.ratingAverage),
			totalRatings: leader.ratingCount,
			publicSentiment: leader.publicSentiment,
			youtubeVideoUrl: leader.youtubeVideoUrl,
			education: leader.education,
			background: leader.background,
			popularityScore: leader.popularityScore,
			isVerified: leader.isVerified,
			isFollowed: false,
			commentsCount: leader._count.comments,
			isActive: true,
			createdAt: leader.createdAt.toISOString().split('T')[0],
			lastActive: leader.updatedAt.toISOString().split('T')[0]
		};

		return json({ leader: formattedLeader });

	} catch (error) {
		console.error('Error fetching leader:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

// PUT - Update a leader
export const PUT: RequestHandler = async ({ params, request, locals }) => {
	try {
		// Check admin authentication
		if (!locals.user || !locals.user.isAdmin) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		const data = await request.json();

		// Validate required fields
		if (!data.name || !data.position) {
			return json({ error: 'Name and position are required' }, { status: 400 });
		}

		// Check if leader exists
		const existingLeader = await prisma.leader.findUnique({
			where: { id: params.id }
		});

		if (!existingLeader) {
			return json({ error: 'Leader not found' }, { status: 404 });
		}

		// Update leader
		const leader = await prisma.leader.update({
			where: { id: params.id },
			data: {
				name: data.name.trim(),
				position: data.position.trim(),
				party: data.party?.trim() || null,
				state: data.state?.trim() || null,
				lga: data.lga?.trim() || null,
				bio: data.bio?.trim() || null,
				detailedBio: data.detailedBio?.trim() || null,
				avatarUrl: data.avatarUrl?.trim() || null,
				coverImageUrl: data.coverImageUrl?.trim() || null,
				publicSentiment: data.publicSentiment?.trim() || null,
				youtubeVideoUrl: data.youtubeVideoUrl?.trim() || null,
				education: data.education?.trim() || null,
				background: data.background?.trim() || null,
				popularityScore: data.popularityScore ? parseInt(data.popularityScore) : 75,
				isVerified: data.isVerified !== undefined ? data.isVerified : false
			},
			include: {
				_count: {
					select: {
						followers: true,
						ratings: true,
						comments: true
					}
				}
			}
		});

		// Format response to match React app structure
		const formattedLeader = {
			id: leader.id,
			name: leader.name,
			position: leader.position,
			party: leader.party,
			state: leader.state,
			lga: leader.lga,
			bio: leader.bio,
			detailedBio: leader.detailedBio,
			avatarUrl: leader.avatarUrl,
			coverImageUrl: leader.coverImageUrl,
			followers: leader.followersCount,
			currentRating: Number(leader.ratingAverage),
			totalRatings: leader.ratingCount,
			publicSentiment: leader.publicSentiment,
			youtubeVideoUrl: leader.youtubeVideoUrl,
			education: leader.education,
			background: leader.background,
			popularityScore: leader.popularityScore,
			isVerified: leader.isVerified,
			isFollowed: false,
			commentsCount: leader._count.comments,
			isActive: true,
			createdAt: leader.createdAt.toISOString().split('T')[0],
			lastActive: leader.updatedAt.toISOString().split('T')[0]
		};

		return json({ leader: formattedLeader });

	} catch (error) {
		console.error('Error updating leader:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

// DELETE - Delete a leader
export const DELETE: RequestHandler = async ({ params, locals }) => {
	try {
		// Check admin authentication
		if (!locals.user || !locals.user.isAdmin) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check if leader exists
		const existingLeader = await prisma.leader.findUnique({
			where: { id: params.id }
		});

		if (!existingLeader) {
			return json({ error: 'Leader not found' }, { status: 404 });
		}

		// Delete leader (this will cascade delete related records)
		await prisma.leader.delete({
			where: { id: params.id }
		});

		return json({ message: 'Leader deleted successfully' });

	} catch (error) {
		console.error('Error deleting leader:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
