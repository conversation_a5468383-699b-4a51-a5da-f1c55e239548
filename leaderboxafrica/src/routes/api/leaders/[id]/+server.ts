// Individual Leader API - Public access to leader profiles
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma.js';
import type { RequestHandler } from './$types';

// GET - Get a specific leader by ID for public access
export const GET: RequestHandler = async ({ params }) => {
	try {
		const leader = await prisma.leader.findUnique({
			where: { id: params.id },
			include: {
				_count: {
					select: {
						followers: true,
						ratings: true,
						comments: true
					}
				}
			}
		});

		if (!leader) {
			return json({ error: 'Leader not found' }, { status: 404 });
		}

		// Format response to match React app structure exactly
		const formattedLeader = {
			id: leader.id,
			name: leader.name,
			position: leader.position,
			party: leader.party,
			state: leader.state,
			lga: leader.lga,
			bio: leader.bio,
			detailedBio: leader.detailedBio,
			avatarUrl: leader.avatarUrl,
			coverImageUrl: leader.coverImageUrl,
			followers: leader.followersCount,              // React app uses 'followers'
			currentRating: Number(leader.ratingAverage),   // React app uses 'currentRating'
			totalRatings: leader.ratingCount,              // React app uses 'totalRatings'
			publicSentiment: leader.publicSentiment || 'Neutral',
			youtubeVideoUrl: leader.youtubeVideoUrl,
			education: leader.education,
			background: leader.background,
			popularityScore: leader.popularityScore || 75,
			isVerified: leader.isVerified || false,
			isFollowed: false,                             // Will be computed client-side based on user
			commentsCount: leader._count.comments,
			// Additional fields for profile page
			achievements: [], // TODO: Add achievements table/field
			socialMedia: {}, // TODO: Add social media fields
			recentActivity: [], // TODO: Add activity tracking
			polls: [] // TODO: Add polls relation
		};

		return json({ leader: formattedLeader });

	} catch (error) {
		console.error('Error fetching leader:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
