// Public Leaders API - For homepage and public access
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma.js';
import type { RequestHandler } from './$types';

// GET - List all leaders for public access
export const GET: RequestHandler = async ({ url }) => {
	try {
		// Parse query parameters
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = parseInt(url.searchParams.get('limit') || '50'); // Higher limit for public view
		const search = url.searchParams.get('search') || '';
		const party = url.searchParams.get('party') || '';
		const state = url.searchParams.get('state') || '';
		const position = url.searchParams.get('position') || '';

		// Build where clause
		const where: any = {};
		
		if (search) {
			where.OR = [
				{ name: { contains: search, mode: 'insensitive' } },
				{ position: { contains: search, mode: 'insensitive' } },
				{ party: { contains: search, mode: 'insensitive' } },
				{ state: { contains: search, mode: 'insensitive' } }
			];
		}

		if (party && party !== 'all') {
			where.party = party;
		}

		if (state && state !== 'all') {
			where.state = state;
		}

		if (position && position !== 'all') {
			where.position = { contains: position, mode: 'insensitive' };
		}

		// Calculate offset
		const offset = (page - 1) * limit;

		// Get leaders with pagination
		const [leaders, totalCount] = await Promise.all([
			prisma.leader.findMany({
				where,
				orderBy: [
					{ popularityScore: 'desc' },
					{ followersCount: 'desc' },
					{ name: 'asc' }
				],
				skip: offset,
				take: limit,
				include: {
					_count: {
						select: {
							followers: true,
							ratings: true,
							comments: true
						}
					}
				}
			}),
			prisma.leader.count({ where })
		]);

		// Format response to match React app structure exactly
		const formattedLeaders = leaders.map(leader => ({
			id: leader.id,
			name: leader.name,
			position: leader.position,
			party: leader.party,
			state: leader.state,
			lga: leader.lga,
			bio: leader.bio,
			detailedBio: leader.detailedBio,
			avatarUrl: leader.avatarUrl,
			coverImageUrl: leader.coverImageUrl,
			followers: leader.followersCount,              // React app uses 'followers'
			currentRating: Number(leader.ratingAverage),   // React app uses 'currentRating'
			totalRatings: leader.ratingCount,              // React app uses 'totalRatings'
			publicSentiment: leader.publicSentiment || 'Neutral',
			youtubeVideoUrl: leader.youtubeVideoUrl,
			education: leader.education,
			background: leader.background,
			popularityScore: leader.popularityScore || 75,
			isVerified: leader.isVerified || false,
			isFollowed: false,                             // Will be computed client-side based on user
			commentsCount: leader._count.comments
		}));

		// For public API, return simple array if no pagination requested
		if (!url.searchParams.has('page')) {
			return json(formattedLeaders);
		}

		// Return paginated response
		return json({
			leaders: formattedLeaders,
			pagination: {
				page,
				limit,
				totalCount,
				totalPages: Math.ceil(totalCount / limit)
			}
		});

	} catch (error) {
		console.error('Error fetching leaders:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
