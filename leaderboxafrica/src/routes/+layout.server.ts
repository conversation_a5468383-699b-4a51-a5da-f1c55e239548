// Root Layout Server Load Function
// Provides authentication state to all pages

import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ locals }) => {
	// Pass authentication state from server to client
	return {
		user: locals.user ? {
			id: locals.user.id,
			email: locals.user.email,
			name: locals.user.name,
			role: locals.user.role,
			isAdmin: locals.user.isAdmin,
			state: locals.user.state,
			lga: locals.user.lga,
			gender: locals.user.gender,
			party: locals.user.party,
			avatarUrl: locals.user.avatarUrl,
			onboardingComplete: locals.user.onboardingComplete,
			status: locals.user.status
		} : null,
		session: locals.session ? {
			id: locals.session.id,
			expiresAt: locals.session.expiresAt
		} : null
	};
};
