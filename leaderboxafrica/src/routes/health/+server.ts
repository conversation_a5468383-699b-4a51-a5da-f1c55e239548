// Health check endpoint for deployment monitoring
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
	try {
		// Basic health check
		const health = {
			status: 'healthy',
			timestamp: new Date().toISOString(),
			uptime: process.uptime(),
			environment: process.env.NODE_ENV || 'development',
			version: '1.0.0',
			services: {
				database: 'unknown', // Will be updated when we add database checks
				redis: 'unknown'      // Will be updated when we add Redis checks
			}
		};

		// TODO: Add database connectivity check
		// TODO: Add Redis connectivity check
		// TODO: Add any other service checks

		return json(health, {
			status: 200,
			headers: {
				'Cache-Control': 'no-cache, no-store, must-revalidate',
				'Pragma': 'no-cache',
				'Expires': '0'
			}
		});
	} catch (error) {
		console.error('Health check failed:', error);
		
		return json(
			{
				status: 'unhealthy',
				timestamp: new Date().toISOString(),
				error: error instanceof Error ? error.message : 'Unknown error'
			},
			{
				status: 503,
				headers: {
					'Cache-Control': 'no-cache, no-store, must-revalidate',
					'Pragma': 'no-cache',
					'Expires': '0'
				}
			}
		);
	}
};
