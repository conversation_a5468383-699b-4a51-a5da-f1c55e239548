<!-- Homepage - Exact replica of React HomePage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { authStore } from '$lib/stores/auth.js';
	import { goto } from '$app/navigation';
	import { fly } from 'svelte/transition';

	// Layout
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';

	// UI Components
	import Input from '$lib/components/ui/Input.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardFooter from '$lib/components/ui/CardFooter.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import Select from '$lib/components/ui/Select.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';
	import { cn } from '$lib/utils.js';

	// State variables
	let searchTerm = '';
	let filters = { party: 'all', state: 'all', position: 'all' };
	let showSuggestLeaderDialog = false;
	let showMobileFilters = false;
	let leaders: any[] = [];
	let loading = true;

	// Reactive variables
	$: user = $authStore.user;
	// $: isAuthenticated = $authStore.isAuthenticated;

	// Computed values
	$: parties = ['all', ...new Set(leaders.map(l => l.party).filter(Boolean))];
	$: states = ['all', ...new Set(leaders.map(l => l.state).filter(Boolean))];
	$: positions = ['all', ...new Set(leaders.map(l => l.position).filter(Boolean))];

	$: recommendedLeaders = (() => {
		let sortedLeaders;
		if (user) {
			// Logged-in user logic
			sortedLeaders = leaders
				.map(leader => {
					let score = 0;
					if (leader.party === user.party) score += 10;
					if (leader.state === user.state) score += 5;
					score += (leader.followers || 0) / 1000; // Add popularity
					return { ...leader, recommendationScore: score };
				})
				.sort((a, b) => b.recommendationScore - a.recommendationScore);
		} else {
			// Logged-out user logic
			const shuffled = [...leaders].sort(() => 0.5 - Math.random());
			sortedLeaders = shuffled.sort((a, b) => (b.popularityScore || 0) - (a.popularityScore || 0));
		}
		return sortedLeaders;
	})();

	$: filteredLeaders = recommendedLeaders
		.filter(leader => leader.name.toLowerCase().includes(searchTerm.toLowerCase()))
		.filter(leader => filters.party === 'all' || leader.party === filters.party)
		.filter(leader => filters.state === 'all' || leader.state === filters.state)
		.filter(leader => filters.position === 'all' || leader.position === filters.position);

	// Functions
	function handleFilterChange(type: string, value: string) {
		filters = { ...filters, [type]: value };
	}

	function getPartyColor(party: string) {
		const colors: Record<string, string> = {
			'APC': 'bg-blue-500 text-white',
			'PDP': 'bg-green-600 text-white',
			'LP': 'bg-red-500 text-white',
			'NNPP': 'bg-orange-500 text-white',
			'APGA': 'bg-yellow-500 text-black',
		};
		return colors[party] || 'bg-gray-500 text-white';
	}

	function handleFollowToggle(e: Event, leader: any) {
		e.preventDefault();
		e.stopPropagation();
		if (!user) {
			// TODO: Show toast notification
			console.log("Login Required", "Please log in to follow leaders.");
			return;
		}
		if (leader.isFollowed) {
			// unfollowLeader(leader.id);
			console.log(`Unfollowed ${leader.name}`);
		} else {
			// followLeader(leader.id);
			console.log(`Followed ${leader.name}`);
		}
	}

	// Load leaders data
	async function loadLeaders() {
		try {
			const response = await fetch('/api/leaders');
			if (!response.ok) {
				throw new Error('Failed to fetch leaders');
			}
			const data = await response.json();
			leaders = data.leaders || data; // Handle both paginated and direct array responses
		} catch (error) {
			console.error('Error loading leaders:', error);
			// Fallback to empty array on error
			leaders = [];
		} finally {
			loading = false;
		}
	}

	onMount(() => {
		authStore.initialize();
		loadLeaders();
	});
</script>

<svelte:head>
	<title>LeaderBox - Connect With Political Leaders in Nigeria</title>
	<meta name="description" content="LeaderBox is a political social media platform where political leaders and citizens connect, engage and drive accountability." />
	<meta name="keywords" content="Nigeria, politics, leaders, democracy, accountability" />
</svelte:head>

<MainLayout>
	<div
		class="py-8"
		in:fly={{ opacity: 0, duration: 500 }}
	>
		<!-- Hero Section with exact React styling -->
		<header class="text-center mb-10 civic-icons-bg py-10 rounded-xl">
			<h1 class="text-4xl md:text-5xl font-extrabold tracking-tight gradient-text from-primary to-accent">
				Connect With 11,000+ Political Leaders in Nigeria
			</h1>
			<p class="mt-4 text-lg text-muted-foreground max-w-3xl mx-auto">
				LeaderBox is a political social media platform where political leaders and citizens connect, engage and drive accountability.
			</p>
		</header>

	<div class="sticky top-[70px] md:top-[80px] z-30 bg-background/80 backdrop-blur-sm py-4 px-2 -mx-2 rounded-b-lg shadow-sm mb-4">
		<div class="max-w-5xl mx-auto flex flex-col md:flex-row gap-3 items-center">
			<div class="relative w-full md:flex-grow">
				<Input
					type="search"
					placeholder="Search for a political leader..."
					bind:value={searchTerm}
					className="pl-4 pr-10 text-base h-11"
				/>
				<!-- Search Icon -->
				<svg class="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
				</svg>
			</div>
			<div class="hidden md:flex gap-3 items-center">
				<Select
					value={filters.position}
					className="w-full lg:w-36 h-11"
					placeholder="Position"
					options={positions.map(pos => ({ value: pos, label: pos === 'all' ? 'All' : pos }))}
					on:change={(e) => handleFilterChange('position', e.detail)}
				>
					<!-- Briefcase Icon -->
					<svg slot="icon" class="mr-2 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V8m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v2" />
					</svg>
				</Select>
				<Select
					value={filters.party}
					className="w-full lg:w-32 h-11"
					placeholder="Party"
					options={parties.map(party => ({ value: party, label: party === 'all' ? 'All' : party }))}
					on:change={(e) => handleFilterChange('party', e.detail)}
				>
					<!-- Users Icon -->
					<svg slot="icon" class="mr-2 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
					</svg>
				</Select>
				<Select
					value={filters.state}
					className="w-full lg:w-32 h-11"
					placeholder="State"
					options={states.map(state => ({ value: state, label: state === 'all' ? 'All' : state }))}
					on:change={(e) => handleFilterChange('state', e.detail)}
				>
					<!-- MapPin Icon -->
					<svg slot="icon" class="mr-2 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
					</svg>
				</Select>
			</div>
			<div class="md:hidden w-full">
				<Button variant="outline" className="w-full flex items-center justify-center h-11" on:click={() => showMobileFilters = !showMobileFilters}>
					<!-- SlidersHorizontal Icon -->
					<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
					</svg>
					<span>{showMobileFilters ? 'Hide Filters' : 'Show Filters'}</span>
				</Button>
			</div>
		</div>
		{#if showMobileFilters}
			<div
				class="md:hidden mt-3 grid grid-cols-1 sm:grid-cols-3 gap-3"
				in:fly={{ y: -10, duration: 200 }}
			>
				<Select
					value={filters.position}
					className="w-full h-11"
					placeholder="Position"
					options={positions.map(pos => ({ value: pos, label: pos === 'all' ? 'All Positions' : pos }))}
					on:change={(e) => handleFilterChange('position', e.detail)}
				/>
				<Select
					value={filters.party}
					className="w-full h-11"
					placeholder="Party"
					options={parties.map(party => ({ value: party, label: party === 'all' ? 'All Parties' : party }))}
					on:change={(e) => handleFilterChange('party', e.detail)}
				/>
				<Select
					value={filters.state}
					className="w-full h-11"
					placeholder="State"
					options={states.map(state => ({ value: state, label: state === 'all' ? 'All States' : state }))}
					on:change={(e) => handleFilterChange('state', e.detail)}
				/>
			</div>
		{/if}
	</div>

	<h2 class="text-2xl font-bold text-foreground mb-6">Suggested For You</h2>

	<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
		{#each filteredLeaders as leader (leader.id)}
			<div
				class="h-full"
				in:fly={{ opacity: 0, scale: 0.9, duration: 300 }}
				style="--delay: {Math.random() * 200}ms"
			>
				<Card className="modern-card h-full flex flex-col text-center overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
					<CardHeader className="items-center p-4">
						<a href="/leaders/{leader.id}" class="block">
							<Avatar
								className="w-24 h-24 mb-2 border-4 border-primary/20"
								src={leader.avatarUrl}
								alt={leader.name}
								fallback={leader.name.split(' ').map(n => n[0]).join('')}
							/>
							<h3 class="text-lg font-semibold text-foreground hover:text-primary transition-colors truncate w-full" title={leader.name}>
								{leader.name}
							</h3>
						</a>
					</CardHeader>
					<CardContent className="flex-grow p-4 pt-0">
						<p class="text-sm text-muted-foreground truncate" title={leader.position}>{leader.position}</p>
						<div class="flex justify-center items-center gap-2 mt-2">
							<Badge className={cn('text-xs', getPartyColor(leader.party))}>{leader.party}</Badge>
							<Badge variant="secondary" className="text-xs">{leader.state}</Badge>
						</div>
						<Button
							size="sm"
							className="w-full mt-3"
							variant={user && leader.isFollowed ? "secondary" : "default"}
							on:click={(e) => handleFollowToggle(e, leader)}
						>
							{#if user && leader.isFollowed}
								<!-- UserCheck Icon -->
								<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
								</svg>
								Following
							{:else}
								<!-- UserPlus Icon -->
								<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
								</svg>
								Follow
							{/if}
						</Button>
					</CardContent>
					<CardFooter className="p-3 bg-secondary/30 flex justify-between items-center text-sm">
						<div class="flex items-center text-amber-600 font-bold">
							<!-- Star Icon -->
							<svg class="mr-1 h-4 w-4 fill-current text-amber-500" fill="currentColor" viewBox="0 0 20 20">
								<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
							</svg>
							{leader.currentRating.toFixed(1)}
						</div>
						<div class="flex items-center text-muted-foreground">
							<!-- Users Icon -->
							<svg class="mr-1 h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
							</svg>
							{leader.followers.toLocaleString()}
						</div>
					</CardFooter>
				</Card>
			</div>
		{/each}
	</div>

	{#if filteredLeaders.length === 0}
		<div class="text-center py-16 bg-card border border-border rounded-xl shadow-lg col-span-full">
			<!-- Search Icon -->
			<svg class="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
			</svg>
			<p class="text-xl font-semibold text-foreground">No Leaders Found</p>
			<p class="text-muted-foreground mt-2">
				{searchTerm ? `No leaders match "${searchTerm}". Try a different search or suggest this leader.` : "Try adjusting your filters."}
			</p>
			{#if searchTerm}
				<Button className="mt-4" on:click={() => showSuggestLeaderDialog = true}>
					<!-- UserPlus Icon -->
					<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
					</svg>
					Suggest '{searchTerm}' to be added?
				</Button>
			{/if}
		</div>
	{/if}

	<!-- Suggest Leader Dialog -->
	{#if showSuggestLeaderDialog}
		<!-- TODO: Replace with proper SuggestLeaderDialog component -->
		<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
			<div class="bg-white rounded-lg p-6 max-w-md w-full">
				<h3 class="text-lg font-semibold mb-4">Suggest a Leader</h3>
				<p class="text-gray-600 mb-4">
					Would you like to suggest "{searchTerm}" to be added to LeaderBox?
				</p>
				<div class="flex gap-3">
					<Button
						className="flex-1"
						on:click={() => {
							// TODO: Implement suggest leader functionality
							console.log(`Suggested leader: ${searchTerm}`);
							showSuggestLeaderDialog = false;
						}}
					>
						Yes, Suggest
					</Button>
					<Button
						variant="outline"
						className="flex-1"
						on:click={() => showSuggestLeaderDialog = false}
					>
						Cancel
					</Button>
				</div>
			</div>
		</div>
	{/if}
	</div>
</MainLayout>


