<!-- User Profile Page - Using real authentication system -->
<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import UserProfileHeaderCard from '$lib/components/user-profile/UserProfileHeaderCard.svelte';
	import UserProfileStatsCard from '$lib/components/user-profile/UserProfileStatsCard.svelte';
	import UserActivityTabsContent from '$lib/components/user-profile/UserActivityTabsContent.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import Progress from '$lib/components/ui/Progress.svelte';
	import { Award } from 'lucide-svelte';

	// State
	let profileUser = $state(null);
	let loading = $state(true);
	let currentUser = $state(null);
	let leaders = $state([]);
	let allRegisteredUsers = $state([]);
	let userProfileLikes = $state({});

	// Derived values
	let userId = $derived($page.params.userId);
	let isCurrentUserProfile = $derived(currentUser?.id === profileUser?.id);
	let isLikedByCurrentUser = $derived(userProfileLikes[userId]?.includes(currentUser?.id) || false);
	let likesCount = $derived(profileUser?.profileLikesCount || 0);

	// Initialize data on mount
	onMount(async () => {
		await loadInitialData();
		await loadUserProfile();
	});

	// Load initial data (current user, leaders, etc.)
	async function loadInitialData() {
		try {
			// Get current user from auth validation
			const authResponse = await fetch('/api/auth/validate');
			if (authResponse.ok) {
				const authData = await authResponse.json();
				currentUser = authData.user;
			}

			// Load leaders data
			const leadersResponse = await fetch('/api/leaders');
			if (leadersResponse.ok) {
				const leadersData = await leadersResponse.json();
				leaders = leadersData.leaders || [];
			}

			// Mock registered users data (since there's no API endpoint for this yet)
			allRegisteredUsers = [
				{
					id: 'd6c54e09-aae0-44b9-a0ed-1688e50482b4',
					name: 'John Doe',
					email: '<EMAIL>',
					avatarUrl: null,
					state: 'Lagos',
					lga: 'Ikeja',
					party: 'APC',
					position: null,
					isLeader: false,
					bio: 'Political enthusiast and community advocate.',
					createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
					followers: 0,
					profileLikesCount: 150,
					petitionsCreated: 5,
					groupMembersCount: 50,
					followedLeaders: leaders.slice(0, 3).map(l => l.id)
				},
				{
					id: 'd6c5',
					name: 'Jane Smith',
					email: '<EMAIL>',
					avatarUrl: null,
					state: 'Abuja',
					lga: 'Garki',
					party: 'PDP',
					position: null,
					isLeader: false,
					bio: 'Community organizer and political activist.',
					createdAt: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000).toISOString(),
					followers: 0,
					profileLikesCount: 89,
					petitionsCreated: 3,
					groupMembersCount: 25,
					followedLeaders: leaders.slice(1, 4).map(l => l.id)
				}
			];

			// Mock user profile likes
			userProfileLikes = {
				'd6c54e09-aae0-44b9-a0ed-1688e50482b4': ['user1', 'user2', 'user3'],
				'd6c5': ['user4', 'user5']
			};

		} catch (error) {
			console.error('Error loading initial data:', error);
		}
	}

	// Load user profile data
	async function loadUserProfile() {
		loading = true;
		let foundUser = null;

		try {
			// First check if it's a leader
			if (userId && leaders.length > 0) {
				const leaderResponse = await fetch(`/api/leaders/${userId}`);
				if (leaderResponse.ok) {
					const leaderData = await leaderResponse.json();
					if (leaderData.leader) {
						const leader = leaderData.leader;
						foundUser = {
							id: leader.id,
							name: leader.name,
							email: `${leader.name.toLowerCase().replace(/\s+/g, '.')}@leader.example.com`,
							avatarUrl: leader.avatarUrl,
							state: leader.state,
							party: leader.party,
							position: leader.position,
							isLeader: true,
							bio: leader.bioSummary || 'Esteemed political figure.',
							createdAt: leader.createdAt || new Date(Date.now() - Math.random() * 1000 * 60 * 60 * 24 * 365 * 2).toISOString(),
							followers: leader.followersCount || 0,
							profileLikesCount: (leader.followersCount || 0) * 2,
							petitionsCreated: 0,
							groupMembersCount: 0,
						};
					}
				}
			}

			// If not found in leaders, check registered users
			if (!foundUser && Array.isArray(allRegisteredUsers) && allRegisteredUsers.length > 0) {
				foundUser = allRegisteredUsers.find(u => u.id === userId);
			}

			if (foundUser) {
				profileUser = foundUser;
			} else {
				// User not found
				console.error('User not found:', userId);
				goto('/');
				return;
			}

		} catch (error) {
			console.error('Error loading user profile:', error);
			goto('/');
		} finally {
			loading = false;
		}
	}

	// Event handlers
	function handleSendMessage() {
		if (!currentUser) {
			alert('Please log in to send messages.');
			goto('/login');
			return;
		}
		goto(`/messages?to=${profileUser?.id}`);
	}

	function handleLikeToggle() {
		if (!currentUser) {
			alert('Please log in to like profiles.');
			goto('/login');
			return;
		}
		// In a real app, this would call an API to toggle the like
		console.log('Toggle profile like for user:', userId);
		// For now, just update the local state
		if (isLikedByCurrentUser) {
			userProfileLikes[userId] = userProfileLikes[userId]?.filter(id => id !== currentUser.id) || [];
		} else {
			userProfileLikes[userId] = [...(userProfileLikes[userId] || []), currentUser.id];
		}
	}

	function handleEditProfile() {
		goto('/settings');
	}
</script>

<svelte:head>
	<title>{profileUser?.name || 'User Profile'} - LeaderBox</title>
</svelte:head>

{#if loading}
	<div class="flex justify-center items-center min-h-screen">
		<div class="text-center">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
			<p class="text-muted-foreground">Loading profile...</p>
		</div>
	</div>
{:else if profileUser}
	<div class="container mx-auto py-6 px-2 md:px-4 space-y-8">
		<!-- Profile Header -->
		<UserProfileHeaderCard 
			{profileUser}
			{isCurrentUserProfile}
			onSendMessage={handleSendMessage}
			onEditProfile={handleEditProfile}
			onLikeToggle={handleLikeToggle}
			{isLikedByCurrentUser}
		/>

		<!-- Influencer Progress Card (only for current user) -->
		{#if isCurrentUserProfile && likesCount < 1000}
			<Card class="modern-card bg-accent/10 border-accent/50 w-full overflow-hidden">
				<CardContent className="p-4 w-full">
					<div class="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full">
						<Award size={24} class="text-accent flex-shrink-0" />
						<div class="flex-1 min-w-0 w-full overflow-hidden">
							<h3 class="font-semibold text-foreground text-sm sm:text-base truncate">Become a Political Influencer</h3>
							<p class="text-xs text-muted-foreground mb-2 break-words">Get 1,000 profile likes to earn the Influencer badge!</p>
							<Progress value={(likesCount / 1000) * 100} className="mt-2 h-2 w-full" />
							<div class="flex justify-between items-center mt-1 gap-2 text-xs text-muted-foreground">
								<span class="truncate">{Math.round((likesCount / 1000) * 100)}% Complete</span>
								<span class="whitespace-nowrap">{likesCount} / 1,000</span>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		{/if}

		<!-- Profile Content Grid -->
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Stats Sidebar -->
			<div class="lg:col-span-1 space-y-6">
				<UserProfileStatsCard {profileUser} />
			</div>

			<!-- Activity Content -->
			<div class="lg:col-span-2">
				<UserActivityTabsContent {profileUser} {leaders} />
			</div>
		</div>
	</div>
{:else}
	<div class="flex justify-center items-center min-h-screen">
		<div class="text-center">
			<h2 class="text-2xl font-bold text-foreground mb-2">User Not Found</h2>
			<p class="text-muted-foreground mb-4">The user profile you're looking for doesn't exist.</p>
			<button 
				type="button"
				on:click={() => goto('/')}
				class="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
			>
				Go Home
			</button>
		</div>
	</div>
{/if}

<style>
	:global(.modern-card) {
		@apply bg-card border border-border rounded-lg shadow-sm;
	}
</style>
