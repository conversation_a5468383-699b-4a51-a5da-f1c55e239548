// Dashboard Page Server Load Function
// Protects the dashboard route and ensures user is authenticated

import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// Check if user is authenticated
	if (!locals.user || !locals.session) {
		// User is not authenticated, redirect to login
		throw redirect(302, '/login');
	}

	// User is authenticated, allow access to dashboard
	return {
		user: locals.user
	};
};
