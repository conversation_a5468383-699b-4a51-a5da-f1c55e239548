<!-- User Dashboard - Exact replica of React UserDashboardPage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { fly } from 'svelte/transition';
	import type { PageData } from './$types';
	

	
	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	
	// Dashboard Components
	import DashboardFeedTab from '$lib/components/dashboard/DashboardFeedTab.svelte';
	import DashboardActivityTab from '$lib/components/dashboard/DashboardActivityTab.svelte';
	import DashboardLeadersTab from '$lib/components/dashboard/DashboardLeadersTab.svelte';
	import ActivityLogItem from '$lib/components/dashboard/ActivityLogItem.svelte';
	import SuggestedActionCard from '$lib/components/dashboard/SuggestedActionCard.svelte';
	import ProfileImageReminder from '$lib/components/ProfileImageReminder.svelte';

	// Get server-provided data
	let { data }: { data: PageData } = $props();

	// User is guaranteed to exist due to server-side protection
	let user = data.user;

	// State
	let activeTab = 'feed';
	let loading = true;

	// Mock data - TODO: Replace with real API calls
	let followedLeadersList = [];
	let userGroups = [];
	let userBanterActivity = [];
	let userPollActivity = [];
	let userPetitionActivity = [];
	let customFeedItems = [];
	let suggestedActions = [];
	let activityTimeline = [];

	// Dashboard tabs configuration
	const dashboardTabs = [
		{ value: "feed", label: "Feed", icon: "layout-dashboard" },
		{ value: "groups", label: "Groups", icon: "users" },
		{ value: "banter", label: "Banter", icon: "message-square" },
		{ value: "polls", label: "Polls", icon: "bar-chart-2" },
		{ value: "petitions", label: "Petitions", icon: "file-text" },
		{ value: "leaders", label: "Leaders", icon: "user-check" }
	];

	// Load dashboard data
	async function loadDashboardData() {
		try {
			loading = true;
			
			// TODO: Replace with actual API calls
			// Simulate API delay
			await new Promise(resolve => setTimeout(resolve, 1000));

			// Mock followed leaders
			followedLeadersList = [
				{
					id: '1',
					name: 'Bola Ahmed Tinubu',
					position: 'President',
					avatarUrl: ''
				},
				{
					id: '2',
					name: 'Peter Obi',
					position: 'Former Governor',
					avatarUrl: ''
				}
			];

			// Mock user groups
			userGroups = [
				{
					id: '1',
					name: 'Lagos State Development',
					createdAt: '2024-01-15T10:00:00Z'
				},
				{
					id: '2',
					name: 'Youth in Politics',
					createdAt: '2024-01-10T15:30:00Z'
				}
			];

			// Mock banter activity
			userBanterActivity = [
				{
					id: '1',
					title: 'Discussion on Economic Policies',
					createdAt: '2024-01-20T09:00:00Z'
				},
				{
					id: '2',
					title: 'Infrastructure Development in Nigeria',
					createdAt: '2024-01-18T14:20:00Z'
				}
			];

			// Mock poll activity
			userPollActivity = [
				{
					id: '1',
					topic: 'Budget Allocation for Education',
					createdAt: '2024-01-19T11:15:00Z'
				}
			];

			// Mock petition activity
			userPetitionActivity = [
				{
					id: '1',
					title: 'Improve Road Infrastructure in Lagos',
					createdAt: '2024-01-17T16:45:00Z',
					createdBy: user.name
				}
			];

			// Mock feed items
			customFeedItems = [
				{
					type: 'leader_followed_update',
					data: {
						id: '1',
						name: 'Bola Ahmed Tinubu',
						update: 'shared an update on economic reforms'
					},
					timestamp: '2024-01-20T08:30:00Z'
				},
				{
					type: 'new_petition_location',
					data: {
						id: '2',
						title: 'Better Healthcare for All Nigerians',
						location: 'Nigeria'
					},
					timestamp: '2024-01-19T12:00:00Z'
				},
				{
					type: 'poll_voted',
					data: {
						id: '1',
						topic: 'Budget Allocation for Education'
					},
					timestamp: '2024-01-19T11:15:00Z'
				}
			];

			// Mock suggested actions
			suggestedActions = [
				{
					title: 'Rate a Leader',
					description: 'Share your opinion on political leaders',
					actionText: 'Rate Now',
					link: '/',
					icon: 'star'
				},
				{
					title: 'Join a Group',
					description: 'Connect with like-minded citizens',
					actionText: 'Explore',
					link: '/groups',
					icon: 'users'
				},
				{
					title: 'Vote on Polls',
					description: 'Make your voice heard on important issues',
					actionText: 'Vote',
					link: '/polls',
					icon: 'bar-chart'
				}
			];

			// Mock activity timeline
			activityTimeline = [
				{
					type: 'voted_poll',
					text: 'You voted on "Budget Allocation for Education"',
					timestamp: '2024-01-19T11:15:00Z'
				},
				{
					type: 'rated_leader',
					text: 'You rated Bola Ahmed Tinubu',
					timestamp: '2024-01-18T16:30:00Z'
				},
				{
					type: 'signed_petition',
					text: 'You signed "Improve Road Infrastructure in Lagos"',
					timestamp: '2024-01-17T16:45:00Z'
				},
				{
					type: 'created_banter',
					text: 'You started a discussion on "Economic Policies"',
					timestamp: '2024-01-16T10:20:00Z'
				}
			];

		} catch (error) {
			console.error('Failed to load dashboard data:', error);
		} finally {
			loading = false;
		}
	}

	// Functions
	function getIconSvg(iconName: string) {
		const icons = {
			'layout-dashboard': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v4" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4" />`,
			'users': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />`,
			'message-square': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />`,
			'bar-chart-2': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />`,
			'file-text': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />`,
			'user-check': `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />`
		};
		return icons[iconName] || '';
	}

	onMount(() => {
		// User is guaranteed to exist due to server-side protection
		loadDashboardData();
	});
</script>

<svelte:head>
	<title>Dashboard - LeaderBox</title>
	<meta name="description" content="Your personalized political dashboard. Stay updated with leaders, polls, petitions, and political discussions." />
</svelte:head>

<!-- User is guaranteed to be authenticated due to server-side protection -->
<div
	class="py-6 px-2 md:px-4 space-y-8"
	in:fly={{ opacity: 0, y: 20, duration: 500 }}
>
			<!-- Header -->
			<header class="mb-8">
				<h1 class="text-3xl md:text-4xl font-extrabold text-foreground">
					Welcome back, <span class="gradient-text">{user.name || user.email.split('@')[0]}!</span>
				</h1>
				<p class="text-lg text-muted-foreground mt-1">Here's what's happening in your political sphere.</p>
			</header>

			<!-- Profile Image Reminder -->
			<ProfileImageReminder />

			<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
				<!-- Main Content -->
				<div class="lg:col-span-2 space-y-6">
					<!-- Tabs -->
					<div class="w-full">
						<!-- Tab Navigation -->
						<div class="overflow-x-auto no-scrollbar">
							<div class="inline-flex w-max sm:w-full sm:grid sm:grid-cols-6 bg-secondary/50 rounded-lg p-1 mb-2">
								{#each dashboardTabs as tab}
									<button
										class="dashboard-tab-trigger {activeTab === tab.value ? 'active' : ''}"
										on:click={() => activeTab = tab.value}
									>
										<svg class="w-4 h-4 mr-1.5 hidden sm:inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											{@html getIconSvg(tab.icon)}
										</svg>
										{tab.label}
									</button>
								{/each}
							</div>
						</div>

						<!-- Tab Content -->
						{#if activeTab === 'feed'}
							<DashboardFeedTab items={customFeedItems} />
						{:else if activeTab === 'groups'}
							<DashboardActivityTab 
								activity={userGroups.map(g => ({...g, title: g.name, createdAt: g.createdAt}))} 
								type="Group" 
								linkPrefix="/group"
								emptyMessage="You haven't joined any groups yet."
								ctaLink="/groups"
								ctaText="Discover groups!"
							/>
						{:else if activeTab === 'banter'}
							<DashboardActivityTab 
								activity={userBanterActivity} 
								type="Banter" 
								linkPrefix="/banter"
								emptyMessage="No banter activity yet."
								ctaLink="/banter-room"
								ctaText="Join or start a discussion!"
							/>
						{:else if activeTab === 'polls'}
							<DashboardActivityTab 
								activity={userPollActivity} 
								type="Poll" 
								linkPrefix="/poll"
								emptyMessage="You haven't voted on any polls yet."
								ctaLink="/polls"
								ctaText="Explore polls now!"
							/>
						{:else if activeTab === 'petitions'}
							<DashboardActivityTab 
								activity={userPetitionActivity} 
								type="Petition" 
								linkPrefix="/petition"
								emptyMessage="No petition activity yet."
								ctaLink="/petitions"
								ctaText="Start or sign a petition!"
							/>
						{:else if activeTab === 'leaders'}
							<DashboardLeadersTab leaders={followedLeadersList} />
						{/if}
					</div>
				</div>

				<!-- Sidebar -->
				<div class="space-y-6">
					<!-- Suggested Actions -->
					<Card className="modern-card">
						<CardHeader>
							<h3 class="text-lg text-primary flex items-center">
								<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
								</svg>
								Suggested Actions
							</h3>
						</CardHeader>
						<CardContent className="space-y-3">
							{#if suggestedActions.length > 0}
								{#each suggestedActions as action}
									<SuggestedActionCard suggestion={action} />
								{/each}
							{:else}
								<p class="text-sm text-muted-foreground p-4 text-center">No specific suggestions right now. Keep exploring!</p>
							{/if}
						</CardContent>
					</Card>

					<!-- Activity Timeline -->
					<Card className="modern-card">
						<CardHeader>
							<h3 class="text-lg text-primary flex items-center">
								<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
								</svg>
								Activity Timeline
							</h3>
						</CardHeader>
						<CardContent>
							<div class="h-[250px] overflow-y-auto pr-3 space-y-1">
								{#if activityTimeline.length > 0}
									{#each activityTimeline as activity}
										<ActivityLogItem {activity} />
									{/each}
								{:else}
									<p class="text-sm text-muted-foreground p-4 text-center">No recent activity to show.</p>
								{/if}
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
</div>

<style>
	.no-scrollbar::-webkit-scrollbar {
		display: none;
	}
	.no-scrollbar {
		-ms-overflow-style: none; 
		scrollbar-width: none; 
	}

	.dashboard-tab-trigger {
		@apply flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 text-muted-foreground hover:text-foreground hover:bg-background/50;
	}

	.dashboard-tab-trigger.active {
		@apply bg-background text-primary shadow-md;
	}

	.gradient-text {
		background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}
</style>
