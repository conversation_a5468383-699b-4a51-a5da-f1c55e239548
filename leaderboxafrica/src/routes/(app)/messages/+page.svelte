<!-- Messages Page - Svelte conversion of React MessagesPage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { fly } from 'svelte/transition';
	import type { PageData } from './$types';

	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import { Send, Search, MessageCircle } from 'lucide-svelte';

	// Get server-provided data
	let { data }: { data: PageData } = $props();
	let user = data.user;

	// State
	let selectedConversationId = $state(null);
	let messageInput = $state('');
	let searchTerm = $state('');

	// Mock data (same as React version)
	const mockConversations = [
		{
			id: 1,
			userId: 'user-2',
			userName: 'Adebayo Ogundimu',
			avatarUrl: 'https://avatar.vercel.sh/adebayo.png?size=40',
			lastMessage: 'Thanks for the follow! Looking forward to connecting.',
			timestamp: '2024-01-15T14:30:00Z',
			unread: true
		},
		{
			id: 2,
			userId: 'user-3',
			userName: 'Fatima Abdullahi',
			avatarUrl: 'https://avatar.vercel.sh/fatima.png?size=40',
			lastMessage: 'What are your thoughts on the new policy?',
			timestamp: '2024-01-15T12:15:00Z',
			unread: false
		},
		{
			id: 3,
			userId: 'user-4',
			userName: 'Chinedu Okoro',
			avatarUrl: 'https://avatar.vercel.sh/chinedu.png?size=40',
			lastMessage: 'Great point in your recent post!',
			timestamp: '2024-01-14T16:45:00Z',
			unread: false
		}
	];

	const mockMessages = [
		{
			id: 1,
			conversationId: 1,
			senderId: 'user-2',
			text: 'Thanks for the follow! Looking forward to connecting.',
			timestamp: '2024-01-15T14:30:00Z'
		},
		{
			id: 2,
			conversationId: 1,
			senderId: user?.id,
			text: 'Likewise! Your insights on governance are really valuable.',
			timestamp: '2024-01-15T14:32:00Z'
		},
		{
			id: 3,
			conversationId: 2,
			senderId: 'user-3',
			text: 'What are your thoughts on the new policy?',
			timestamp: '2024-01-15T12:15:00Z'
		},
		{
			id: 4,
			conversationId: 2,
			senderId: user?.id,
			text: 'I think it has potential but needs more community input.',
			timestamp: '2024-01-15T12:18:00Z'
		}
	];

	// Derived values
	let selectedConversation = $derived(
		mockConversations.find(c => c.id === selectedConversationId)
	);

	let currentMessages = $derived(
		mockMessages.filter(m => m.conversationId === selectedConversationId)
	);

	// Functions
	function handleSendMessage() {
		if (!messageInput.trim() || !selectedConversationId) return;
		
		// Mock sending message
		console.log('Sending message:', messageInput);
		messageInput = '';
	}

	// Initialize first conversation
	onMount(() => {
		const targetUserId = $page.url.searchParams.get('to');
		if (targetUserId) {
			const existingConvo = mockConversations.find(c => c.userId === targetUserId);
			if (existingConvo) {
				selectedConversationId = existingConvo.id;
			} else if (mockConversations.length > 0) {
				selectedConversationId = mockConversations[0].id;
			}
		} else if (mockConversations.length > 0) {
			selectedConversationId = mockConversations[0].id;
		}
	});
</script>

<div 
	class="container mx-auto py-6 px-2 md:px-4 h-[calc(100vh-150px)] md:h-[calc(100vh-120px)]"
	in:fly={{ opacity: 0, duration: 300 }}
>
	<Card class="modern-card h-full flex flex-col md:flex-row overflow-hidden">
		<!-- Conversations Sidebar -->
		<div class="w-full md:w-1/3 lg:w-1/4 border-b md:border-b-0 md:border-r border-border flex flex-col">
			<CardHeader className="p-3 md:p-4 border-b border-border">
				<CardTitle class="text-lg md:text-xl text-primary flex items-center">
					<MessageCircle size={20} class="mr-2"/> Messages
				</CardTitle>
				<div class="relative mt-2">
					<Input 
						placeholder="Search conversations..." 
						className="modern-input pr-10 text-xs" 
						bind:value={searchTerm}
					/>
					<Search size={16} class="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
				</div>
			</CardHeader>
			<CardContent className="p-0 flex-grow overflow-y-auto">
				{#each mockConversations.sort((a,b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()) as convo (convo.id)}
					<div
						class="flex items-center space-x-3 p-3 cursor-pointer hover:bg-secondary/50 transition-colors {selectedConversationId === convo.id ? 'bg-secondary/70' : ''}"
						on:click={() => selectedConversationId = convo.id}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && (selectedConversationId = convo.id)}
					>
						<Avatar className="h-10 w-10" src={convo.avatarUrl} alt={convo.userName} fallback={convo.userName.substring(0,1)} />
						<div class="flex-grow overflow-hidden">
							<div class="flex justify-between items-start">
								<p class="font-medium text-sm truncate">{convo.userName}</p>
								{#if convo.unread}
									<span class="w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-1"></span>
								{/if}
							</div>
							<p class="text-xs text-muted-foreground truncate">{convo.lastMessage}</p>
							<p class="text-xs text-muted-foreground">{new Date(convo.timestamp).toLocaleDateString()}</p>
						</div>
					</div>
				{/each}
			</CardContent>
		</div>

		<!-- Chat Area -->
		<div class="w-full md:w-2/3 lg:w-3/4 flex flex-col bg-background">
			{#if selectedConversation}
				<!-- Chat Header -->
				<CardHeader className="p-3 md:p-4 border-b border-border flex flex-row items-center space-x-3">
					<Avatar className="h-9 w-9" src={selectedConversation.avatarUrl} alt={selectedConversation.userName} fallback={selectedConversation.userName.substring(0,1)} />
					<div>
						<CardTitle class="text-base md:text-lg text-foreground">{selectedConversation.userName}</CardTitle>
						<p class="text-xs text-muted-foreground">Online</p>
					</div>
				</CardHeader>

				<!-- Messages -->
				<CardContent className="flex-grow p-3 md:p-4 space-y-3 overflow-y-auto">
					{#each currentMessages as msg (msg.id)}
						<div class="flex {msg.senderId === user?.id ? 'justify-end' : 'justify-start'}">
							<div class="max-w-[70%] p-2 md:p-3 rounded-xl text-sm {msg.senderId === user?.id ? 'bg-primary text-primary-foreground rounded-br-none' : 'bg-secondary text-secondary-foreground rounded-bl-none'}">
								{msg.text}
								<p class="text-xs mt-1 {msg.senderId === user?.id ? 'text-primary-foreground/70 text-right' : 'text-muted-foreground/70 text-left'}">
									{new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
								</p>
							</div>
						</div>
					{/each}
					{#if currentMessages.length === 0}
						<p class="text-center text-muted-foreground py-10">No messages yet. Start the conversation!</p>
					{/if}
				</CardContent>

				<!-- Message Input -->
				<div class="p-3 md:p-4 border-t border-border bg-secondary/30">
					<div class="flex items-center space-x-2">
						<Input 
							placeholder="Type your message..." 
							className="modern-input flex-grow bg-background" 
							bind:value={messageInput}
							on:keydown={(e) => e.key === 'Enter' && handleSendMessage()}
						/>
						<Button on:click={handleSendMessage} className="btn-primary" disabled={!messageInput.trim()}>
							<Send size={18} />
						</Button>
					</div>
				</div>
			{:else}
				<!-- No Conversation Selected -->
				<div class="flex-grow flex flex-col items-center justify-center text-center p-8">
					<MessageCircle size={48} class="text-muted-foreground mb-4" />
					<h2 class="text-xl font-semibold text-foreground">Select a conversation</h2>
					<p class="text-muted-foreground">Choose a conversation from the sidebar to start messaging.</p>
				</div>
			{/if}
		</div>
	</Card>
</div>
