<!-- Main App Layout -->
<!-- Layout for authenticated app pages -->

<script lang="ts">
	import { leadersStore } from '$lib/stores/leaders.js';
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';
	import '$lib/styles/app.css';
	import { onMount } from 'svelte';
	import type { LayoutData } from './$types';

	// Get server-provided data
	let { data }: { data: LayoutData } = $props();

	// Initialize other stores (auth is already initialized from server)
	onMount(() => {
		leadersStore.initialize();
	});
</script>

<!-- Auth state is already available from server, no need to wait -->
<MainLayout>
	<slot />
</MainLayout>
