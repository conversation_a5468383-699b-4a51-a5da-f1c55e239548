<!-- Settings Page - Using real authentication system -->
<script lang="ts">
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Label from '$lib/components/ui/Label.svelte';
	import Switch from '$lib/components/ui/Switch.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardDescription from '$lib/components/ui/CardDescription.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardFooter from '$lib/components/ui/CardFooter.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import { User, <PERSON>, Shield, LogOut, <PERSON>, <PERSON>O<PERSON>, Camera } from 'lucide-svelte';

	// Get server-provided data
	let { data }: { data: PageData } = $props();

	// User is guaranteed to exist due to server-side protection
	let user = data.user;
	let loading = $state(false);

	let profileData = $state({
		name: '',
		email: '',
		state: '',
		lga: '',
		party: ''
	});

	let passwordData = $state({
		currentPassword: '',
		newPassword: '',
		confirmNewPassword: ''
	});

	let showCurrentPassword = $state(false);
	let showNewPassword = $state(false);
	let showConfirmNewPassword = $state(false);

	let notifications = $state({
		emailMentions: true,
		emailPollUpdates: false,
		appNewFollowers: true
	});

	let avatarPreview = $state(null);
	let avatarFile = $state(null);
	let avatarInputRef: HTMLInputElement;

	// Initialize profile data from server-provided user data
	onMount(() => {
		profileData = {
			name: user.name || '',
			email: user.email || '',
			state: user.state || '',
			lga: user.lga || '',
			party: user.party || ''
		};
		avatarPreview = user.avatarUrl || null;
	});

	// Event handlers
	function handleProfileChange(field: string, value: string) {
		profileData = { ...profileData, [field]: value };
	}

	function handlePasswordChange(field: string, value: string) {
		passwordData = { ...passwordData, [field]: value };
	}

	function handleNotificationChange(key: string) {
		notifications = { ...notifications, [key]: !notifications[key] };
		alert("Notification preference saved (demo).");
	}

	async function handleProfileSave() {
		if (!user) return;

		try {
			const response = await fetch('/api/auth/profile', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(profileData)
			});

			if (response.ok) {
				const data = await response.json();
				if (data.success) {
					user = data.user;
					alert("Profile updated successfully!");
				} else {
					alert("Error updating profile: " + data.error);
				}
			} else {
				alert("Error updating profile. Please try again.");
			}
		} catch (error) {
			console.error('Error updating profile:', error);
			alert("Error updating profile. Please try again.");
		}
	}

	function handleChangePassword() {
		if (passwordData.newPassword !== passwordData.confirmNewPassword) {
			alert("New passwords do not match.");
			return;
		}
		if (passwordData.newPassword.length < 6) {
			alert("New password must be at least 6 characters.");
			return;
		}
		// In a real app, this would call an API to change the password
		alert("Password changed successfully (demo).");
		passwordData = { currentPassword: '', newPassword: '', confirmNewPassword: '' };
	}

	async function handleLogout() {
		try {
			const response = await fetch('/api/auth/logout', {
				method: 'POST'
			});

			if (response.ok) {
				goto('/login');
				alert("Logged out successfully.");
			} else {
				alert("Error logging out. Please try again.");
			}
		} catch (error) {
			console.error('Error logging out:', error);
			alert("Error logging out. Please try again.");
		}
	}

	function handleAvatarChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const file = target.files?.[0];
		if (file) {
			if (file.size > 2 * 1024 * 1024) { // 2MB limit
				toastStore.add({
					title: "File too large",
					description: "Avatar image must be less than 2MB.",
					type: "error"
				});
				return;
			}
			avatarFile = file;
			avatarPreview = URL.createObjectURL(file);
		}
	}

	async function handleSaveAvatar() {
		if (avatarFile && user) {
			try {
				// In a real app, you would upload the file to a storage service first
				// For now, we'll just use the preview URL
				const response = await fetch('/api/auth/profile', {
					method: 'PATCH',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ avatarUrl: avatarPreview })
				});

				if (response.ok) {
					const data = await response.json();
					if (data.success) {
						user = data.user;
						alert("Avatar updated successfully!");
						avatarFile = null;
					} else {
						alert("Error updating avatar: " + data.error);
					}
				} else {
					alert("Error updating avatar. Please try again.");
				}
			} catch (error) {
				console.error('Error updating avatar:', error);
				alert("Error updating avatar. Please try again.");
			}
		}
	}

	function triggerAvatarUpload() {
		avatarInputRef?.click();
	}
</script>

<svelte:head>
	<title>Settings - LeaderBox</title>
</svelte:head>

<!-- User is guaranteed to be authenticated due to server-side protection -->
<div class="container mx-auto py-8 px-4 space-y-8 max-w-3xl">
		<!-- Header -->
		<header class="mb-8">
			<h1 class="text-3xl md:text-4xl font-extrabold text-foreground">Settings</h1>
			<p class="text-lg text-muted-foreground mt-1">Manage your account preferences and details.</p>
		</header>

		<!-- Profile Information Card -->
		<Card class="modern-card">
			<CardHeader>
				<CardTitle class="text-xl text-primary flex items-center">
					<User class="mr-2" /> Profile Information
				</CardTitle>
				<CardDescription>Update your personal details and avatar.</CardDescription>
			</CardHeader>
			<CardContent className="p-6 pt-0 space-y-6">
				<!-- Avatar and Name Section -->
				<div class="flex items-center gap-6">
					<div class="relative">
						<Avatar
							className="h-24 w-24 border-4 border-secondary"
							src={avatarPreview || `https://avatar.vercel.sh/${user.name || user.email}.png?size=96`}
							alt={user.name}
							fallback={user.name ? user.name.substring(0, 2).toUpperCase() : user.email.substring(0, 2).toUpperCase()}
						/>
						<Button
							size="icon"
							className="absolute -bottom-1 -right-1 h-8 w-8 rounded-full btn-primary"
							on:click={triggerAvatarUpload}
						>
							<Camera size={16} />
						</Button>
						<input 
							type="file" 
							accept="image/*" 
							bind:this={avatarInputRef} 
							on:change={handleAvatarChange} 
							class="hidden" 
						/>
					</div>
					<div class="flex-grow">
						<Label for="name">Full Name</Label>
						<Input 
							id="name" 
							value={profileData.name} 
							on:input={(e) => handleProfileChange('name', e.target.value)} 
							class="modern-input mt-1" 
						/>
					</div>
				</div>

				{#if avatarFile}
					<div class="flex justify-end">
						<Button on:click={handleSaveAvatar} size="sm" className="btn-accent">
							Save Avatar
						</Button>
					</div>
				{/if}

				<!-- Profile Fields -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label for="email">Email Address</Label>
						<Input 
							id="email" 
							type="email" 
							value={profileData.email} 
							on:input={(e) => handleProfileChange('email', e.target.value)} 
							class="modern-input mt-1" 
						/>
					</div>
					<div>
						<Label for="state">State</Label>
						<Input 
							id="state" 
							value={profileData.state} 
							on:input={(e) => handleProfileChange('state', e.target.value)} 
							class="modern-input mt-1" 
						/>
					</div>
					<div>
						<Label for="lga">Local Government Area</Label>
						<Input 
							id="lga" 
							value={profileData.lga} 
							on:input={(e) => handleProfileChange('lga', e.target.value)} 
							class="modern-input mt-1" 
						/>
					</div>
					<div>
						<Label for="party">Political Party</Label>
						<Input 
							id="party" 
							value={profileData.party} 
							on:input={(e) => handleProfileChange('party', e.target.value)} 
							class="modern-input mt-1" 
						/>
					</div>
				</div>
			</CardContent>
			<CardFooter>
				<Button on:click={handleProfileSave} className="btn-primary">
					Save Profile
				</Button>
			</CardFooter>
		</Card>

		<!-- Password Change Card -->
		<Card class="modern-card">
			<CardHeader>
				<CardTitle class="text-xl text-primary flex items-center">
					<Shield class="mr-2" /> Change Password
				</CardTitle>
				<CardDescription>Update your account password.</CardDescription>
			</CardHeader>
			<CardContent className="p-6 pt-0 space-y-4">
				<div class="relative">
					<Label for="currentPassword">Current Password</Label>
					<Input 
						id="currentPassword" 
						type={showCurrentPassword ? "text" : "password"} 
						value={passwordData.currentPassword} 
						on:input={(e) => handlePasswordChange('currentPassword', e.target.value)} 
						class="modern-input mt-1 pr-10" 
					/>
					<Button 
						type="button" 
						variant="ghost" 
						size="icon" 
						class="absolute right-2 top-8 h-7 w-7 text-muted-foreground" 
						on:click={() => showCurrentPassword = !showCurrentPassword}
					>
						{#if showCurrentPassword}
							<EyeOff size={16} />
						{:else}
							<Eye size={16} />
						{/if}
					</Button>
				</div>
				<div class="relative">
					<Label for="newPassword">New Password</Label>
					<Input 
						id="newPassword" 
						type={showNewPassword ? "text" : "password"} 
						value={passwordData.newPassword} 
						on:input={(e) => handlePasswordChange('newPassword', e.target.value)} 
						class="modern-input mt-1 pr-10" 
					/>
					<Button 
						type="button" 
						variant="ghost" 
						size="icon" 
						class="absolute right-2 top-8 h-7 w-7 text-muted-foreground" 
						on:click={() => showNewPassword = !showNewPassword}
					>
						{#if showNewPassword}
							<EyeOff size={16} />
						{:else}
							<Eye size={16} />
						{/if}
					</Button>
				</div>
				<div class="relative">
					<Label for="confirmNewPassword">Confirm New Password</Label>
					<Input 
						id="confirmNewPassword" 
						type={showConfirmNewPassword ? "text" : "password"} 
						value={passwordData.confirmNewPassword} 
						on:input={(e) => handlePasswordChange('confirmNewPassword', e.target.value)} 
						class="modern-input mt-1 pr-10" 
					/>
					<Button 
						type="button" 
						variant="ghost" 
						size="icon" 
						class="absolute right-2 top-8 h-7 w-7 text-muted-foreground" 
						on:click={() => showConfirmNewPassword = !showConfirmNewPassword}
					>
						{#if showConfirmNewPassword}
							<EyeOff size={16} />
						{:else}
							<Eye size={16} />
						{/if}
					</Button>
				</div>
			</CardContent>
			<CardFooter>
				<Button on:click={handleChangePassword} className="btn-primary">
					Change Password
				</Button>
			</CardFooter>
		</Card>

		<!-- Notification Preferences Card -->
		<Card class="modern-card">
			<CardHeader>
				<CardTitle class="text-xl text-primary flex items-center">
					<Bell class="mr-2" /> Notification Preferences
				</CardTitle>
				<CardDescription>Manage how you receive notifications from LeaderBox.</CardDescription>
			</CardHeader>
			<CardContent className="p-6 pt-0 space-y-4">
				<div class="flex items-center justify-between p-3 bg-secondary/30 rounded-md">
					<Label for="emailMentions" class="text-sm font-medium">Email for @mentions</Label>
					<Switch 
						id="emailMentions" 
						bind:checked={notifications.emailMentions} 
						onCheckedChange={() => handleNotificationChange('emailMentions')} 
					/>
				</div>
				<div class="flex items-center justify-between p-3 bg-secondary/30 rounded-md">
					<Label for="emailPollUpdates" class="text-sm font-medium">Email for poll updates</Label>
					<Switch 
						id="emailPollUpdates" 
						bind:checked={notifications.emailPollUpdates} 
						onCheckedChange={() => handleNotificationChange('emailPollUpdates')} 
					/>
				</div>
				<div class="flex items-center justify-between p-3 bg-secondary/30 rounded-md">
					<Label for="appNewFollowers" class="text-sm font-medium">App notifications for new followers</Label>
					<Switch 
						id="appNewFollowers" 
						bind:checked={notifications.appNewFollowers} 
						onCheckedChange={() => handleNotificationChange('appNewFollowers')} 
					/>
				</div>
			</CardContent>
		</Card>

		<!-- Account Actions Card -->
		<Card class="modern-card">
			<CardHeader>
				<CardTitle class="text-xl text-destructive flex items-center">
					<LogOut class="mr-2"/> Account Actions
				</CardTitle>
				<CardDescription>Manage your account session.</CardDescription>
			</CardHeader>
			<CardContent className="p-6 pt-0">
				<Button variant="destructive" className="w-full sm:w-auto" on:click={handleLogout}>
					Log Out
				</Button>
				<p class="text-xs text-muted-foreground mt-2">This will end your current session on this device.</p>
			</CardContent>
		</Card>
	</div>

<style>
	:global(.modern-card) {
		@apply bg-card border border-border rounded-lg shadow-sm;
	}
	
	:global(.modern-input) {
		@apply border-border focus:border-primary focus:ring-primary;
	}
	
	:global(.btn-primary) {
		@apply bg-primary text-primary-foreground hover:bg-primary/90;
	}
	
	:global(.btn-accent) {
		@apply bg-accent text-accent-foreground hover:bg-accent/90;
	}
</style>
