<!-- Leader Profile Page - Matches React LeaderProfilePage.jsx exactly -->
<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { authStore } from '$lib/stores/auth.js';
	import { toast } from '$lib/stores/toast.js';
	import { fade } from 'svelte/transition';
	import Button from '$lib/components/ui/Button.svelte';

	// Layout
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';

	// Import leader profile components
	import LeaderProfileHeader from '$lib/components/leader-profile/LeaderProfileHeader.svelte';
	import LeaderMetricsCard from '$lib/components/leader-profile/LeaderMetricsCard.svelte';
	import LeaderEngagementCard from '$lib/components/leader-profile/LeaderEngagementCard.svelte';
	import LeaderBioCard from '$lib/components/leader-profile/LeaderBioCard.svelte';
	import LeaderActivityTabs from '$lib/components/leader-profile/LeaderActivityTabs.svelte';
	import RatingDialog from '$lib/components/leader-profile/RatingDialog.svelte';
	import DirectMessageDialog from '$lib/components/leader-profile/DirectMessageDialog.svelte';
	import SuggestEditDialog from '$lib/components/leader-profile/SuggestEditDialog.svelte';

	// Props from server load
	export let data;

	// Reactive variables
	$: user = $authStore.user;
	$: leaderId = $page.params.id;

	// State
	$: leader = data.leader; // Use server-loaded data
	let loading = false; // No loading needed since data comes from server
	let userRatingDetails = null;
	let canRateAgain = true;
	let commentsList = [];

	// Modal states
	let showRatingModal = false;
	let ratingModalCurrentRating = 0;
	let ratingModalCommentText = '';
	let ratingModalHoverRating = 0;
	let showDirectMessageModal = false;
	let showSuggestEditModal = false;

	// Functions
	async function loadUserSpecificData() {
		if (!browser || !user || !leaderId) return;

		try {
			await loadUserRatingDetails();
			await loadComments();
		} catch (error) {
			console.error('Error loading user-specific data:', error);
		}
	}

	async function loadUserRatingDetails() {
		if (!user || !leaderId) return;

		try {
			const response = await fetch(`/api/leaders/${leaderId}/user-rating`);
			if (response.ok) {
				const data = await response.json();
				userRatingDetails = data.rating;

				// Check if user can rate again (24 hour cooldown)
				if (userRatingDetails) {
					const twentyFourHours = 24 * 60 * 60 * 1000;
					const lastRated = new Date(userRatingDetails.createdAt).getTime();
					canRateAgain = Date.now() - lastRated >= twentyFourHours;
				}
			}
		} catch (error) {
			console.error('Error loading user rating:', error);
		}
	}

	async function loadComments() {
		if (!leaderId) return;

		try {
			const response = await fetch(`/api/leaders/${leaderId}/comments`);
			if (response.ok) {
				const data = await response.json();
				commentsList = data.comments || [];
			}
		} catch (error) {
			console.error('Error loading comments:', error);
		}
	}

	function handleFollowToggle() {
		if (!user) {
			if (browser) {
				toast.info('Please log in to follow leaders');
				goto('/login');
			}
			return;
		}

		// TODO: Implement follow/unfollow API
		console.log('Follow toggle for leader:', leaderId);
		if (browser) {
			toast.info('Follow functionality coming soon!');
		}
	}

	function openRatingDialog() {
		if (!user) {
			if (browser) {
				toast.info('Please log in to rate leaders');
				goto('/login');
			}
			return;
		}

		if (!canRateAgain && userRatingDetails) {
			if (browser) {
				toast.warning('You can update your rating after 24 hours');
			}
			return;
		}

		ratingModalCurrentRating = userRatingDetails?.rating || 0;
		ratingModalCommentText = userRatingDetails?.comment || '';
		showRatingModal = true;
	}

	async function handleSubmitRating() {
		if (!browser) return;

		if (!leader || ratingModalCurrentRating === 0) {
			toast.warning('Please select a star rating');
			return;
		}

		try {
			const response = await fetch(`/api/leaders/${leaderId}/rate`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					rating: ratingModalCurrentRating,
					comment: ratingModalCommentText
				})
			});

			if (!response.ok) {
				throw new Error('Failed to submit rating');
			}

			toast.success(`You rated ${leader.name} ${ratingModalCurrentRating} stars!`);
			showRatingModal = false;

			// Reload user rating data
			await loadUserRatingDetails();
		} catch (error) {
			console.error('Error submitting rating:', error);
			toast.error('Failed to submit rating. Please try again.');
		}
	}

	function openDirectMessageDialog() {
		if (!user) {
			if (browser) {
				toast.info('Please log in to send messages');
				goto('/login');
			}
			return;
		}
		showDirectMessageModal = true;
	}

	function openSuggestEditDialog() {
		if (!user) {
			if (browser) {
				toast.info('Please log in to suggest edits');
				goto('/login');
			}
			return;
		}
		showSuggestEditModal = true;
	}

	function handleSubmitSuggestion(suggestionData) {
		if (!leader || !browser) return;

		console.log('Suggestion for leader:', leader.name, suggestionData);
		toast.success('Thank you for helping keep LeaderBox up-to-date. Your suggestion will be reviewed.');
		showSuggestEditModal = false;
	}

	// Load user-specific data when component mounts or user changes
	onMount(() => {
		if (browser && user && leaderId) {
			loadUserSpecificData();
		}
	});

	// Load user-specific data when user logs in/out
	$: if (browser && user && leaderId) {
		loadUserSpecificData();
	}
</script>

<svelte:head>
	<title>{leader ? `${leader.name} - LeaderBox` : 'Loading... - LeaderBox'}</title>
	<meta name="description" content={leader ? `Learn about ${leader.name}, ${leader.position} from ${leader.state} State. View ratings, bio, and engage with this political leader.` : 'Loading leader profile...'} />
</svelte:head>

<MainLayout>
	{#if loading}
		<div class="flex justify-center items-center min-h-[calc(100vh-200px)]">
			<p>Loading leader profile...</p>
		</div>
	{:else if leader}
		<div class="space-y-6 md:space-y-8" in:fade={{ duration: 500 }}>
			<LeaderProfileHeader {leader} />

			<div class="flex justify-end -mt-4 mr-2 md:mr-0">
				<Button variant="outline" size="sm" on:click={openSuggestEditDialog} className="text-xs">
					<svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
					</svg>
					Suggest Edit/Update
				</Button>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mt-2">
				<LeaderMetricsCard {leader} />
				<LeaderEngagementCard
					leaderName={leader.name}
					isFollowing={leader.isFollowed}
					onFollowToggle={handleFollowToggle}
					onRate={openRatingDialog}
					{canRateAgain}
					{userRatingDetails}
					onSendMessage={openDirectMessageDialog}
				/>
			</div>

			<LeaderBioCard {leader} />
			<LeaderActivityTabs {leader} {commentsList} {user} />

			<!-- Modals -->
			<RatingDialog
				isOpen={showRatingModal}
				onClose={() => showRatingModal = false}
				leaderName={leader.name}
				currentRating={ratingModalCurrentRating}
				setCurrentRating={(rating) => ratingModalCurrentRating = rating}
				commentText={ratingModalCommentText}
				setCommentText={(text) => ratingModalCommentText = text}
				hoverRating={ratingModalHoverRating}
				setHoverRating={(rating) => ratingModalHoverRating = rating}
				onSubmit={handleSubmitRating}
			/>

			<DirectMessageDialog
				isOpen={showDirectMessageModal}
				onClose={() => showDirectMessageModal = false}
				leaderName={leader.name}
			/>

			<SuggestEditDialog
				isOpen={showSuggestEditModal}
				onClose={() => showSuggestEditModal = false}
				{leader}
				onSubmit={handleSubmitSuggestion}
			/>
		</div>
	{:else}
		<div class="flex justify-center items-center min-h-[calc(100vh-200px)]">
			<p>Leader not found.</p>
		</div>
	{/if}
</MainLayout>
