// Server-side data loading for leader profile page
import { error } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma.js';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
	try {
		const leader = await prisma.leader.findUnique({
			where: { id: params.id },
			include: {
				_count: {
					select: {
						followers: true,
						ratings: true,
						comments: true
					}
				}
			}
		});

		if (!leader) {
			throw error(404, 'Leader not found');
		}

		// Format response to match React app structure exactly
		const formattedLeader = {
			id: leader.id,
			name: leader.name,
			position: leader.position,
			party: leader.party,
			state: leader.state,
			lga: leader.lga,
			bio: leader.bio,
			detailedBio: leader.detailedBio,
			avatarUrl: leader.avatarUrl,
			coverImageUrl: leader.coverImageUrl,
			followers: leader.followersCount,              // React app uses 'followers'
			currentRating: Number(leader.ratingAverage),   // React app uses 'currentRating'
			totalRatings: leader.ratingCount,              // React app uses 'totalRatings'
			publicSentiment: leader.publicSentiment || 'Neutral',
			youtubeVideoUrl: leader.youtubeVideoUrl,
			education: leader.education,
			background: leader.background,
			popularityScore: leader.popularityScore || 75,
			isVerified: leader.isVerified || false,
			isFollowed: false,                             // Will be computed client-side based on user
			commentsCount: leader._count.comments,
			// Additional fields for profile page
			achievements: [], // TODO: Add achievements table/field
			socialMedia: {}, // TODO: Add social media fields
			recentActivity: [], // TODO: Add activity tracking
			polls: [] // TODO: Add polls relation
		};

		return {
			leader: formattedLeader
		};

	} catch (err) {
		console.error('Error loading leader:', err);
		if (err.status === 404) {
			throw err;
		}
		throw error(500, 'Internal server error');
	}
};
