<!-- Contact Page - New page for LeaderBox Africa -->
<script lang="ts">
	import { fly } from 'svelte/transition';
	import type { PageData } from './$types';

	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Textarea from '$lib/components/ui/Textarea.svelte';
	import { Mail, Phone, MapPin, Send, MessageSquare, Users, Shield } from 'lucide-svelte';
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';

	// Get server-provided data
	let { data }: { data: PageData } = $props();
	let user = data.user;

	// State
	let formData = $state({
		name: '',
		email: '',
		subject: '',
		message: '',
		category: 'general'
	});
	let isSubmitting = $state(false);
	let isSubmitted = $state(false);

	// Functions
	async function handleSubmit() {
		if (!formData.name || !formData.email || !formData.message) {
			return;
		}

		isSubmitting = true;

		try {
			// Mock form submission
			await new Promise(resolve => setTimeout(resolve, 1000));

			isSubmitted = true;

			// Reset form
			formData = {
				name: '',
				email: '',
				subject: '',
				message: '',
				category: 'general'
			};
		} catch (error) {
			console.error('Form submission failed:', error);
			// In a real app, show error message to user
		} finally {
			isSubmitting = false;
		}
	}

	// Pre-fill user data if logged in
	$effect(() => {
		if (user) {
			formData.name = user.name || '';
			formData.email = user.email || '';
		}
	});

	const contactInfo = [
		{
			icon: Mail,
			title: 'Email Us',
			content: '<EMAIL>',
			description: 'Send us an email and we\'ll respond within 24 hours'
		},
		{
			icon: Phone,
			title: 'Call Us',
			content: '+234 (0) ************',
			description: 'Monday to Friday, 9AM to 6PM WAT'
		},
		{
			icon: MapPin,
			title: 'Visit Us',
			content: 'Lagos, Nigeria',
			description: 'Our headquarters in the heart of Lagos'
		}
	];

	const supportCategories = [
		{ value: 'general', label: 'General Inquiry' },
		{ value: 'technical', label: 'Technical Support' },
		{ value: 'account', label: 'Account Issues' },
		{ value: 'feedback', label: 'Feedback & Suggestions' },
		{ value: 'partnership', label: 'Partnership Opportunities' },
		{ value: 'media', label: 'Media & Press' }
	];
</script>

<svelte:head>
	<title>Contact Us - LeaderBox Africa</title>
	<meta name="description" content="Get in touch with LeaderBox Africa. We're here to help with any questions, feedback, or support you need." />
</svelte:head>

<MainLayout>
	<div
		class="container mx-auto py-8 px-4"
		in:fly={{ opacity: 0, duration: 500 }}
	>
	<!-- Header -->
	<header class="text-center mb-12">
		<h1 class="text-4xl font-extrabold gradient-text from-primary to-accent mb-4">Contact Us</h1>
		<p class="text-lg text-muted-foreground max-w-2xl mx-auto">
			Have questions, feedback, or need support? We're here to help. Reach out to us and we'll get back to you as soon as possible.
		</p>
	</header>

	<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
		<!-- Contact Information -->
		<div class="lg:col-span-1 space-y-6">
			<Card class="modern-card">
				<CardHeader>
					<CardTitle class="text-xl text-primary flex items-center">
						<MessageSquare size={24} class="mr-2" />
						Get in Touch
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-6 p-6 pt-0">
					{#each contactInfo as info}
						<div class="flex items-start gap-3">
							<div class="p-2 bg-primary/10 rounded-lg">
								<svelte:component this={info.icon} size={20} class="text-primary" />
							</div>
							<div>
								<h3 class="font-semibold text-foreground">{info.title}</h3>
								<p class="text-primary font-medium">{info.content}</p>
								<p class="text-sm text-muted-foreground">{info.description}</p>
							</div>
						</div>
					{/each}
				</CardContent>
			</Card>

			<!-- Quick Links -->
			<Card class="modern-card">
				<CardHeader>
					<CardTitle class="text-lg text-primary flex items-center">
						<Users size={20} class="mr-2" />
						Quick Links
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3 p-6 pt-0">
					<a href="/about" class="block text-sm text-muted-foreground hover:text-primary transition-colors">
						About LeaderBox Africa
					</a>
					<a href="/privacy" class="block text-sm text-muted-foreground hover:text-primary transition-colors">
						Privacy Policy
					</a>
					<a href="/terms" class="block text-sm text-muted-foreground hover:text-primary transition-colors">
						Terms of Service
					</a>
					<a href="/help" class="block text-sm text-muted-foreground hover:text-primary transition-colors">
						Help Center
					</a>
				</CardContent>
			</Card>
		</div>

		<!-- Contact Form -->
		<div class="lg:col-span-2">
			<Card class="modern-card">
				<CardHeader>
					<CardTitle class="text-xl text-primary">Send us a Message</CardTitle>
					<p class="text-muted-foreground">Fill out the form below and we'll get back to you soon.</p>
				</CardHeader>
				<CardContent className="p-6 pt-0">
					{#if isSubmitted}
						<div class="text-center py-8">
							<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
								<Send size={24} class="text-green-600" />
							</div>
							<h3 class="text-lg font-semibold text-foreground mb-2">Message Sent!</h3>
							<p class="text-muted-foreground">Thank you for contacting us. We'll respond within 24 hours.</p>
							<Button
								onclick={() => isSubmitted = false}
								className="mt-4 btn-outline-primary"
							>
								Send Another Message
							</Button>
						</div>
					{:else}
						<form on:submit|preventDefault={handleSubmit} class="space-y-6">
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label for="name" class="block text-sm font-medium text-foreground mb-2">
										Full Name *
									</label>
									<Input
										id="name"
										bind:value={formData.name}
										placeholder="Your full name"
										required
										className="modern-input"
									/>
								</div>
								<div>
									<label for="email" class="block text-sm font-medium text-foreground mb-2">
										Email Address *
									</label>
									<Input
										id="email"
										type="email"
										bind:value={formData.email}
										placeholder="<EMAIL>"
										required
										className="modern-input"
									/>
								</div>
							</div>

							<div>
								<label for="category" class="block text-sm font-medium text-foreground mb-2">
									Category
								</label>
								<select 
									id="category"
									bind:value={formData.category}
									class="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
								>
									{#each supportCategories as category}
										<option value={category.value}>{category.label}</option>
									{/each}
								</select>
							</div>

							<div>
								<label for="subject" class="block text-sm font-medium text-foreground mb-2">
									Subject
								</label>
								<Input
									id="subject"
									bind:value={formData.subject}
									placeholder="Brief description of your inquiry"
									className="modern-input"
								/>
							</div>

							<div>
								<label for="message" class="block text-sm font-medium text-foreground mb-2">
									Message *
								</label>
								<Textarea
									id="message"
									bind:value={formData.message}
									placeholder="Please provide details about your inquiry..."
									rows={6}
									required
									className="modern-input resize-none"
								/>
							</div>

							<Button 
								type="submit" 
								disabled={isSubmitting || !formData.name || !formData.email || !formData.message}
								className="w-full btn-primary"
							>
								{#if isSubmitting}
									<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
									Sending...
								{:else}
									<Send size={18} class="mr-2" />
									Send Message
								{/if}
							</Button>
						</form>
					{/if}
				</CardContent>
			</Card>
		</div>
	</div>
</MainLayout>

	<!-- Additional Information -->
	<div class="mt-12 text-center">
		<Card class="modern-card bg-secondary/30 max-w-4xl mx-auto">
			<CardContent className="p-8">
				<div class="flex items-center justify-center mb-4">
					<Shield size={32} class="text-primary" />
				</div>
				<h3 class="text-xl font-semibold text-foreground mb-2">Your Privacy Matters</h3>
				<p class="text-muted-foreground">
					We respect your privacy and will never share your personal information with third parties. 
					All communications are secure and confidential.
				</p>
			</CardContent>
		</Card>
	</div>

