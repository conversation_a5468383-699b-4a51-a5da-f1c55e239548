<!-- Groups Page - Svelte conversion of React GroupsPage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { fly } from 'svelte/transition';
	import { authStore, user } from '$lib/stores/auth.js';

	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardFooter from '$lib/components/ui/CardFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';
	import { Users, Lock, Globe, PlusCircle, Search, TrendingUp, Sparkles, UserCircle } from 'lucide-svelte';
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';

	// State
	let searchTerm = $state('');
	let activeTab = $state('trending');
	let showCreateGroupDialog = $state(false);

	// Mock groups data (same structure as React version)
	const mockGroups = [
		{
			id: 1,
			name: 'Youth in Politics',
			description: 'A community for young Nigerians interested in political participation and civic engagement.',
			coverImage: 'https://source.unsplash.com/random/400x200?community,nigeria,1',
			members: 1247,
			isPublic: true,
			category: 'Politics',
			createdAt: '2024-01-01T00:00:00Z',
			status: 'visible'
		},
		{
			id: 2,
			name: 'Healthcare Advocacy',
			description: 'Advocating for better healthcare policies and infrastructure across Nigeria.',
			coverImage: 'https://source.unsplash.com/random/400x200?healthcare,nigeria,2',
			members: 892,
			isPublic: true,
			category: 'Healthcare',
			createdAt: '2024-01-05T00:00:00Z',
			status: 'visible'
		},
		{
			id: 3,
			name: 'Education Reform',
			description: 'Discussing and promoting educational reforms for a better Nigeria.',
			coverImage: 'https://source.unsplash.com/random/400x200?education,nigeria,3',
			members: 634,
			isPublic: true,
			category: 'Education',
			createdAt: '2024-01-10T00:00:00Z',
			status: 'visible'
		},
		{
			id: 4,
			name: 'Lagos State Development',
			description: 'Local community focused on Lagos State development and governance.',
			coverImage: 'https://source.unsplash.com/random/400x200?lagos,development,4',
			members: 423,
			isPublic: true,
			category: 'Local Government',
			createdAt: '2024-01-12T00:00:00Z',
			status: 'visible'
		}
	];

	// Derived values
	let visibleGroups = $derived(
		mockGroups.filter(group => group.status === 'visible' && group.isPublic)
	);

	let filteredGroups = $derived(() => {
		let sourceGroups = [];

		if (activeTab === 'trending') {
			sourceGroups = [...visibleGroups].sort((a, b) => b.members - a.members);
		} else if (activeTab === 'new') {
			sourceGroups = [...visibleGroups].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
		} else if (activeTab === 'my-groups' && $user) {
			// Mock user groups - in real app this would come from API
			sourceGroups = visibleGroups.slice(0, 2); // Mock: user is member of first 2 groups
		} else {
			sourceGroups = visibleGroups;
		}

		if (searchTerm) {
			return sourceGroups.filter(group =>
				group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				group.description.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}
		return sourceGroups;
	});

	// Functions
	function handleCreateGroupClick() {
		if (!$user) {
			goto('/login');
		} else {
			showCreateGroupDialog = true;
		}
	}

	function handleGroupClick(groupId: number) {
		goto(`/group/${groupId}`);
	}
</script>

<MainLayout>
	<div
		class="container mx-auto py-8 px-4"
		in:fly={{ opacity: 0, duration: 500 }}
	>
	<!-- Header -->
	<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
		<div>
			<h1 class="text-4xl font-extrabold text-foreground">Discover Groups</h1>
			<p class="text-muted-foreground mt-1">Join communities to discuss topics that matter to you.</p>
		</div>
		<Button on:click={handleCreateGroupClick} className="btn-primary self-start md:self-center">
			<PlusCircle size={18} class="mr-2" /> Create a Group
		</Button>
	</div>

	<!-- Search -->
	<div class="relative mb-6">
		<Input
			type="search"
			placeholder="Search groups by name or description..."
			bind:value={searchTerm}
			className="modern-input pl-4 pr-10 text-base h-11"
		/>
		<Search class="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
	</div>

	<!-- Tabs -->
	<div class="flex justify-center mb-8">
		<div class="flex bg-secondary/50 rounded-lg p-1 gap-1">
			<button 
				class="px-4 py-2 text-sm font-medium rounded-md transition-all flex items-center gap-2 {activeTab === 'trending' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground'}"
				on:click={() => activeTab = 'trending'}
			>
				<TrendingUp size={18} /> Trending
			</button>
			<button 
				class="px-4 py-2 text-sm font-medium rounded-md transition-all flex items-center gap-2 {activeTab === 'new' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground'}"
				on:click={() => activeTab = 'new'}
			>
				<Sparkles size={18} /> New
			</button>
			<button 
				class="px-4 py-2 text-sm font-medium rounded-md transition-all flex items-center gap-2 {activeTab === 'my-groups' ? 'bg-background text-primary shadow-md' : 'text-muted-foreground hover:text-foreground'}"
				on:click={() => activeTab = 'my-groups'}
			>
				<UserCircle size={18} /> My Groups
			</button>
		</div>
	</div>

	<!-- Groups Grid -->
	{#if filteredGroups().length > 0}
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
			{#each filteredGroups() as group (group.id)}
				<Card class="modern-card h-full flex flex-col hover:shadow-md transition-shadow">
					<CardHeader class="p-0">
						<button on:click={() => handleGroupClick(group.id)} class="w-full">
							<img
								alt="{group.name} cover"
								class="w-full h-32 object-cover rounded-t-lg"
								src={group.coverImage}
							/>
						</button>
					</CardHeader>
					<CardContent className="flex-grow p-4">
						<CardTitle class="text-lg text-primary hover:underline mb-1">
							<button on:click={() => handleGroupClick(group.id)} class="text-left">
								{group.name}
							</button>
						</CardTitle>
						<p class="text-sm text-muted-foreground line-clamp-2 mb-3">{group.description}</p>
						<div class="flex items-center justify-between">
							<div class="flex items-center gap-1 text-sm text-muted-foreground">
								<Users size={16} />
								{group.members} members
							</div>
							<Badge variant="secondary" class="text-xs">
								{group.category}
							</Badge>
						</div>
					</CardContent>
					<CardFooter className="p-4 pt-0">
						<Button 
							className="w-full btn-outline-primary"
							on:click={() => handleGroupClick(group.id)}
						>
							View Group
						</Button>
					</CardFooter>
				</Card>
			{/each}
		</div>
	{:else}
		<div class="text-center py-16 modern-card bg-secondary/30">
			<Users size={48} class="mx-auto text-muted-foreground/50 mb-4" />
			<p class="text-xl font-semibold">No groups found</p>
			<p class="text-muted-foreground mt-2">
				{#if searchTerm}
					No groups match "{searchTerm}".
				{:else}
					There are no groups in this category yet.
				{/if}
			</p>
		</div>
	{/if}
	</div>

	<!-- Create Group Modal (placeholder) -->
	{#if showCreateGroupDialog}
		<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50" on:click={() => showCreateGroupDialog = false}>
			<div class="bg-background p-6 rounded-lg max-w-md w-full mx-4" on:click|stopPropagation>
				<h3 class="text-lg font-semibold mb-4">Create Group</h3>
				<p class="text-muted-foreground mb-4">Group creation functionality coming soon!</p>
				<Button on:click={() => showCreateGroupDialog = false} className="w-full">Close</Button>
			</div>
		</div>
	{/if}
</MainLayout>
