<!-- Petitions Page - Svelte conversion of React PetitionsPage.jsx -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { fly } from 'svelte/transition';
	import type { PageData } from './$types';

	// UI Components
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardFooter from '$lib/components/ui/CardFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Progress from '$lib/components/ui/Progress.svelte';
	import { FileText, Users, Search, PlusCircle } from 'lucide-svelte';
	import MainLayout from '$lib/components/layouts/MainLayout.svelte';

	// Get server-provided data
	let { data }: { data: PageData } = $props();
	let user = data.user;

	// State
	let searchTerm = $state('');
	let showCreateModal = $state(false);
	let visiblePetitionsCount = $state(20);
	let signingPetitionId = $state<number | null>(null);
	let isCreatingPetition = $state(false);

	// Mock petitions data (same structure as React version)
	const mockPetitions = [
		{
			id: 1,
			title: 'Improve Healthcare Infrastructure in Rural Areas',
			description: 'We petition for better healthcare facilities and medical equipment in rural communities across Nigeria.',
			target: 1000,
			signatures: 756,
			status: 'active',
			createdBy: 'Adebayo Ogundimu',
			timestamp: '2024-01-10T10:00:00Z',
			taggedLeader: 'Minister of Health'
		},
		{
			id: 2,
			title: 'Increase Education Funding',
			description: 'Petition to allocate more budget to education sector for better schools and teacher training.',
			target: 2000,
			signatures: 1234,
			status: 'active',
			createdBy: 'Fatima Abdullahi',
			timestamp: '2024-01-08T14:30:00Z',
			taggedLeader: 'Minister of Education'
		},
		{
			id: 3,
			title: 'Fix Road Infrastructure',
			description: 'Urgent need to repair and maintain major highways connecting states.',
			target: 1500,
			signatures: 892,
			status: 'active',
			createdBy: 'Chinedu Okoro',
			timestamp: '2024-01-05T09:15:00Z',
			taggedLeader: 'Minister of Works'
		}
	];

	// Derived values
	let filteredPetitions = $derived(
		mockPetitions
			.filter(p => p.status === 'active')
			.filter(p => 
				p.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
				p.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
				(p.taggedLeader && p.taggedLeader.toLowerCase().includes(searchTerm.toLowerCase()))
			)
			.sort((a,b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
	);

	let petitionsToShow = $derived(filteredPetitions.slice(0, visiblePetitionsCount));

	// Functions
	async function handleSignPetition(petitionId: number) {
		if (!user) {
			goto('/login');
			return;
		}

		signingPetitionId = petitionId;

		try {
			// Mock signing logic with delay
			await new Promise(resolve => setTimeout(resolve, 1000));
			console.log('Signed petition:', petitionId);
			// In real app: update petition signature count
		} catch (error) {
			console.error('Failed to sign petition:', error);
			// In real app: show error message
		} finally {
			signingPetitionId = null;
		}
	}

	function handleCreatePetition() {
		if (!user) {
			goto('/login');
			return;
		}
		showCreateModal = true;
	}

	function handleReadMore(petitionId: number) {
		goto(`/petition/${petitionId}`);
	}

	function loadMorePetitions() {
		visiblePetitionsCount += 20;
	}
</script>

<MainLayout>
	<div
		class="container mx-auto py-8 px-4"
		in:fly={{ opacity: 0, duration: 500 }}
	>
	<!-- Header -->
	<header class="text-center mb-8 space-y-2">
		<h1 class="text-4xl font-extrabold gradient-text from-primary to-accent">Petitions</h1>
		<p class="text-muted-foreground text-sm md:text-base">
			Create and sign petitions to drive change in your community and country.
		</p>
	</header>

	<!-- Search and Create -->
	<div class="flex flex-col sm:flex-row gap-4 items-center sticky top-[70px] md:top-[80px] z-30 bg-background/80 backdrop-blur-sm py-3 px-2 -mx-2 rounded-b-lg shadow-sm mb-6">
		<div class="relative flex-grow w-full sm:w-auto">
			<Input
				type="search"
				placeholder="Search petitions by title, keyword, or tagged leader..."
				bind:value={searchTerm}
				className="modern-input pl-4 pr-10 text-base h-11"
			/>
			<Search class="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
		</div>
		<Button onclick={handleCreatePetition} className="btn-primary rounded-full px-6 py-2.5 text-base w-full sm:w-auto h-11">
			<PlusCircle size={20} class="mr-2" /> Create Petition
		</Button>
	</div>

	<!-- Petitions Grid -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pt-4">
		{#if petitionsToShow.length > 0}
			{#each petitionsToShow as petition (petition.id)}
				<Card class="modern-card h-full flex flex-col">
					<CardHeader>
						<CardTitle class="text-lg text-primary line-clamp-2">{petition.title}</CardTitle>
						<div class="flex items-center gap-2 text-xs text-muted-foreground">
							<span>by {petition.createdBy}</span>
							{#if petition.taggedLeader}
								<span>• Tagged: {petition.taggedLeader}</span>
							{/if}
						</div>
					</CardHeader>
					
					<CardContent className="flex-grow p-6 pt-0">
						<p class="text-sm text-muted-foreground line-clamp-3 mb-4">{petition.description}</p>
						
						<!-- Progress -->
						<div class="space-y-2">
							<div class="flex justify-between text-sm">
								<span class="text-muted-foreground">Progress</span>
								<span class="font-medium">{petition.signatures} / {petition.target}</span>
							</div>
							<Progress value={(petition.signatures / petition.target) * 100} class="h-2" />
							<p class="text-xs text-muted-foreground">
								{Math.round((petition.signatures / petition.target) * 100)}% of target reached
							</p>
						</div>
					</CardContent>
					
					<CardFooter className="flex gap-2 p-6 pt-0">
						<Button
							size="sm"
							className="btn-accent flex-1"
							disabled={signingPetitionId === petition.id}
							onclick={() => handleSignPetition(petition.id)}
						>
							{#if signingPetitionId === petition.id}
								<div class="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1.5"></div>
								Signing...
							{:else}
								<Users size={16} class="mr-1.5" /> Sign Petition
							{/if}
						</Button>
						<Button
							variant="outline"
							size="sm"
							onclick={() => handleReadMore(petition.id)}
						>
							Read More
						</Button>
					</CardFooter>
				</Card>
			{/each}
		{:else}
			<div class="text-center py-10 modern-card md:col-span-2 lg:col-span-3">
				<FileText size={48} class="mx-auto text-muted-foreground/50 mb-4" />
				<p class="text-xl text-muted-foreground">No petitions found matching your search.</p>
				{#if searchTerm === ''}
					<p class="text-sm text-muted-foreground">Why not start one?</p>
				{/if}
			</div>
		{/if}
	</div>

	<!-- Load More -->
	{#if filteredPetitions.length > visiblePetitionsCount}
		<div class="text-center mt-8">
			<Button onclick={loadMorePetitions} className="btn-outline-primary">
				Load More Petitions
			</Button>
		</div>
	{/if}

	<!-- Create Petition Modal (placeholder) -->
	{#if showCreateModal}
		<div
			class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
			role="dialog"
			aria-modal="true"
			onclick={() => showCreateModal = false}
		>
			<div class="bg-background p-6 rounded-lg max-w-md w-full mx-4" onclick={(e) => e.stopPropagation()}>
				<h3 class="text-lg font-semibold mb-4">Create Petition</h3>
				<p class="text-muted-foreground mb-4">Petition creation functionality coming soon!</p>
				<Button onclick={() => showCreateModal = false} className="w-full">Close</Button>
			</div>
		</div>
	{/if}
</div>
</MainLayout>
