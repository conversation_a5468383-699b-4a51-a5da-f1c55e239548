# File Upload Feature Documentation

## Overview

The LeaderBox application includes a comprehensive file upload system using AWS S3 for secure, scalable file storage. This feature supports avatar uploads, cover images, and document uploads with proper validation, progress tracking, and error handling.

## Features

- **Direct S3 uploads** using presigned URLs for security and performance
- **Multiple file types** support (images, documents)
- **File validation** (type, size, format)
- **Progress tracking** with visual feedback
- **Drag & drop** interface
- **Preview functionality** for images
- **Error handling** with user-friendly messages
- **Reusable components** for different use cases

## Components

### 1. FileUpload.svelte
A general-purpose file upload component that can be configured for different use cases.

**Props:**
- `uploadType`: 'avatar' | 'cover' | 'documents'
- `currentFileUrl`: Current file URL (for editing)
- `label`: Display label
- `accept`: File type restrictions
- `disabled`: Disable upload
- `required`: Mark as required field
- `showPreview`: Show file preview
- `previewSize`: 'sm' | 'md' | 'lg'

**Events:**
- `upload`: Fired when upload succeeds
- `error`: Fired when upload fails
- `progress`: Fired during upload progress

### 2. AvatarUpload.svelte
Specialized component for avatar uploads with circular preview and overlay controls.

**Props:**
- `currentAvatarUrl`: Current avatar URL
- `userName`: User name for fallback initials
- `size`: 'sm' | 'md' | 'lg' | 'xl'
- `disabled`: Disable upload
- `required`: Mark as required field

## Configuration

### Upload Types

The system supports three predefined upload configurations:

#### Avatar
- **Folder**: `avatars/`
- **Allowed types**: JPEG, PNG, WebP
- **Max size**: 2MB
- **Thumbnail generation**: Yes

#### Cover Images
- **Folder**: `covers/`
- **Allowed types**: JPEG, PNG, WebP
- **Max size**: 5MB
- **Thumbnail generation**: No

#### Documents
- **Folder**: `documents/`
- **Allowed types**: PDF, DOC, DOCX
- **Max size**: 10MB
- **Thumbnail generation**: No

### Environment Variables

Add these to your `.env` file:

```env
# AWS S3 Configuration
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="your_aws_access_key_id"
AWS_SECRET_ACCESS_KEY="your_aws_secret_access_key"
S3_BUCKET_NAME="your_s3_bucket_name"
S3_BUCKET_URL="https://your_s3_bucket_name.s3.us-east-1.amazonaws.com"
```

## AWS S3 Setup

### 1. Create S3 Bucket

1. Go to AWS S3 Console
2. Create a new bucket with a unique name
3. Configure bucket settings:
   - **Block public access**: Uncheck "Block all public access" (required for public file access)
   - **Object Ownership**: Select "ACLs disabled (recommended)" - we'll use bucket policy instead
   - **Versioning**: Enable (optional)
   - **Encryption**: Enable (recommended)

### 2. Configure Bucket Policy

Add this bucket policy to allow public read access to uploaded files:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        }
    ]
}
```

### 3. Configure CORS

Add this CORS configuration to your S3 bucket:

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
        "AllowedOrigins": ["http://localhost:5173", "https://yourdomain.com"],
        "ExposeHeaders": ["ETag"]
    }
]
```

### 4. Create IAM User

1. Go to AWS IAM Console
2. Create a new user with programmatic access
3. Attach this policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:DeleteObject",
                "s3:PutObjectAcl"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name"
        }
    ]
}
```

## Usage Examples

### Basic File Upload

```svelte
<script>
    import FileUpload from '$lib/components/ui/FileUpload.svelte';
    
    let fileUrl = '';
    
    function handleUpload(event) {
        fileUrl = event.detail.fileUrl;
        console.log('File uploaded:', fileUrl);
    }
</script>

<FileUpload
    uploadType="cover"
    bind:currentFileUrl={fileUrl}
    label="Upload Cover Image"
    on:upload={handleUpload}
/>
```

### Avatar Upload

```svelte
<script>
    import AvatarUpload from '$lib/components/ui/AvatarUpload.svelte';
    
    let avatarUrl = '';
    let userName = 'John Doe';
    
    function handleAvatarUpload(event) {
        avatarUrl = event.detail.fileUrl;
    }
</script>

<AvatarUpload
    bind:currentAvatarUrl={avatarUrl}
    {userName}
    size="lg"
    on:upload={handleAvatarUpload}
/>
```

## API Endpoints

### POST /api/upload/presigned-url

Generates a presigned URL for direct S3 upload.

**Request:**
```json
{
    "fileName": "avatar.jpg",
    "fileType": "image/jpeg",
    "fileSize": 1024000,
    "uploadType": "avatar"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "uploadUrl": "https://s3.amazonaws.com/...",
        "fileUrl": "https://bucket.s3.amazonaws.com/avatars/...",
        "key": "avatars/timestamp-uuid.jpg"
    }
}
```

### GET /api/upload/presigned-url

Retrieves upload configurations.

**Query Parameters:**
- `type`: Upload type (optional)

## Security Considerations

1. **Presigned URLs** expire after 1 hour
2. **File validation** on both client and server
3. **Size limits** enforced per upload type
4. **MIME type validation** prevents malicious uploads
5. **Unique filenames** prevent conflicts and overwrites
6. **Authentication required** for all upload operations

## Troubleshooting

### Common Issues

1. **CORS errors**: Check S3 bucket CORS configuration
2. **Access denied**: Verify IAM permissions and bucket policy
3. **File too large**: Check upload type size limits
4. **Invalid file type**: Verify allowed MIME types
5. **Upload timeout**: Check network connection and file size
6. **400 Bad Request with ACL error**: Ensure "Object Ownership" is set to "ACLs disabled" in bucket settings
7. **Files not publicly accessible**: Verify bucket policy allows public read access

### Debug Mode

Set `NODE_ENV=development` to see detailed error logs in the console.

## File Naming Convention

Files are automatically renamed using this pattern:
```
{folder}/{timestamp}-{uuid}.{extension}
```

Example: `avatars/1703123456789-a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg`

This ensures:
- **Uniqueness**: No file conflicts
- **Organization**: Files grouped by type
- **Traceability**: Timestamp for debugging
- **Security**: Original filenames not exposed
