# Component Conversion Checklist

## Detailed Component Analysis & Conversion Tasks

### Priority 1: Core UI Components (15 components)

#### 1.1 Dialog System
- [ ] **dialog.jsx** → **Dialog.svelte**
  - Props: `open`, `onOpenChange`, `children`
  - State: Dialog open/close state
  - Events: `onOpenChange` callback
  - Features: Modal overlay, focus management, escape key handling

- [ ] **alert-dialog.jsx** → **AlertDialog.svelte**  
  - Props: `open`, `onOpenChange`, `title`, `description`, `onConfirm`, `onCancel`
  - State: Confirmation state
  - Events: Confirm/cancel actions
  - Features: Confirmation dialogs with actions

- [ ] **sheet.jsx** → **Sheet.svelte**
  - Props: `open`, `onOpenChange`, `side`, `children`
  - State: Sheet visibility
  - Events: Open/close events
  - Features: Slide-out panels from sides

#### 1.2 Form Components  
- [ ] **textarea.jsx** → **Textarea.svelte**
  - Props: `value`, `placeholder`, `rows`, `className`, `disabled`
  - State: Text content
  - Events: `on:input`, `on:change`
  - Features: Multi-line text input with auto-resize

- [ ] **label.jsx** → **Label.svelte**
  - Props: `htmlFor`, `className`, `children`
  - Features: Form labels with proper accessibility

- [ ] **switch.jsx** → **Switch.svelte**
  - Props: `checked`, `onCheckedChange`, `disabled`, `className`
  - State: Toggle state
  - Events: `on:change`
  - Features: Toggle switch component

- [ ] **combobox.jsx** → **Combobox.svelte**
  - Props: `options`, `value`, `onChange`, `placeholder`, `disabled`
  - State: `open`, `inputValue`, `popoverWidth`
  - Events: Selection change, input change
  - Features: Searchable dropdown with filtering

#### 1.3 Navigation & Layout
- [ ] **dropdown-menu.jsx** → **DropdownMenu.svelte**
  - Props: `trigger`, `items`, `onSelect`
  - State: Menu open state
  - Events: Item selection
  - Features: Context menus and dropdowns

- [ ] **tabs.jsx** → **Tabs.svelte**
  - Props: `value`, `onValueChange`, `tabs`, `children`
  - State: Active tab
  - Events: Tab change
  - Features: Tabbed interface component

- [ ] **popover.jsx** → **Popover.svelte**
  - Props: `open`, `onOpenChange`, `trigger`, `content`
  - State: Popover visibility
  - Events: Open/close events
  - Features: Floating content panels

- [ ] **command.jsx** → **Command.svelte**
  - Props: `value`, `onValueChange`, `items`
  - State: Search value, filtered items
  - Events: Command selection
  - Features: Command palette interface

#### 1.4 Data Display
- [ ] **table.jsx** → **Table.svelte**
  - Props: `columns`, `data`, `sortable`, `onSort`
  - State: Sort configuration
  - Events: Column sorting, row selection
  - Features: Data table with sorting and selection

- [ ] **progress.jsx** → **Progress.svelte**
  - Props: `value`, `max`, `className`
  - Features: Progress bar component

- [ ] **scroll-area.jsx** → **ScrollArea.svelte**
  - Props: `className`, `children`, `height`
  - Features: Custom scrollable area

- [ ] **tooltip.jsx** → **Tooltip.svelte**
  - Props: `content`, `children`, `side`, `delay`
  - State: Tooltip visibility
  - Events: Hover events
  - Features: Hover tooltips with positioning

### Priority 2: Media & Shared Components (4 components)

- [ ] **MediaDisplayItem.jsx** → **MediaDisplayItem.svelte**
  - Props: `item`, `onClick`, `className`, `overlayText`
  - State: Loading state
  - Events: Click handling
  - Features: Display single media item (image/video/file)

- [ ] **MediaGrid.jsx** → **MediaGrid.svelte**
  - Props: `mediaItems`
  - State: `viewerState` (isOpen, imageUrl)
  - Events: Item click, viewer open/close
  - Features: Grid layout for multiple media items

- [ ] **MediaUpload.jsx** → **MediaUpload.svelte**
  - Props: `onFilesChange`, `existingFiles`, `maxFiles`, `maxFileSizeMB`
  - State: `selectedFilesData`, `isImageViewerOpen`, `selectedImageUrl`
  - Events: File selection, file removal, preview
  - Features: File upload with preview and validation

- [ ] **ImageViewerModal.jsx** → **ImageViewerModal.svelte**
  - Props: `isOpen`, `onClose`, `imageUrl`, `imageAlt`
  - State: Modal visibility
  - Events: Close events, keyboard navigation
  - Features: Full-screen image viewer

### Priority 3: Feature Components (8 components)

#### 3.1 Banter Components
- [ ] **BanterCard.jsx** → **BanterCard.svelte**
  - Props: `banter`, `user`, `onVote`, `onComment`
  - State: Comment text, reply state
  - Events: Vote, comment, reply actions
  - Features: Display banter post with interactions

- [ ] **StartBanterDialog.jsx** → **StartBanterDialog.svelte**
  - Props: `isOpen`, `onClose`, `onSubmit`
  - State: Form data (title, content, tags, leaders, etc.)
  - Events: Form submission, file upload
  - Features: Create new banter post

- [ ] **EditBanterDialog.jsx** → **EditBanterDialog.svelte**
  - Props: `isOpen`, `onClose`, `banter`, `onUpdate`
  - State: Edit form data
  - Events: Update submission
  - Features: Edit existing banter post

#### 3.2 Groups Components  
- [ ] **CreateGroupDialog.jsx** → **CreateGroupDialog.svelte**
  - Props: `isOpen`, `onClose`, `onSubmit`
  - State: Group form data (name, description, privacy, etc.)
  - Events: Form submission
  - Features: Create new group

- [ ] **StartDiscussionDialog.jsx** → **StartDiscussionDialog.svelte**
  - Props: `isOpen`, `onClose`, `groupId`, `onSubmit`
  - State: Discussion form data
  - Events: Form submission
  - Features: Start group discussion

#### 3.3 User Profile Components
- [ ] **UserActivityTabsContent.jsx** → **UserActivityTabsContent.svelte**
  - Props: `user`, `activities`, `activeTab`
  - State: Tab state, activity data
  - Events: Tab switching
  - Features: User activity timeline

- [ ] **UserProfileHeaderCard.jsx** → **UserProfileHeaderCard.svelte**
  - Props: `user`, `isOwnProfile`, `onFollow`, `onMessage`
  - State: Follow state
  - Events: Follow/unfollow, message actions
  - Features: User profile header with actions

- [ ] **UserProfileStatsCard.jsx** → **UserProfileStatsCard.svelte**
  - Props: `user`, `stats`
  - Features: Display user statistics

### Priority 4: Admin Components (2 components)

- [ ] **AdminLayout.jsx** → **AdminLayout.svelte**
  - Props: Navigation items, user data
  - State: `isMobileMenuOpen`
  - Events: Navigation, menu toggle
  - Features: Admin dashboard layout with sidebar

- [ ] **UserTable.jsx** → **UserTable.svelte**
  - Props: `users`, `sortConfig`, `onEdit`, `onFlag`, `onBan`, `onDelete`
  - State: Sort configuration
  - Events: User actions, sorting
  - Features: Admin user management table

### Priority 5: Home Components (1 component)

- [ ] **SuggestLeaderDialog.jsx** → **SuggestLeaderDialog.svelte**
  - Props: `isOpen`, `onClose`, `onSubmit`
  - State: Suggestion form data
  - Events: Form submission
  - Features: Suggest new leader form

## Conversion Standards

### File Structure
```
leaderboxafrica/src/lib/components/
├── ui/                 # Core UI components
├── shared/            # Shared utility components  
├── banter/            # Banter-specific components
├── groups/            # Groups-specific components
├── user-profile/      # User profile components
├── admin/             # Admin components
├── home/              # Home page components
└── layouts/           # Layout components
```

### Component Template
```svelte
<!-- Component Name - Brief description -->
<script lang="ts">
  // Props
  let { propName, anotherProp = defaultValue } = $props();
  
  // State
  let localState = $state(initialValue);
  
  // Derived values
  let computedValue = $derived(localState * 2);
  
  // Effects
  $effect(() => {
    // Side effects here
  });
  
  // Event handlers
  function handleClick() {
    localState++;
  }
</script>

<!-- Template -->
<div class="component-class" on:click={handleClick}>
  <!-- Component content -->
</div>
```

## Progress Tracking
- Total Components: 30
- Completed: 0
- In Progress: 0  
- Remaining: 30

Last Updated: [Current Date]
