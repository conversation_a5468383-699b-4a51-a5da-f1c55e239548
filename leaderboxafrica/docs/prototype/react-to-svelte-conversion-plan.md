# React to SvelteKit Component Conversion Plan

## Overview
This document outlines the comprehensive plan for converting all React components from `/leaderbox-react/src/components/` to Svelte 5 components with Runes in `/leaderboxafrica/src/lib/components/`.

## Conversion Rules & Guidelines

### 1. Svelte 5 Runes Conversion
- **Props**: `let { propName } = $props()` 
- **State**: `useState(value)` → `let state = $state(value)`
- **State Updates**: `setState(newValue)` → `state = newValue` or `state++`
- **Derived Values**: `useEffect` for calculations → `$derived()`
- **Side Effects**: `useEffect` for DOM/API calls → `$effect()`
- **Event Handlers**: `onClick` → `on:click`
- **CSS Classes**: `className` → `class`

### 2. Store Usage
- Use Svelte stores instead of implementing full functionality
- Import existing stores from `$lib/stores/`
- Focus on component structure and props, not business logic

### 3. Already Converted Components (DO NOT RECREATE)
✅ **Layouts**
- `MainLayout.svelte` - Already converted and using Svelte 5

✅ **UI Components** 
- `Avatar.svelte`
- `Button.svelte` 
- `Card.svelte`, `CardContent.svelte`, `CardHeader.svelte`, `CardFooter.svelte`
- `Input.svelte`
- `Select.svelte`
- `Toast.svelte`
- `Badge.svelte`
- `FileUpload.svelte`
- `AvatarUpload.svelte`

✅ **Dashboard Components**
- `ActivityLogItem.svelte`
- `DashboardActivityTab.svelte`
- `DashboardFeedTab.svelte`
- `DashboardLeadersTab.svelte`
- `FeedItemCard.svelte`
- `SuggestedActionCard.svelte`

✅ **Leader Profile Components**
- `DirectMessageDialog.svelte`
- `LeaderActivityTabs.svelte`
- `LeaderBioCard.svelte`
- `LeaderEngagementCard.svelte`
- `LeaderMetricsCard.svelte`
- `LeaderProfileHeader.svelte`
- `RatingDialog.svelte`
- `SuggestEditDialog.svelte`

✅ **Poll Components**
- `CommentAfterVoteDialog.svelte`
- `CreatePollDialog.svelte`
- `PollCard.svelte`

✅ **Other**
- `ProfileImageReminder.svelte`

## Components to Convert

### Priority 1: Core UI Components
1. **Dialog System** (`/ui/`)
   - `dialog.jsx` → `Dialog.svelte`
   - `alert-dialog.jsx` → `AlertDialog.svelte`
   - `sheet.jsx` → `Sheet.svelte`

2. **Form Components** (`/ui/`)
   - `textarea.jsx` → `Textarea.svelte`
   - `label.jsx` → `Label.svelte`
   - `switch.jsx` → `Switch.svelte`
   - `combobox.jsx` → `Combobox.svelte`

3. **Navigation & Layout** (`/ui/`)
   - `dropdown-menu.jsx` → `DropdownMenu.svelte`
   - `tabs.jsx` → `Tabs.svelte`
   - `popover.jsx` → `Popover.svelte`
   - `command.jsx` → `Command.svelte`

4. **Data Display** (`/ui/`)
   - `table.jsx` → `Table.svelte`
   - `progress.jsx` → `Progress.svelte`
   - `scroll-area.jsx` → `ScrollArea.svelte`
   - `tooltip.jsx` → `Tooltip.svelte`

### Priority 2: Media & Shared Components
5. **Media Components** (`/shared/`, `/ui/`)
   - `MediaDisplayItem.jsx` → `MediaDisplayItem.svelte`
   - `MediaGrid.jsx` → `MediaGrid.svelte`
   - `MediaUpload.jsx` → `MediaUpload.svelte`
   - `ImageViewerModal.jsx` → `ImageViewerModal.svelte`

### Priority 3: Feature Components
6. **Banter Components** (`/banter/`)
   - `BanterCard.jsx` → `BanterCard.svelte`
   - `StartBanterDialog.jsx` → `StartBanterDialog.svelte`
   - `EditBanterDialog.jsx` → `EditBanterDialog.svelte`

7. **Groups Components** (`/groups/`)
   - `CreateGroupDialog.jsx` → `CreateGroupDialog.svelte`
   - `StartDiscussionDialog.jsx` → `StartDiscussionDialog.svelte`

8. **User Profile Components** (`/user-profile/`)
   - `UserActivityTabsContent.jsx` → `UserActivityTabsContent.svelte`
   - `UserProfileHeaderCard.jsx` → `UserProfileHeaderCard.svelte`
   - `UserProfileStatsCard.jsx` → `UserProfileStatsCard.svelte`

### Priority 4: Admin Components
9. **Admin Layout** (`/layouts/`)
   - `AdminLayout.jsx` → `AdminLayout.svelte`

10. **Admin User Management** (`/admin/`)
    - `UserTable.jsx` → `UserTable.svelte`

### Priority 5: Home Components
11. **Home Components** (`/home/<USER>
    - `SuggestLeaderDialog.jsx` → `SuggestLeaderDialog.svelte`

## Conversion Progress Tracking

### Status Legend
- 🔴 Not Started
- 🟡 In Progress  
- 🟢 Completed
- ⚪ Skipped (Already exists)

### Current Status
| Component Category | Status | Count | Notes |
|-------------------|--------|-------|-------|
| UI Components | 🔴 | 15 | Core UI building blocks |
| Media Components | 🔴 | 4 | File upload and display |
| Banter Components | 🔴 | 3 | Chat/discussion features |
| Groups Components | 🔴 | 2 | Group management |
| User Profile Components | 🔴 | 3 | User profile display |
| Admin Components | 🔴 | 2 | Admin interface |
| Home Components | 🔴 | 1 | Homepage features |
| **TOTAL** | 🔴 | **30** | Components to convert |

## Next Steps
1. Start with Priority 1 (Core UI Components)
2. Test each component as it's converted
3. Update this document with progress
4. Move to next priority level
5. Document any issues or deviations

## Notes
- Focus on component structure and props interface
- Use existing Svelte stores for data management
- Maintain same visual styling and behavior
- Test components in isolation before integration
