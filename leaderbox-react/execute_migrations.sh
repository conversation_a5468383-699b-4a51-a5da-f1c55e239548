#!/bin/bash

# LeaderBox Database Migration Executor
# This script helps you execute migrations on your Supabase instance

set -e

SUPABASE_URL="https://supabase.linkfa.de"
SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE2NzI1MzEyMDAsImV4cCI6MTk4ODE4ODgwMH0.fIs4Oqe-Kd4s2tQZhCdbBlNv12y2Tq9BlSI3GMe3ZVs"

echo "🚀 LeaderBox Database Setup"
echo "=========================="
echo ""

# Function to check if Supabase is accessible
check_supabase() {
    echo "🔍 Checking Supabase connection..."
    response=$(curl -s -H "apikey: $SERVICE_ROLE_KEY" "$SUPABASE_URL/rest/v1/")
    if [[ $response == *"swagger"* ]]; then
        echo "✅ Supabase is accessible"
        return 0
    else
        echo "❌ Cannot connect to Supabase"
        return 1
    fi
}

# Function to verify tables exist
verify_tables() {
    echo "🔍 Verifying database setup..."
    
    # Try to access the users table (should exist after migration)
    response=$(curl -s -H "apikey: $SERVICE_ROLE_KEY" -H "Authorization: Bearer $SERVICE_ROLE_KEY" "$SUPABASE_URL/rest/v1/users?limit=1")
    
    if [[ $response == *"[]"* ]] || [[ $response == *"{"* ]]; then
        echo "✅ Database tables are accessible"
        return 0
    else
        echo "⚠️  Database tables not found or not accessible"
        echo "Response: $response"
        return 1
    fi
}

# Function to show migration instructions
show_instructions() {
    echo ""
    echo "📋 MIGRATION INSTRUCTIONS"
    echo "========================"
    echo ""
    echo "Since direct SQL execution through REST API is limited, please follow these steps:"
    echo ""
    echo "1. Open your Supabase Dashboard:"
    echo "   🌐 https://supabase.linkfa.de"
    echo ""
    echo "2. Navigate to 'SQL Editor' in the sidebar"
    echo ""
    echo "3. Execute each migration file in order:"
    echo ""
    echo "   📄 Step 1: supabase/migrations/001_initial_schema.sql"
    echo "   📄 Step 2: supabase/migrations/002_database_functions.sql"
    echo "   📄 Step 3: supabase/migrations/003_performance_indexes.sql"
    echo "   📄 Step 4: supabase/migrations/004_rls_policies.sql"
    echo "   📄 Step 5: supabase/migrations/005_seed_data.sql (optional)"
    echo ""
    echo "4. Copy and paste the contents of each file into the SQL editor"
    echo "5. Click 'Run' to execute each migration"
    echo ""
    echo "🔧 Your connection details:"
    echo "   URL: $SUPABASE_URL"
    echo "   Service Role Key: $SERVICE_ROLE_KEY"
    echo ""
}

# Function to test API after setup
test_api() {
    echo "🧪 Testing API endpoints..."
    
    # Test users endpoint
    echo "Testing /users endpoint..."
    curl -s -H "apikey: $SERVICE_ROLE_KEY" "$SUPABASE_URL/rest/v1/users?limit=1" | head -c 100
    echo ""
    
    # Test leaders endpoint
    echo "Testing /leaders endpoint..."
    curl -s -H "apikey: $SERVICE_ROLE_KEY" "$SUPABASE_URL/rest/v1/leaders?limit=1" | head -c 100
    echo ""
    
    # Test polls endpoint
    echo "Testing /polls endpoint..."
    curl -s -H "apikey: $SERVICE_ROLE_KEY" "$SUPABASE_URL/rest/v1/polls?limit=1" | head -c 100
    echo ""
}

# Main execution
main() {
    if check_supabase; then
        echo ""
        if verify_tables; then
            echo ""
            echo "🎉 Database appears to be already set up!"
            echo "Running API tests..."
            test_api
        else
            echo ""
            echo "📋 Database needs to be set up."
            show_instructions
        fi
    else
        echo ""
        echo "❌ Cannot connect to Supabase. Please check your configuration."
        exit 1
    fi
    
    echo ""
    echo "📚 For detailed instructions, see: DATABASE_SETUP_GUIDE.md"
    echo ""
}

# Run the script
main
