# LeaderBox Database Setup Guide

## 🎯 Quick Setup Instructions

Your Supabase instance at `https://supabase.linkfa.de` is **working perfectly**! Here's how to set up your database:

### Method 1: Using Supabase Dashboard (Recommended)

1. **Access your Supabase Dashboard**
   - Go to `https://supabase.linkfa.de` in your browser
   - Log in with your admin credentials

2. **Navigate to SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - This allows you to execute SQL directly on your database

3. **Execute Migrations in Order**
   Execute each migration file in the SQL editor:

   **Step 1: Initial Schema**
   ```sql
   -- Copy and paste the contents of supabase/migrations/001_initial_schema.sql
   ```

   **Step 2: Database Functions**
   ```sql
   -- Copy and paste the contents of supabase/migrations/002_database_functions.sql
   ```

   **Step 3: Performance Indexes**
   ```sql
   -- Copy and paste the contents of supabase/migrations/003_performance_indexes.sql
   ```

   **Step 4: RLS Policies**
   ```sql
   -- Copy and paste the contents of supabase/migrations/004_rls_policies.sql
   ```

   **Step 5: Seed Data (Optional)**
   ```sql
   -- Copy and paste the contents of supabase/migrations/005_seed_data.sql
   ```

### Method 2: Using a Database Client

If you have access to a PostgreSQL client that can connect to your Supabase instance:

```bash
# If your instance allows external connections
psql "postgresql://postgres:[SERVICE_ROLE_KEY]@supabase.linkfa.de:5432/postgres"
```

**Note**: Most hosted Supabase instances don't expose port 5432 for security reasons.

### Method 3: Using Supabase CLI (If Available)

```bash
# Install Supabase CLI
npm install -g supabase

# Link to your project
supabase link --project-ref your-project-ref

# Apply migrations
supabase db push
```

## 📋 Migration Files Summary

### 001_initial_schema.sql
- Creates all core tables (users, leaders, polls, banters, petitions, groups)
- Sets up relationships and foreign keys
- Adds UUID extension
- Creates updated_at triggers

### 002_database_functions.sql
- Custom PostgreSQL functions for business logic
- Rating calculations
- Vote counting functions
- Search functions

### 003_performance_indexes.sql
- Performance indexes for all major queries
- Full-text search indexes
- Composite indexes for complex queries

### 004_rls_policies.sql
- Row Level Security policies
- User authentication and authorization
- Data access controls

### 005_seed_data.sql
- Demo users, leaders, polls, and groups
- Sample data for testing and development

## ✅ Verification Steps

After running the migrations, verify your setup:

1. **Check Tables Created**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' ORDER BY table_name;
   ```

2. **Check RLS Status**
   ```sql
   SELECT schemaname, tablename, rowsecurity 
   FROM pg_tables WHERE schemaname = 'public';
   ```

3. **Check Functions**
   ```sql
   SELECT routine_name FROM information_schema.routines 
   WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';
   ```

4. **Test API Access**
   ```bash
   curl -H "apikey: YOUR_ANON_KEY" "https://supabase.linkfa.de/rest/v1/users"
   ```

## 🔧 Your Configuration

- **Supabase URL**: `https://supabase.linkfa.de`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNjcyNTMxMjAwLCJleHAiOjE5ODgxODg4MDB9.nHnlkKkZDGIc6_UgY6C3tmQc1Mpls2lU07lxShTbFuE`
- **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE2NzI1MzEyMDAsImV4cCI6MTk4ODE4ODgwMH0.fIs4Oqe-Kd4s2tQZhCdbBlNv12y2Tq9BlSI3GMe3ZVs`

## 🚀 Next Steps

After database setup:

1. **Update your application's environment variables**
2. **Test the API endpoints**
3. **Set up authentication in your app**
4. **Migrate existing localStorage data**

## 📞 Need Help?

If you encounter any issues:
1. Check the Supabase dashboard logs
2. Verify all migrations ran successfully
3. Test API access with curl commands
4. Check RLS policies are properly applied

Your Supabase instance is ready - just need to execute the migrations!
