# supabase/config.toml
# This file configures the Supabase CLI to connect to your
# remote self-hosted project at supabase.linkfa.de.

# This is a placeholder and not used for self-hosted projects.
project_id = "leaderbox-self-hosted"

# [IMPORTANT] This section points the CLI to your server.
[api]
# The main URL of your Supabase instance.
endpoint = "supabase.linkfa.de" 

# Your public ANON_KEY. This allows the CLI to perform public actions.
# You must get this from your Dokploy environment variables.
# Replace the placeholder below with your REAL ANON_KEY.
anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNjcyNTMxMjAwLCJleHAiOjE5ODgxODg4MDB9.nHnlkKkZDGIc6_UgY6C3tmQc1Mpls2lU07lxShTbFuE"

# The SERVICE_ROLE_KEY is NOT stored here for security reasons.
# It is provided to the MCP server via the --access-token flag.