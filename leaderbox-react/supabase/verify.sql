-- LeaderBox Database Verification Script
-- Run this script to verify that your database setup is working correctly

\echo '================================================'
\echo 'LeaderBox Database Verification'
\echo '================================================'

-- Test 1: Check all tables exist
\echo 'Test 1: Verifying table structure...'
DO $$
DECLARE
    expected_tables text[] := ARRAY[
        'users', 'leaders', 'user_leader_follows', 'user_leader_ratings',
        'leader_comments', 'comment_votes', 'polls', 'poll_options',
        'poll_votes', 'poll_comments', 'banters', 'banter_votes',
        'banter_comments', 'petitions', 'petition_signatures',
        'groups', 'group_members', 'group_discussions', 'user_profile_likes'
    ];
    table_name text;
    table_exists boolean;
BEGIN
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = table_name
        ) INTO table_exists;
        
        IF table_exists THEN
            RAISE NOTICE 'Table % exists ✓', table_name;
        ELSE
            RAISE WARNING 'Table % missing ✗', table_name;
        END IF;
    END LOOP;
END $$;

-- Test 2: Check functions exist
\echo 'Test 2: Verifying database functions...'
DO $$
DECLARE
    expected_functions text[] := ARRAY[
        'increment_followers', 'recalculate_leader_rating', 'update_poll_vote_counts',
        'update_comment_votes', 'update_banter_votes', 'update_petition_signatures',
        'search_leaders', 'get_user_followed_leaders', 'user_follows_leader'
    ];
    function_name text;
    function_exists boolean;
BEGIN
    FOREACH function_name IN ARRAY expected_functions
    LOOP
        SELECT EXISTS (
            SELECT FROM information_schema.routines 
            WHERE routine_schema = 'public' 
              AND routine_name = function_name
              AND routine_type = 'FUNCTION'
        ) INTO function_exists;
        
        IF function_exists THEN
            RAISE NOTICE 'Function % exists ✓', function_name;
        ELSE
            RAISE WARNING 'Function % missing ✗', function_name;
        END IF;
    END LOOP;
END $$;

-- Test 3: Check RLS is enabled
\echo 'Test 3: Verifying Row Level Security...'
SELECT 
    tablename,
    CASE WHEN rowsecurity THEN 'Enabled ✓' ELSE 'Disabled ✗' END as rls_status
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Test 4: Check indexes exist
\echo 'Test 4: Verifying performance indexes...'
SELECT 
    schemaname,
    tablename,
    COUNT(*) as index_count
FROM pg_indexes 
WHERE schemaname = 'public' 
GROUP BY schemaname, tablename
ORDER BY tablename;

-- Test 5: Test sample queries
\echo 'Test 5: Testing sample queries...'

-- Test user query
\echo 'Testing user query...'
SELECT 
    'User query test' as test,
    count(*) as user_count,
    CASE WHEN count(*) > 0 THEN 'Pass ✓' ELSE 'Fail ✗' END as status
FROM users;

-- Test leader query with relationships
\echo 'Testing leader query with relationships...'
SELECT 
    'Leader relationship test' as test,
    count(*) as leader_count,
    CASE WHEN count(*) > 0 THEN 'Pass ✓' ELSE 'Fail ✗' END as status
FROM leaders l
LEFT JOIN user_leader_follows ulf ON l.id = ulf.leader_id;

-- Test poll query with options
\echo 'Testing poll query with options...'
SELECT 
    'Poll options test' as test,
    count(*) as poll_option_count,
    CASE WHEN count(*) > 0 THEN 'Pass ✓' ELSE 'Fail ✗' END as status
FROM polls p
JOIN poll_options po ON p.id = po.poll_id;

-- Test 6: Test database functions
\echo 'Test 6: Testing database functions...'

-- Test search function
\echo 'Testing search function...'
SELECT 
    'Search function test' as test,
    count(*) as search_results,
    CASE WHEN count(*) > 0 THEN 'Pass ✓' ELSE 'Fail ✗' END as status
FROM search_leaders('Tinubu');

-- Test user follows function
\echo 'Testing user follows function...'
SELECT 
    'User follows function test' as test,
    user_follows_leader('userDemo123', '1') as follows_result,
    CASE WHEN user_follows_leader('userDemo123', '1') IS NOT NULL THEN 'Pass ✓' ELSE 'Fail ✗' END as status;

-- Test 7: Check data integrity
\echo 'Test 7: Checking data integrity...'

-- Check foreign key constraints
SELECT 
    'Foreign key constraints' as test,
    count(*) as constraint_count,
    CASE WHEN count(*) > 0 THEN 'Pass ✓' ELSE 'Fail ✗' END as status
FROM information_schema.table_constraints 
WHERE constraint_schema = 'public' 
  AND constraint_type = 'FOREIGN KEY';

-- Check unique constraints
SELECT 
    'Unique constraints' as test,
    count(*) as constraint_count,
    CASE WHEN count(*) > 0 THEN 'Pass ✓' ELSE 'Fail ✗' END as status
FROM information_schema.table_constraints 
WHERE constraint_schema = 'public' 
  AND constraint_type = 'UNIQUE';

-- Test 8: Performance test
\echo 'Test 8: Basic performance test...'

-- Test index usage
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM leaders WHERE name ILIKE '%Tinubu%';

EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM polls ORDER BY created_at DESC LIMIT 10;

-- Test 9: Real-time readiness
\echo 'Test 9: Checking real-time readiness...'
SELECT 
    'Real-time tables' as test,
    count(*) as table_count,
    CASE WHEN count(*) > 0 THEN 'Ready ✓' ELSE 'Not Ready ✗' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('leaders', 'polls', 'banters', 'leader_comments', 'poll_votes');

-- Final summary
\echo '================================================'
\echo 'Verification Summary'
\echo '================================================'

SELECT 
    'Database Setup' as component,
    'Complete' as status,
    NOW() as verified_at;

\echo 'Verification completed!'
\echo 'If all tests show ✓, your database is ready for use.'
\echo 'If any tests show ✗, review the setup steps and error messages.'
\echo '================================================'
