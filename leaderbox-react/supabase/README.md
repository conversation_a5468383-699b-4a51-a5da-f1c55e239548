# LeaderBox Supabase Database Setup

This directory contains all the database migrations and configuration files needed to set up your LeaderBox application with your self-hosted Supabase instance at https://supabase.linkfa.de.

## 📁 File Structure

```
supabase/
├── migrations/
│   ├── 001_initial_schema.sql      # Core database schema
│   ├── 002_database_functions.sql  # PostgreSQL functions
│   ├── 003_performance_indexes.sql # Performance indexes
│   ├── 004_rls_policies.sql       # Row Level Security policies
│   └── 005_seed_data.sql          # Initial demo data
├── config.toml                    # Supabase configuration
└── README.md                      # This file
```

## 🚀 Quick Setup

### 1. Prerequisites

- Access to your self-hosted Supabase instance at https://supabase.linkfa.de
- PostgreSQL admin access
- Supabase CLI (optional, for local development)

### 2. Database Setup

Execute the migration files in order on your Supabase instance:

```sql
-- 1. Create the core schema
\i 001_initial_schema.sql

-- 2. Add database functions
\i 002_database_functions.sql

-- 3. Create performance indexes
\i 003_performance_indexes.sql

-- 4. Set up Row Level Security
\i 004_rls_policies.sql

-- 5. Insert seed data (optional, for development)
\i 005_seed_data.sql
```

### 3. Authentication Configuration

In your Supabase dashboard:

1. **Enable Email/Password Authentication**
   - Go to Authentication > Settings
   - Enable "Enable email confirmations" if desired
   - Set up email templates

2. **Configure Site URL**
   - Set your application URL (e.g., `http://localhost:5173` for development)
   - Add any additional redirect URLs

3. **API Keys**
   - Copy your `anon` key and `service_role` key
   - These will be used in your application's environment variables

### 4. Environment Variables

Create a `.env.local` file in your project root:

```env
VITE_SUPABASE_URL=https://supabase.linkfa.de
VITE_SUPABASE_ANON_KEY=your_anon_key_here
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## 📊 Database Schema Overview

### Core Tables

- **users** - User profiles and authentication data
- **leaders** - Political leaders and their information
- **polls** - Polls with options and voting data
- **banters** - Social posts/discussions
- **petitions** - Petition campaigns
- **groups** - User groups and communities

### Relationship Tables

- **user_leader_follows** - User-leader follow relationships
- **user_leader_ratings** - User ratings for leaders
- **leader_comments** - Comments on leader profiles
- **poll_votes** - User votes on polls
- **petition_signatures** - Petition signatures
- **group_members** - Group membership

### Features Included

✅ **Row Level Security (RLS)** - Secure data access based on user authentication
✅ **Performance Indexes** - Optimized queries for all major operations
✅ **Database Functions** - Complex operations like rating calculations
✅ **Full-text Search** - Search across leaders, polls, groups, and petitions
✅ **Real-time Ready** - Optimized for Supabase real-time subscriptions
✅ **Audit Trails** - Created/updated timestamps on all records

## 🔧 Database Functions

The following custom functions are available:

- `increment_followers(leader_id, increment_by)` - Update leader follower count
- `recalculate_leader_rating(leader_id)` - Recalculate average rating
- `update_poll_vote_counts(poll_id)` - Update poll vote totals
- `update_comment_votes(comment_id, comment_type)` - Update comment vote counts
- `search_leaders(search_term)` - Full-text search for leaders
- `get_user_followed_leaders(user_id)` - Get user's followed leaders
- `user_follows_leader(user_id, leader_id)` - Check if user follows leader

## 🔒 Security Features

### Row Level Security Policies

- **Users**: Can view/edit own profile, admins can manage all
- **Leaders**: Public read access, admin-only write access
- **Polls**: Public read, authenticated users can create/vote
- **Comments**: Public read, users can manage their own
- **Groups**: Public groups visible to all, private groups to members only
- **Votes/Ratings**: Users can only manage their own

### Data Protection

- All sensitive operations require authentication
- Admin-only access for content moderation
- Automatic audit trails for all changes
- Secure handling of user data and relationships

## 📈 Performance Optimizations

### Indexes Created

- **Primary Operations**: User lookups, leader searches, poll voting
- **Full-text Search**: Trigram indexes for fuzzy matching
- **Real-time Queries**: Optimized for live updates
- **Composite Indexes**: Multi-column indexes for complex queries

### Query Optimization

- Materialized views for complex aggregations (if needed)
- Partial indexes for active/visible content
- Statistics updates for query planner optimization

## 🧪 Seed Data

The seed data includes:

- **4 Demo Users**: Different roles (user, admin, editor, creator)
- **10 Political Leaders**: Mix of current and former officials
- **3 Sample Polls**: Energy, education, and security topics
- **5 Banter Posts**: Political discussions and debates
- **4 Petitions**: Infrastructure, education, employment, environment
- **4 Groups**: Youth governance, economic policy, women in politics, Lagos traffic

## 🔄 Migration Notes

### From localStorage to Supabase

When migrating existing localStorage data:

1. **User Data**: Maps to `users` table with profile information
2. **Leader Data**: Preserves all leader information and relationships
3. **Ratings/Comments**: Maintains user interactions and vote history
4. **Polls/Banters**: Preserves all content and engagement data
5. **Groups**: Maintains group structure and membership

### Data Integrity

- Foreign key constraints ensure referential integrity
- Check constraints validate data ranges (e.g., ratings 1-5)
- Unique constraints prevent duplicate relationships
- Cascade deletes handle cleanup automatically

## 🚨 Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure RLS policies are correctly applied
2. **Function Errors**: Check that all functions are created successfully
3. **Index Errors**: Verify pg_trgm extension is installed
4. **Seed Data Errors**: Check for UUID conflicts with existing data

### Verification Queries

```sql
-- Check table creation
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' ORDER BY table_name;

-- Check RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables WHERE schemaname = 'public';

-- Check functions
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';

-- Check indexes
SELECT indexname, tablename FROM pg_indexes 
WHERE schemaname = 'public' ORDER BY tablename, indexname;
```

## 📞 Support

If you encounter any issues during setup:

1. Check the Supabase logs for detailed error messages
2. Verify all prerequisites are met
3. Ensure proper permissions on your Supabase instance
4. Review the migration files for any syntax errors

## 🔄 Next Steps

After completing the database setup:

1. Install the Supabase client in your application
2. Configure environment variables
3. Set up the service layer to replace localStorage calls
4. Implement the migration utility for existing users
5. Test the integration with your application

For the complete migration guide, refer to the main project documentation.
