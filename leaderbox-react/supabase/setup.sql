-- LeaderBox Database Setup Script
-- This script runs all migrations in the correct order
-- Execute this file on your Supabase instance to set up the complete database

-- Display setup information
\echo '================================================'
\echo 'LeaderBox Database Setup'
\echo 'Setting up database schema, functions, and data'
\echo '================================================'

-- Check PostgreSQL version
SELECT version();

-- Enable required extensions
\echo 'Enabling required extensions...'
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Run migrations in order
\echo 'Running migration 001: Initial Schema...'
\i 001_initial_schema.sql

\echo 'Running migration 002: Database Functions...'
\i 002_database_functions.sql

\echo 'Running migration 003: Performance Indexes...'
\i 003_performance_indexes.sql

\echo 'Running migration 004: RLS Policies...'
\i 004_rls_policies.sql

\echo 'Running migration 005: Seed Data...'
\i 005_seed_data.sql

-- Verify setup
\echo 'Verifying database setup...'

-- Check tables
\echo 'Tables created:'
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check functions
\echo 'Functions created:'
SELECT routine_name, routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_type = 'FUNCTION'
ORDER BY routine_name;

-- Check RLS status
\echo 'RLS Status:'
SELECT schemaname, tablename, rowsecurity, hasrls
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Check indexes
\echo 'Indexes created:'
SELECT schemaname, tablename, indexname
FROM pg_indexes 
WHERE schemaname = 'public' 
ORDER BY tablename, indexname;

-- Check data counts
\echo 'Data verification:'
SELECT 'users' as table_name, count(*) as record_count FROM users
UNION ALL
SELECT 'leaders', count(*) FROM leaders
UNION ALL
SELECT 'polls', count(*) FROM polls
UNION ALL
SELECT 'banters', count(*) FROM banters
UNION ALL
SELECT 'petitions', count(*) FROM petitions
UNION ALL
SELECT 'groups', count(*) FROM groups
ORDER BY table_name;

-- Performance check
\echo 'Running performance analysis...'
ANALYZE;

-- Final status
\echo '================================================'
\echo 'Database setup completed successfully!'
\echo 'Next steps:'
\echo '1. Configure your application environment variables'
\echo '2. Set up authentication in Supabase dashboard'
\echo '3. Test the connection from your application'
\echo '================================================'
