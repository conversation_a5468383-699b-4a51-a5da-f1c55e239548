# Supabase Configuration for LeaderBox
# This file contains configuration settings for your Supabase project

project_id = "your-project-id"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54324
site_url = "http://localhost:5173"
additional_redirect_urls = ["https://your-production-domain.com"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true
security_captcha_enabled = false

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false
secure_password_change = true

[auth.sms]
enable_signup = false
enable_confirmations = false

[auth.external.apple]
enabled = false

[auth.external.azure]
enabled = false

[auth.external.bitbucket]
enabled = false

[auth.external.discord]
enabled = false

[auth.external.facebook]
enabled = false

[auth.external.github]
enabled = false

[auth.external.gitlab]
enabled = false

[auth.external.google]
enabled = false

[auth.external.keycloak]
enabled = false

[auth.external.linkedin]
enabled = false

[auth.external.notion]
enabled = false

[auth.external.twitch]
enabled = false

[auth.external.twitter]
enabled = false

[auth.external.slack]
enabled = false

[auth.external.spotify]
enabled = false

[auth.external.workos]
enabled = false

[auth.external.zoom]
enabled = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
port = 54323
ip_version = "ipv4"

[storage]
enabled = true
port = 54324
file_size_limit = "50MiB"
image_transformation = true

[inbucket]
enabled = true
port = 54325
smtp_port = 54326
pop3_port = 54327

[functions]
enabled = true
port = 54321

[analytics]
enabled = false
port = 54327
vector_port = 54328
backend = "postgres"

# Custom settings for LeaderBox
[custom]
app_name = "LeaderBox"
version = "1.0.0"
environment = "development"

# Database specific settings
[custom.database]
enable_rls = true
enable_realtime = true
max_connections = 100
statement_timeout = "30s"
idle_in_transaction_session_timeout = "60s"

# Performance settings
[custom.performance]
enable_pg_stat_statements = true
enable_auto_explain = true
log_min_duration_statement = 1000
shared_preload_libraries = ["pg_stat_statements", "auto_explain", "pg_trgm"]

# Security settings
[custom.security]
row_level_security = true
force_ssl = true
log_connections = true
log_disconnections = true
log_checkpoints = true

# Backup settings
[custom.backup]
enabled = true
retention_days = 30
schedule = "0 2 * * *"  # Daily at 2 AM

# Monitoring settings
[custom.monitoring]
log_statement = "all"
log_duration = true
log_lock_waits = true
deadlock_timeout = "1s"

# Extension settings
[custom.extensions]
required = [
  "uuid-ossp",
  "pg_trgm",
  "btree_gin",
  "btree_gist"
]
optional = [
  "pg_stat_statements",
  "auto_explain",
  "pg_buffercache"
]
