-- LeaderBox Performance Indexes
-- Migration: 003_performance_indexes.sql
-- Description: Create indexes for optimal query performance

-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_created_at ON users(created_at DESC);
CREATE INDEX idx_users_is_admin ON users(is_admin);

-- Leaders table indexes
CREATE INDEX idx_leaders_name ON leaders(name);
CREATE INDEX idx_leaders_party ON leaders(party);
CREATE INDEX idx_leaders_state ON leaders(state);
CREATE INDEX idx_leaders_followers_count ON leaders(followers_count DESC);
CREATE INDEX idx_leaders_rating_average ON leaders(rating_average DESC);
CREATE INDEX idx_leaders_created_at ON leaders(created_at DESC);
CREATE INDEX idx_leaders_name_trgm ON leaders USING gin(name gin_trgm_ops);
CREATE INDEX idx_leaders_party_trgm ON leaders USING gin(party gin_trgm_ops);

-- User-Leader relationship indexes
CREATE INDEX idx_user_leader_follows_user_id ON user_leader_follows(user_id);
CREATE INDEX idx_user_leader_follows_leader_id ON user_leader_follows(leader_id);
CREATE INDEX idx_user_leader_follows_created_at ON user_leader_follows(created_at DESC);

CREATE INDEX idx_user_leader_ratings_user_id ON user_leader_ratings(user_id);
CREATE INDEX idx_user_leader_ratings_leader_id ON user_leader_ratings(leader_id);
CREATE INDEX idx_user_leader_ratings_rating ON user_leader_ratings(rating);
CREATE INDEX idx_user_leader_ratings_created_at ON user_leader_ratings(created_at DESC);

-- Leader comments indexes
CREATE INDEX idx_leader_comments_leader_id ON leader_comments(leader_id);
CREATE INDEX idx_leader_comments_user_id ON leader_comments(user_id);
CREATE INDEX idx_leader_comments_created_at ON leader_comments(created_at DESC);
CREATE INDEX idx_leader_comments_upvotes ON leader_comments(upvotes DESC);
CREATE INDEX idx_leader_comments_rating_given ON leader_comments(rating_given);

-- Comment votes indexes
CREATE INDEX idx_comment_votes_user_id ON comment_votes(user_id);
CREATE INDEX idx_comment_votes_comment_id ON comment_votes(comment_id);
CREATE INDEX idx_comment_votes_comment_type ON comment_votes(comment_type);
CREATE INDEX idx_comment_votes_vote_type ON comment_votes(vote_type);

-- Polls indexes
CREATE INDEX idx_polls_creator_id ON polls(creator_id);
CREATE INDEX idx_polls_created_at ON polls(created_at DESC);
CREATE INDEX idx_polls_total_votes ON polls(total_votes DESC);
CREATE INDEX idx_polls_upvotes ON polls(upvotes DESC);
CREATE INDEX idx_polls_topic_trgm ON polls USING gin(topic gin_trgm_ops);

-- Poll options indexes
CREATE INDEX idx_poll_options_poll_id ON poll_options(poll_id);
CREATE INDEX idx_poll_options_votes ON poll_options(votes DESC);
CREATE INDEX idx_poll_options_order ON poll_options(poll_id, option_order);

-- Poll votes indexes
CREATE INDEX idx_poll_votes_user_id ON poll_votes(user_id);
CREATE INDEX idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX idx_poll_votes_created_at ON poll_votes(created_at DESC);

-- Poll comments indexes
CREATE INDEX idx_poll_comments_poll_id ON poll_comments(poll_id);
CREATE INDEX idx_poll_comments_user_id ON poll_comments(user_id);
CREATE INDEX idx_poll_comments_created_at ON poll_comments(created_at DESC);
CREATE INDEX idx_poll_comments_upvotes ON poll_comments(upvotes DESC);

-- Banters indexes
CREATE INDEX idx_banters_author_id ON banters(author_id);
CREATE INDEX idx_banters_created_at ON banters(created_at DESC);
CREATE INDEX idx_banters_upvotes ON banters(upvotes DESC);
CREATE INDEX idx_banters_comments_count ON banters(comments_count DESC);
CREATE INDEX idx_banters_content_trgm ON banters USING gin(content gin_trgm_ops);

-- Banter votes indexes
CREATE INDEX idx_banter_votes_user_id ON banter_votes(user_id);
CREATE INDEX idx_banter_votes_banter_id ON banter_votes(banter_id);
CREATE INDEX idx_banter_votes_vote_type ON banter_votes(vote_type);
CREATE INDEX idx_banter_votes_created_at ON banter_votes(created_at DESC);

-- Banter comments indexes
CREATE INDEX idx_banter_comments_banter_id ON banter_comments(banter_id);
CREATE INDEX idx_banter_comments_user_id ON banter_comments(user_id);
CREATE INDEX idx_banter_comments_created_at ON banter_comments(created_at DESC);
CREATE INDEX idx_banter_comments_upvotes ON banter_comments(upvotes DESC);

-- Petitions indexes
CREATE INDEX idx_petitions_creator_id ON petitions(creator_id);
CREATE INDEX idx_petitions_status ON petitions(status);
CREATE INDEX idx_petitions_signatures ON petitions(signatures DESC);
CREATE INDEX idx_petitions_created_at ON petitions(created_at DESC);
CREATE INDEX idx_petitions_title_trgm ON petitions USING gin(title gin_trgm_ops);

-- Petition signatures indexes
CREATE INDEX idx_petition_signatures_petition_id ON petition_signatures(petition_id);
CREATE INDEX idx_petition_signatures_user_id ON petition_signatures(user_id);
CREATE INDEX idx_petition_signatures_created_at ON petition_signatures(created_at DESC);

-- Groups indexes
CREATE INDEX idx_groups_creator_id ON groups(creator_id);
CREATE INDEX idx_groups_is_public ON groups(is_public);
CREATE INDEX idx_groups_status ON groups(status);
CREATE INDEX idx_groups_created_at ON groups(created_at DESC);
CREATE INDEX idx_groups_name_trgm ON groups USING gin(name gin_trgm_ops);
CREATE INDEX idx_groups_tags ON groups USING gin(tags);

-- Group members indexes
CREATE INDEX idx_group_members_group_id ON group_members(group_id);
CREATE INDEX idx_group_members_user_id ON group_members(user_id);
CREATE INDEX idx_group_members_role ON group_members(role);
CREATE INDEX idx_group_members_joined_at ON group_members(joined_at DESC);

-- Group discussions indexes
CREATE INDEX idx_group_discussions_group_id ON group_discussions(group_id);
CREATE INDEX idx_group_discussions_creator_id ON group_discussions(creator_id);
CREATE INDEX idx_group_discussions_created_at ON group_discussions(created_at DESC);
CREATE INDEX idx_group_discussions_upvotes ON group_discussions(upvotes DESC);
CREATE INDEX idx_group_discussions_title_trgm ON group_discussions USING gin(title gin_trgm_ops);

-- User profile likes indexes
CREATE INDEX idx_user_profile_likes_liker_id ON user_profile_likes(liker_id);
CREATE INDEX idx_user_profile_likes_liked_user_id ON user_profile_likes(liked_user_id);
CREATE INDEX idx_user_profile_likes_created_at ON user_profile_likes(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX idx_leaders_state_party ON leaders(state, party);
CREATE INDEX idx_leaders_rating_followers ON leaders(rating_average DESC, followers_count DESC);
CREATE INDEX idx_polls_creator_created ON polls(creator_id, created_at DESC);
CREATE INDEX idx_banters_author_created ON banters(author_id, created_at DESC);
CREATE INDEX idx_petitions_status_created ON petitions(status, created_at DESC);
CREATE INDEX idx_groups_public_status ON groups(is_public, status);

-- Full-text search indexes (requires pg_trgm extension)
-- Enable the pg_trgm extension for trigram matching
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Additional trigram indexes for better search performance
CREATE INDEX idx_leaders_search ON leaders USING gin((name || ' ' || COALESCE(party, '') || ' ' || COALESCE(position, '')) gin_trgm_ops);
CREATE INDEX idx_polls_search ON polls USING gin((topic || ' ' || COALESCE(description, '')) gin_trgm_ops);
CREATE INDEX idx_groups_search ON groups USING gin((name || ' ' || COALESCE(description, '')) gin_trgm_ops);
CREATE INDEX idx_petitions_search ON petitions USING gin((title || ' ' || COALESCE(description, '')) gin_trgm_ops);

-- Partial indexes for active/visible content
CREATE INDEX idx_users_active ON users(id) WHERE status = 'active';
CREATE INDEX idx_groups_visible ON groups(id) WHERE status = 'visible' AND is_public = true;
CREATE INDEX idx_petitions_active ON petitions(id) WHERE status IN ('pending', 'active');

-- Indexes for real-time subscriptions
CREATE INDEX idx_leader_comments_realtime ON leader_comments(leader_id, created_at DESC);
CREATE INDEX idx_poll_votes_realtime ON poll_votes(poll_id, created_at DESC);
CREATE INDEX idx_banter_votes_realtime ON banter_votes(banter_id, created_at DESC);
CREATE INDEX idx_petition_signatures_realtime ON petition_signatures(petition_id, created_at DESC);

-- Statistics update for better query planning
ANALYZE users;
ANALYZE leaders;
ANALYZE user_leader_follows;
ANALYZE user_leader_ratings;
ANALYZE leader_comments;
ANALYZE polls;
ANALYZE poll_options;
ANALYZE poll_votes;
ANALYZE banters;
ANALYZE petitions;
ANALYZE groups;
