-- LeaderBox Database Functions
-- Migration: 002_database_functions.sql
-- Description: Create PostgreSQL functions for complex operations

-- Function to increment followers count
CREATE OR REPLACE FUNCTION increment_followers(leader_id UUID, increment_by INTEGER)
RETURNS VOID AS $$
BEGIN
  UPDATE leaders 
  SET followers_count = followers_count + increment_by,
      updated_at = NOW()
  WHERE id = leader_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to recalculate leader rating
CREATE OR REPLACE FUNCTION recalculate_leader_rating(leader_id UUID)
RETURNS VOID AS $$
DECLARE
  avg_rating DECIMAL(3,2);
  rating_count INTEGER;
BEGIN
  SELECT AVG(rating), COUNT(*)
  INTO avg_rating, rating_count
  FROM user_leader_ratings
  WHERE leader_id = recalculate_leader_rating.leader_id;
  
  UPDATE leaders
  SET rating_average = COALESCE(avg_rating, 0),
      rating_count = rating_count,
      updated_at = NOW()
  WHERE id = recalculate_leader_rating.leader_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update poll vote counts
CREATE OR REPLACE FUNCTION update_poll_vote_counts(poll_id UUID)
RETURNS VOID AS $$
DECLARE
  total_count INTEGER;
BEGIN
  -- Count total votes for this poll
  SELECT COUNT(*)
  INTO total_count
  FROM poll_votes
  WHERE poll_id = update_poll_vote_counts.poll_id;
  
  -- Update poll total votes
  UPDATE polls
  SET total_votes = total_count,
      updated_at = NOW()
  WHERE id = update_poll_vote_counts.poll_id;
  
  -- Update individual option vote counts
  UPDATE poll_options
  SET votes = (
    SELECT COUNT(*)
    FROM poll_votes
    WHERE poll_votes.option_id = poll_options.id
  )
  WHERE poll_id = update_poll_vote_counts.poll_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update comment vote counts
CREATE OR REPLACE FUNCTION update_comment_votes(comment_id UUID, comment_type TEXT DEFAULT 'leader_comment')
RETURNS VOID AS $$
DECLARE
  upvote_count INTEGER;
  downvote_count INTEGER;
BEGIN
  -- Count upvotes and downvotes
  SELECT 
    COUNT(CASE WHEN vote_type = 'upvote' THEN 1 END),
    COUNT(CASE WHEN vote_type = 'downvote' THEN 1 END)
  INTO upvote_count, downvote_count
  FROM comment_votes
  WHERE comment_votes.comment_id = update_comment_votes.comment_id
    AND comment_votes.comment_type = update_comment_votes.comment_type;
  
  -- Update the appropriate comment table based on type
  IF comment_type = 'leader_comment' THEN
    UPDATE leader_comments
    SET upvotes = upvote_count,
        downvotes = downvote_count,
        updated_at = NOW()
    WHERE id = update_comment_votes.comment_id;
  ELSIF comment_type = 'poll_comment' THEN
    UPDATE poll_comments
    SET upvotes = upvote_count,
        downvotes = downvote_count,
        updated_at = NOW()
    WHERE id = update_comment_votes.comment_id;
  ELSIF comment_type = 'banter_comment' THEN
    UPDATE banter_comments
    SET upvotes = upvote_count,
        downvotes = downvote_count,
        updated_at = NOW()
    WHERE id = update_comment_votes.comment_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update banter vote counts
CREATE OR REPLACE FUNCTION update_banter_votes(banter_id UUID)
RETURNS VOID AS $$
DECLARE
  upvote_count INTEGER;
  downvote_count INTEGER;
BEGIN
  SELECT 
    COUNT(CASE WHEN vote_type = 'upvote' THEN 1 END),
    COUNT(CASE WHEN vote_type = 'downvote' THEN 1 END)
  INTO upvote_count, downvote_count
  FROM banter_votes
  WHERE banter_votes.banter_id = update_banter_votes.banter_id;
  
  UPDATE banters
  SET upvotes = upvote_count,
      downvotes = downvote_count,
      updated_at = NOW()
  WHERE id = update_banter_votes.banter_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update petition signature count
CREATE OR REPLACE FUNCTION update_petition_signatures(petition_id UUID)
RETURNS VOID AS $$
DECLARE
  signature_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO signature_count
  FROM petition_signatures
  WHERE petition_signatures.petition_id = update_petition_signatures.petition_id;
  
  UPDATE petitions
  SET signatures = signature_count,
      updated_at = NOW()
  WHERE id = update_petition_signatures.petition_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user profile likes count
CREATE OR REPLACE FUNCTION update_user_profile_likes(user_id UUID)
RETURNS VOID AS $$
DECLARE
  likes_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO likes_count
  FROM user_profile_likes
  WHERE liked_user_id = update_user_profile_likes.user_id;
  
  UPDATE users
  SET profile_likes_count = likes_count,
      updated_at = NOW()
  WHERE id = update_user_profile_likes.user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update group member count
CREATE OR REPLACE FUNCTION update_group_member_count(group_id UUID)
RETURNS VOID AS $$
DECLARE
  member_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO member_count
  FROM group_members
  WHERE group_members.group_id = update_group_member_count.group_id;
  
  UPDATE groups
  SET updated_at = NOW()
  WHERE id = update_group_member_count.group_id;
  
  -- Also update the creator's group member count
  UPDATE users
  SET group_members_count = (
    SELECT COUNT(*)
    FROM group_members gm
    JOIN groups g ON g.id = gm.group_id
    WHERE g.creator_id = users.id
  ),
  updated_at = NOW()
  WHERE id = (SELECT creator_id FROM groups WHERE id = update_group_member_count.group_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's followed leaders
CREATE OR REPLACE FUNCTION get_user_followed_leaders(user_id UUID)
RETURNS TABLE(leader_id UUID) AS $$
BEGIN
  RETURN QUERY
  SELECT ulf.leader_id
  FROM user_leader_follows ulf
  WHERE ulf.user_id = get_user_followed_leaders.user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user follows leader
CREATE OR REPLACE FUNCTION user_follows_leader(user_id UUID, leader_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS(
    SELECT 1
    FROM user_leader_follows
    WHERE user_leader_follows.user_id = user_follows_leader.user_id
      AND user_leader_follows.leader_id = user_follows_leader.leader_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's rating for a leader
CREATE OR REPLACE FUNCTION get_user_leader_rating(user_id UUID, leader_id UUID)
RETURNS INTEGER AS $$
DECLARE
  user_rating INTEGER;
BEGIN
  SELECT rating
  INTO user_rating
  FROM user_leader_ratings
  WHERE user_leader_ratings.user_id = get_user_leader_rating.user_id
    AND user_leader_ratings.leader_id = get_user_leader_rating.leader_id;
  
  RETURN user_rating;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's vote on a poll
CREATE OR REPLACE FUNCTION get_user_poll_vote(user_id UUID, poll_id UUID)
RETURNS UUID AS $$
DECLARE
  option_id UUID;
BEGIN
  SELECT pv.option_id
  INTO option_id
  FROM poll_votes pv
  WHERE pv.user_id = get_user_poll_vote.user_id
    AND pv.poll_id = get_user_poll_vote.poll_id;
  
  RETURN option_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search leaders by name or party
CREATE OR REPLACE FUNCTION search_leaders(search_term TEXT)
RETURNS TABLE(
  id UUID,
  name TEXT,
  position TEXT,
  party TEXT,
  state TEXT,
  bio TEXT,
  avatar_url TEXT,
  followers_count INTEGER,
  rating_average DECIMAL(3,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    l.id,
    l.name,
    l.position,
    l.party,
    l.state,
    l.bio,
    l.avatar_url,
    l.followers_count,
    l.rating_average
  FROM leaders l
  WHERE 
    l.name ILIKE '%' || search_term || '%'
    OR l.party ILIKE '%' || search_term || '%'
    OR l.position ILIKE '%' || search_term || '%'
    OR l.state ILIKE '%' || search_term || '%'
  ORDER BY l.followers_count DESC, l.rating_average DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
