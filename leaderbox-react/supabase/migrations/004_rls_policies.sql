-- LeaderBox Row Level Security Policies
-- Migration: 004_rls_policies.sql
-- Description: Configure RLS policies for data protection and user access control

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaders ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_leader_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_leader_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE leader_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE banters ENABLE ROW LEVEL SECURITY;
ALTER TABLE banter_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE banter_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE petitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE petition_signatures ENABLE ROW LEVEL SECURITY;
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_discussions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profile_likes ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Anyone can view active user profiles" ON users
  FOR SELECT USING (status = 'active');

CREATE POLICY "Admins can view all users" ON users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Leaders table policies
CREATE POLICY "Anyone can view leaders" ON leaders
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage leaders" ON leaders
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- User-Leader follows policies
CREATE POLICY "Users can view all follows" ON user_leader_follows
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own follows" ON user_leader_follows
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own follows" ON user_leader_follows
  FOR DELETE USING (auth.uid() = user_id);

-- User-Leader ratings policies
CREATE POLICY "Users can view all ratings" ON user_leader_ratings
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own ratings" ON user_leader_ratings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own ratings" ON user_leader_ratings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own ratings" ON user_leader_ratings
  FOR DELETE USING (auth.uid() = user_id);

-- Leader comments policies
CREATE POLICY "Anyone can view leader comments" ON leader_comments
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can add comments" ON leader_comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" ON leader_comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON leader_comments
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all comments" ON leader_comments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Comment votes policies
CREATE POLICY "Users can view all comment votes" ON comment_votes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own comment votes" ON comment_votes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comment votes" ON comment_votes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comment votes" ON comment_votes
  FOR DELETE USING (auth.uid() = user_id);

-- Polls policies
CREATE POLICY "Anyone can view polls" ON polls
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create polls" ON polls
  FOR INSERT WITH CHECK (auth.uid() = creator_id);

CREATE POLICY "Users can update their own polls" ON polls
  FOR UPDATE USING (auth.uid() = creator_id);

CREATE POLICY "Admins can manage all polls" ON polls
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Poll options policies
CREATE POLICY "Anyone can view poll options" ON poll_options
  FOR SELECT USING (true);

CREATE POLICY "Poll creators can manage options" ON poll_options
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM polls 
      WHERE polls.id = poll_id AND polls.creator_id = auth.uid()
    )
  );

CREATE POLICY "Admins can manage all poll options" ON poll_options
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Poll votes policies
CREATE POLICY "Users can view poll votes" ON poll_votes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own poll votes" ON poll_votes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own poll votes" ON poll_votes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own poll votes" ON poll_votes
  FOR DELETE USING (auth.uid() = user_id);

-- Poll comments policies
CREATE POLICY "Anyone can view poll comments" ON poll_comments
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can add poll comments" ON poll_comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own poll comments" ON poll_comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own poll comments" ON poll_comments
  FOR DELETE USING (auth.uid() = user_id);

-- Banters policies
CREATE POLICY "Anyone can view banters" ON banters
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create banters" ON banters
  FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Users can update their own banters" ON banters
  FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Users can delete their own banters" ON banters
  FOR DELETE USING (auth.uid() = author_id);

CREATE POLICY "Admins can manage all banters" ON banters
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Banter votes policies
CREATE POLICY "Users can view banter votes" ON banter_votes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own banter votes" ON banter_votes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own banter votes" ON banter_votes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own banter votes" ON banter_votes
  FOR DELETE USING (auth.uid() = user_id);

-- Banter comments policies
CREATE POLICY "Anyone can view banter comments" ON banter_comments
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can add banter comments" ON banter_comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own banter comments" ON banter_comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own banter comments" ON banter_comments
  FOR DELETE USING (auth.uid() = user_id);

-- Petitions policies
CREATE POLICY "Anyone can view petitions" ON petitions
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create petitions" ON petitions
  FOR INSERT WITH CHECK (auth.uid() = creator_id);

CREATE POLICY "Users can update their own petitions" ON petitions
  FOR UPDATE USING (auth.uid() = creator_id);

CREATE POLICY "Admins can manage all petitions" ON petitions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Petition signatures policies
CREATE POLICY "Anyone can view petition signatures" ON petition_signatures
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own signatures" ON petition_signatures
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own signatures" ON petition_signatures
  FOR DELETE USING (auth.uid() = user_id);

-- Groups policies
CREATE POLICY "Anyone can view public groups" ON groups
  FOR SELECT USING (is_public = true AND status = 'visible');

CREATE POLICY "Group members can view their groups" ON groups
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members 
      WHERE group_id = groups.id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Authenticated users can create groups" ON groups
  FOR INSERT WITH CHECK (auth.uid() = creator_id);

CREATE POLICY "Group creators can update their groups" ON groups
  FOR UPDATE USING (auth.uid() = creator_id);

CREATE POLICY "Admins can manage all groups" ON groups
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Group members policies
CREATE POLICY "Anyone can view public group members" ON group_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM groups 
      WHERE groups.id = group_id AND is_public = true
    )
  );

CREATE POLICY "Group members can view their group members" ON group_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm2
      WHERE gm2.group_id = group_members.group_id AND gm2.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can join groups" ON group_members
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave groups" ON group_members
  FOR DELETE USING (auth.uid() = user_id);

-- Group discussions policies
CREATE POLICY "Group members can view discussions" ON group_discussions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members 
      WHERE group_id = group_discussions.group_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Group members can create discussions" ON group_discussions
  FOR INSERT WITH CHECK (
    auth.uid() = creator_id AND
    EXISTS (
      SELECT 1 FROM group_members 
      WHERE group_id = group_discussions.group_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own discussions" ON group_discussions
  FOR UPDATE USING (auth.uid() = creator_id);

-- User profile likes policies
CREATE POLICY "Users can view profile likes" ON user_profile_likes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own likes" ON user_profile_likes
  FOR INSERT WITH CHECK (auth.uid() = liker_id);

CREATE POLICY "Users can delete their own likes" ON user_profile_likes
  FOR DELETE USING (auth.uid() = liker_id);
