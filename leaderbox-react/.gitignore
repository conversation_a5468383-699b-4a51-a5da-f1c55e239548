# =========================================================================
# .gitignore for a React + Express Application
# =========================================================================

# -------------------------------------------------------------------------
# SECTION 1: Dependencies & Runtime Files
# -------------------------------------------------------------------------
# These are generated by package managers and are not part of the source code.
# They can be re-installed by running `npm install` or `yarn install`.
/node_modules/
# Note: pnpm-lock.yaml should be committed for reproducible builds

# Log files generated by npm or yarn
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# -------------------------------------------------------------------------
# SECTION 2: Build Output & Compiled Files
# -------------------------------------------------------------------------
# These folders contain the compiled, optimized code for production.
# They are generated from your source code, so they should not be in Git.

# For React (Create React App, Vite, etc.)
/build/
/dist/
/.vite/

# For backend TypeScript builds
# (Uncomment if you are using TypeScript on the backend)
# /dist/

# -------------------------------------------------------------------------
# SECTION 3: Environment Variables & Secrets (VERY IMPORTANT)
# -------------------------------------------------------------------------
# NEVER commit secret keys, API tokens, or passwords.
# Use a .env.example file to show what variables are needed.
.env
.env.local
.env.development
.env.production
.env.*.local

# -------------------------------------------------------------------------
# SECTION 4: Logs & Temporary Files
# -------------------------------------------------------------------------
# Custom application logs and other temporary files.
*.log
*.log.*
*.pid
*.seed
*.pid.lock

# -------------------------------------------------------------------------
# SECTION 5: Testing & Code Coverage
# -------------------------------------------------------------------------
# Reports generated by testing frameworks like Jest or Vitest.
/coverage/
.nyc_output/

# -------------------------------------------------------------------------
# SECTION 6: IDE & Editor Configuration
# -------------------------------------------------------------------------
# User-specific settings for code editors. It's better for each developer
# to manage their own editor configuration.

# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains (WebStorm, IntelliJ)
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# -------------------------------------------------------------------------
# SECTION 7: OS-specific Files
# -------------------------------------------------------------------------
# Files generated by the operating system that have no place in a repo.

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
/src/generated/prisma
/leaderbox-sveltekit/