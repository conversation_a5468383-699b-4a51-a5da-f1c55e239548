<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeaderBox Database Migration Helper</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a202c;
            border-bottom: 3px solid #4299e1;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .status.success { background: #f0fff4; border-left: 4px solid #38a169; }
        .status.warning { background: #fffbf0; border-left: 4px solid #ed8936; }
        .status.error { background: #fff5f5; border-left: 4px solid #e53e3e; }
        .migration-step {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
        }
        .migration-step h3 {
            margin-top: 0;
            color: #2d3748;
        }
        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .button:hover { background: #3182ce; }
        .button.success { background: #38a169; }
        .button.warning { background: #ed8936; }
        .instructions {
            background: #ebf8ff;
            border: 1px solid #bee3f8;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .file-path {
            background: #edf2f7;
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LeaderBox Database Migration Helper</h1>
        
        <div class="status success">
            <strong>✅ Supabase Instance Status:</strong> Your instance at <code>https://supabase.linkfa.de</code> is accessible and ready!
        </div>

        <div class="status warning">
            <strong>⚠️ Database Status:</strong> Tables not created yet. Please execute the migrations below.
        </div>

        <div class="instructions">
            <h3>📋 Quick Setup Instructions</h3>
            <ol>
                <li>Open your <strong>Supabase Dashboard</strong> at <a href="https://supabase.linkfa.de" target="_blank">https://supabase.linkfa.de</a></li>
                <li>Navigate to <strong>"SQL Editor"</strong> in the left sidebar</li>
                <li>Copy and paste each migration below <strong>in order</strong></li>
                <li>Click <strong>"Run"</strong> after pasting each migration</li>
            </ol>
        </div>

        <div class="migration-step">
            <h3>Step 1: Initial Schema</h3>
            <p>Creates all core tables, relationships, and triggers.</p>
            <div class="file-path">📄 supabase/migrations/001_initial_schema.sql</div>
            <button class="button" onclick="copyToClipboard('migration1')">📋 Copy Migration 1</button>
            <button class="button" onclick="openFile('supabase/migrations/001_initial_schema.sql')">📂 Open File</button>
        </div>

        <div class="migration-step">
            <h3>Step 2: Database Functions</h3>
            <p>Adds custom PostgreSQL functions for business logic.</p>
            <div class="file-path">📄 supabase/migrations/002_database_functions.sql</div>
            <button class="button" onclick="copyToClipboard('migration2')">📋 Copy Migration 2</button>
            <button class="button" onclick="openFile('supabase/migrations/002_database_functions.sql')">📂 Open File</button>
        </div>

        <div class="migration-step">
            <h3>Step 3: Performance Indexes</h3>
            <p>Creates indexes for optimal query performance.</p>
            <div class="file-path">📄 supabase/migrations/003_performance_indexes.sql</div>
            <button class="button" onclick="copyToClipboard('migration3')">📋 Copy Migration 3</button>
            <button class="button" onclick="openFile('supabase/migrations/003_performance_indexes.sql')">📂 Open File</button>
        </div>

        <div class="migration-step">
            <h3>Step 4: Row Level Security</h3>
            <p>Sets up authentication and authorization policies.</p>
            <div class="file-path">📄 supabase/migrations/004_rls_policies.sql</div>
            <button class="button" onclick="copyToClipboard('migration4')">📋 Copy Migration 4</button>
            <button class="button" onclick="openFile('supabase/migrations/004_rls_policies.sql')">📂 Open File</button>
        </div>

        <div class="migration-step">
            <h3>Step 5: Seed Data (Optional)</h3>
            <p>Adds sample data for testing and development.</p>
            <div class="file-path">📄 supabase/migrations/005_seed_data.sql</div>
            <button class="button warning" onclick="copyToClipboard('migration5')">📋 Copy Migration 5</button>
            <button class="button" onclick="openFile('supabase/migrations/005_seed_data.sql')">📂 Open File</button>
        </div>

        <div class="instructions">
            <h3>🔧 Your Configuration</h3>
            <div class="code-block">
SUPABASE_URL=https://supabase.linkfa.de
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNjcyNTMxMjAwLCJleHAiOjE5ODgxODg4MDB9.nHnlkKkZDGIc6_UgY6C3tmQc1Mpls2lU07lxShTbFuE
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE2NzI1MzEyMDAsImV4cCI6MTk4ODE4ODgwMH0.fIs4Oqe-Kd4s2tQZhCdbBlNv12y2Tq9BlSI3GMe3ZVs
            </div>
        </div>

        <div class="status success">
            <strong>🎉 After Setup:</strong> Your database will have users, leaders, polls, banters, petitions, groups, and all necessary relationships ready for your LeaderBox application!
        </div>
    </div>

    <script>
        function copyToClipboard(migrationId) {
            // This would copy the migration content to clipboard
            // For now, it just shows an alert
            alert('Please open the migration file and copy its contents to your Supabase SQL Editor');
        }

        function openFile(filePath) {
            alert('Please open the file: ' + filePath + ' in your code editor and copy its contents to the Supabase SQL Editor');
        }
    </script>
</body>
</html>
