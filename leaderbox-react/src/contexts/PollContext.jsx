
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext.jsx';

const PollContext = createContext(null);

export const usePollData = () => {
  const context = useContext(PollContext);
  if (!context) {
    throw new Error('usePollData must be used within a PollProvider');
  }
  return context;
};

const initialPollsData = [
  { 
    id: 'poll1', 
    topic: 'Should Nigeria invest more in renewable energy or fossil fuels for immediate energy needs?', 
    description: "Considering Nigeria's current energy crisis and long-term environmental goals, what should be the primary focus for investment in the energy sector over the next 5 years? Renewable sources offer sustainability but may require significant upfront costs and infrastructure. Fossil fuels are established but have environmental drawbacks.",
    options: [
      { id: 'opt1a', text: 'Renewable Energy', votes: 75 },
      { id: 'opt1b', text: 'Fossil Fuels', votes: 40 },
      { id: 'opt1c', text: 'A Balanced Mix', votes: 150 }
    ],
    source: 'Admin', 
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), 
    totalVotes: 265, 
    upvotes: 25,
    downvotes: 3,
    commentsCount: 12,
    userVotes: {}, 
    comments: [
      { commentId: 'pc1', userId: 'userDemo123', userName: 'EcoWarrior', userAvatarUrl: 'https://avatar.vercel.sh/ecowarrior.png?size=32', text: 'Renewables are the future! We need to think long term.', timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), upvotes: 10, downvotes: 1, replies: [], media: [] },
      { commentId: 'pc2', userId: 'user789', userName: 'Pragmatist', userAvatarUrl: 'https://avatar.vercel.sh/pragmatist.png?size=32', text: 'We need power NOW. Fossil fuels can bridge the gap while we build renewables.', timestamp: new Date(Date.now() - 22 * 60 * 60 * 1000).toISOString(), upvotes: 5, downvotes: 0, replies: [], media: [] },
    ],
    tags: ['energy', 'policy', 'environment'],
    media: []
  },
  { 
    id: 'poll2', 
    topic: 'What is the most critical area for educational reform in Nigeria?', 
    description: "Nigeria's education system faces numerous challenges. If you could prioritize one area for immediate and significant reform, which would it be? Consider teacher training, curriculum development, infrastructure, or funding.",
    options: [
      { id: 'opt2a', text: 'Teacher Training & Welfare', votes: 120 },
      { id: 'opt2b', text: 'Curriculum Modernization', votes: 90 },
      { id: 'opt2c', text: 'Infrastructure Development', votes: 65 },
      { id: 'opt2d', text: 'Increased Funding', votes: 150 }
    ],
    source: 'ConcernedCitizen', 
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), 
    totalVotes: 425, 
    upvotes: 42,
    downvotes: 5,
    commentsCount: 25,
    userVotes: {},
    comments: [],
    tags: ['education', 'reform', 'development'],
    media: []
  },
  { 
    id: 'poll3', 
    topic: 'Effectiveness of current security measures in urban areas?', 
    description: "How effective do you find the current security strategies (e.g., police presence, community policing initiatives, technological surveillance) in ensuring safety and reducing crime in Nigerian cities?",
    options: [
      { id: 'opt3a', text: 'Very Effective', votes: 30 },
      { id: 'opt3b', text: 'Somewhat Effective', votes: 110 },
      { id: 'opt3c', text: 'Not Effective', votes: 95 },
      { id: 'opt3d', text: 'Needs Major Overhaul', votes: 180 }
    ],
    source: 'SecurityAnalyst', 
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), 
    totalVotes: 415, 
    upvotes: 18,
    downvotes: 1,
    commentsCount: 18,
    userVotes: {},
    comments: [],
    tags: ['security', 'urban', 'policing', 'crime'],
    media: []
  }
];


export const PollProvider = ({ children }) => {
  const { user } = useAuth();
  const [polls, setPolls] = useState([]);
  const [userPollItemVotes, setUserPollItemVotes] = useState({});

  useEffect(() => {
    const localPolls = localStorage.getItem('polls');
    const storedPolls = localPolls ? JSON.parse(localPolls) : initialPollsData;
    const pollsWithDefaults = storedPolls.map(p => ({
      ...p,
      upvotes: p.upvotes || 0,
      downvotes: p.downvotes || 0,
      media: p.media || [],
      comments: (p.comments || []).map(c => ({
        ...c,
        upvotes: c.upvotes || 0,
        downvotes: c.downvotes || 0,
        media: c.media || []
      }))
    }));
    setPolls(pollsWithDefaults);

    const storedVotes = localStorage.getItem('userPollItemVotes');
    if (storedVotes) {
      setUserPollItemVotes(JSON.parse(storedVotes));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('polls', JSON.stringify(polls));
  }, [polls]);

  useEffect(() => {
    localStorage.setItem('userPollItemVotes', JSON.stringify(userPollItemVotes));
  }, [userPollItemVotes]);

  const createPoll = (pollData) => {
    if (!user) return null;
    const newPoll = {
      id: `poll${Date.now()}`,
      ...pollData,
      options: pollData.options.map((optText, index) => ({ id: `opt${Date.now()}${index}`, text: optText, votes: 0 })),
      source: user.name || user.email.split('@')[0],
      createdAt: new Date().toISOString(),
      totalVotes: 0,
      upvotes: 0,
      downvotes: 0,
      commentsCount: 0,
      userVotes: {},
      comments: [],
      media: pollData.media || [],
    };
    setPolls(prevPolls => [newPoll, ...prevPolls]);
    return newPoll;
  };

  const voteOnPoll = (pollId, optionId, commentText) => {
    if (!user) return;

    setPolls(prevPolls => prevPolls.map(p => {
      if (p.id === pollId) {
        if (p.userVotes && p.userVotes[user.id]) return p; 

        const updatedOptions = p.options.map(opt => 
          opt.id === optionId ? { ...opt, votes: opt.votes + 1 } : opt
        );
        const updatedUserVotes = { ...p.userVotes, [user.id]: optionId };
        const newTotalVotes = p.totalVotes + 1;
        
        let updatedComments = p.comments || [];
        let newCommentsCount = p.commentsCount || 0;

        if (commentText && commentText.trim() !== "") {
          const newComment = {
            commentId: `pcmt-${Date.now()}`,
            userId: user.id,
            userName: user.name || user.email.split('@')[0],
            userAvatarUrl: user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`,
            text: commentText,
            timestamp: new Date().toISOString(),
            upvotes: 0,
            downvotes: 0,
            replies: [],
            media: []
          };
          updatedComments = [...updatedComments, newComment];
          newCommentsCount +=1;
        }
        
        return { 
          ...p, 
          options: updatedOptions, 
          userVotes: updatedUserVotes, 
          totalVotes: newTotalVotes,
          comments: updatedComments,
          commentsCount: newCommentsCount,
          hasVoted: true, 
          selectedOptionId: optionId 
        };
      }
      return p;
    }));
  };

  const addCommentToPoll = (pollId, text, media = []) => {
    if (!user) return;
    setPolls(prevPolls => prevPolls.map(p => {
      if (p.id === pollId) {
        const newComment = {
          commentId: `pcmt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          userId: user.id,
          userName: user.name || user.email.split('@')[0],
          userAvatarUrl: user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`,
          text,
          timestamp: new Date().toISOString(),
          upvotes: 0,
          downvotes: 0,
          replies: [],
          media: media || []
        };
        return { 
          ...p, 
          comments: [...(p.comments || []), newComment],
          commentsCount: (p.commentsCount || 0) + 1
        };
      }
      return p;
    }));
  };

  const addReplyToPollComment = (pollId, parentCommentId, text, media = []) => {
     if (!user) return;
     setPolls(prevPolls => prevPolls.map(p => {
      if (p.id === pollId) {
        const updatedComments = (p.comments || []).map(comment => {
          if (comment.commentId === parentCommentId) {
            const newReply = {
              replyId: `prep-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              userId: user.id,
              userName: user.name || user.email.split('@')[0],
              userAvatarUrl: user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`,
              text,
              timestamp: new Date().toISOString(),
              upvotes: 0,
              downvotes: 0,
              media: media || []
            };
            return { ...comment, replies: [...(comment.replies || []), newReply] };
          }
          return comment;
        });
        return { ...p, comments: updatedComments };
      }
      return p;
    }));
  };

  const voteOnPollItem = (pollId, itemId, voteType, itemType = 'poll') => {
    if (!user) return;
    const voteKey = `${itemType}-${itemId}-${user.id}`;
    const currentVote = userPollItemVotes[voteKey];

    let newUpvotesChange = 0;
    let newDownvotesChange = 0;
    let newUserVoteStatus = undefined;

    if (voteType === 'upvote') {
      if (currentVote === 'upvote') {
        newUpvotesChange = -1;
        newUserVoteStatus = undefined;
      } else {
        newUpvotesChange = 1;
        if (currentVote === 'downvote') newDownvotesChange = -1;
        newUserVoteStatus = 'upvote';
      }
    } else if (voteType === 'downvote') {
      if (currentVote === 'downvote') {
        newDownvotesChange = -1;
        newUserVoteStatus = undefined;
      } else {
        newDownvotesChange = 1;
        if (currentVote === 'upvote') newUpvotesChange = -1;
        newUserVoteStatus = 'downvote';
      }
    }

    setUserPollItemVotes(prev => ({ ...prev, [voteKey]: newUserVoteStatus }));

    setPolls(prevPolls => {
      return prevPolls.map(p => {
        if (p.id !== pollId) return p;

        if (itemType === 'poll' && p.id === itemId) {
          return { ...p, upvotes: (p.upvotes || 0) + newUpvotesChange, downvotes: (p.downvotes || 0) + newDownvotesChange };
        } else if (itemType === 'comment') {
          const updatedComments = (p.comments || []).map(c => {
            if (c.commentId === itemId) {
              return { ...c, upvotes: (c.upvotes || 0) + newUpvotesChange, downvotes: (c.downvotes || 0) + newDownvotesChange };
            }
            return c;
          });
          return { ...p, comments: updatedComments };
        }
        return p;
      });
    });
  };
  
  const value = {
    polls,
    user, 
    createPoll,
    voteOnPoll,
    addCommentToPoll,
    addReplyToPollComment,
    voteOnPollItem,
    userPollItemVotes,
  };

  return <PollContext.Provider value={value}>{children}</PollContext.Provider>;
};
