
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext.jsx';

const GroupContext = createContext(null);

export const useGroups = () => {
  const context = useContext(GroupContext);
  if (!context) {
    throw new Error('useGroups must be used within a GroupProvider');
  }
  return context;
};

const initialGroups = [
  {
    id: 'group1',
    name: 'Youths for Better Governance',
    description: 'A group dedicated to promoting youth involvement in politics and advocating for good governance in Nigeria.',
    coverImage: 'https://images.unsplash.com/photo-*************-2a4b3d7b88e1?q=80&w=800',
    creatorId: 'userDemo123',
    creatorName: '<PERSON><PERSON>',
    members: ['userDemo123', 'adminDemo456', 'editorDemo789', 'creatorDemo101', 'user1', 'user2', 'user3', 'user4', 'user5', 'user6', 'user7', 'user8'],
    isPublic: true,
    tags: {
      leader: '<PERSON>',
      location: 'Lagos',
      party: 'LP',
      keywords: ['youth', 'governance', 'accountability']
    },
    discussions: [
      { id: 'd1', title: 'How can we increase youth voter turnout in 2027?', content: 'With the last election showing a significant youth interest, what are the practical steps, policies, or tech solutions we can advocate for to ensure even more young people are not just registered, but also come out to vote? Let\'s brainstorm ideas from voter education to community mobilization.', authorId: 'userDemo123', authorName: 'Aisha Bello', authorAvatarUrl: 'https://avatar.vercel.sh/demouser.png?size=40', timestamp: new Date(Date.now() - ********).toISOString(), replies: [{ id: 'r1', authorId: 'adminDemo456', authorName: 'Admin User', authorAvatarUrl: 'https://avatar.vercel.sh/adminuser.png?size=40', text: 'We need to leverage social media more effectively for education, not just campaigns.', timestamp: new Date(Date.now() - ********).toISOString() }], media: [] },
      { id: 'd2', title: 'Discussing the new electoral reform bill', content: 'A new electoral reform bill has been proposed. What are the key clauses we should be paying attention to? Has anyone read through it? Looking for legal minds and policy analysts to break it down for the community.', authorId: 'editorDemo789', authorName: 'Editor User', authorAvatarUrl: 'https://avatar.vercel.sh/editoruser.png?size=40', timestamp: new Date(Date.now() - 2*********).toISOString(), replies: [], media: [{name: "document.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-*************-2e1b58d053ad?w=500"}] },
    ],
    createdAt: new Date(Date.now() - 5 * ********).toISOString(),
    status: 'visible',
  },
  {
    id: 'group2',
    name: 'Rivers State Development Watch',
    description: 'Private group for stakeholders in Rivers State to discuss local development projects and hold leaders accountable.',
    coverImage: 'https://images.unsplash.com/photo-*************-d81bb19240f5?q=80&w=800',
    creatorId: 'creatorDemo101',
    creatorName: 'David Wari',
    members: ['creatorDemo101', 'userDemo123'],
    isPublic: false,
    tags: {
      leader: 'Nyesom Wike',
      location: 'Rivers',
      party: 'PDP',
      keywords: ['development', 'infrastructure']
    },
    discussions: [],
    createdAt: new Date(Date.now() - 2 * ********).toISOString(),
    status: 'pending_members',
  },
  {
    id: 'group3',
    name: 'Kaduna Tech & Innovation Hub',
    description: 'Fostering tech growth and innovation in Kaduna State. Connecting entrepreneurs, developers, and investors.',
    coverImage: 'https://images.unsplash.com/photo-**********-90de374c12ad?q=80&w=800',
    creatorId: 'adminDemo456',
    creatorName: 'Fatima Yusuf',
    members: ['adminDemo456', 'userDemo123', 'editorDemo789'],
    isPublic: true,
    tags: {
      leader: 'Nasir El-Rufai',
      location: 'Kaduna',
      party: '',
      keywords: ['technology', 'startups', 'innovation']
    },
    discussions: [
       { id: 'd3', title: 'Pitch your startup idea!', content: 'We have a virtual pitch day coming up. Drop your startup ideas here, get feedback from the community, and the best ideas will be invited to pitch to a panel of investors. Keep it concise and clear!', authorId: 'adminDemo456', authorName: 'Fatima Yusuf', authorAvatarUrl: 'https://avatar.vercel.sh/adminuser.png?size=40', timestamp: new Date(Date.now() - 3*********).toISOString(), replies: [], media: [] },
    ],
    createdAt: new Date(Date.now() - 10 * ********).toISOString(),
    status: 'visible',
  },
  {
    id: 'group4',
    name: 'Lagos Traffic Solutions Forum',
    description: 'A space for Lagosians to propose and discuss practical solutions to the perennial traffic problem in the city.',
    coverImage: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?q=80&w=800',
    creatorId: 'user1',
    creatorName: 'Tunde Adebayo',
    members: ['user1', 'user2', 'user3', 'user4', 'user5', 'user6', 'user7', 'user8', 'user9', 'user10', 'user11'],
    isPublic: true,
    tags: {
      leader: 'Babajide Sanwo-Olu',
      location: 'Lagos',
      party: 'APC',
      keywords: ['traffic', 'transport', 'LASTMA']
    },
    discussions: [],
    createdAt: new Date(Date.now() - 1 * ********).toISOString(),
    status: 'visible'
  }
];

export const GroupProvider = ({ children }) => {
  const { user } = useAuth();
  const [groups, setGroups] = useState([]);

  useEffect(() => {
    const storedGroups = localStorage.getItem('leaderboxGroups');
    if (storedGroups) {
      setGroups(JSON.parse(storedGroups));
    } else {
      setGroups(initialGroups);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('leaderboxGroups', JSON.stringify(groups));
  }, [groups]);

  const createGroup = (groupData) => {
    if (!user) return null;
    const newGroup = {
      id: `group${Date.now()}`,
      ...groupData,
      creatorId: user.id,
      creatorName: user.name || user.email.split('@')[0],
      members: [user.id],
      discussions: [],
      createdAt: new Date().toISOString(),
      status: 'pending_members',
    };
    
    const tempMembers = Array.from({length: 10}, (_, i) => `invitedUser${i+1}`);
    newGroup.members.push(...tempMembers);

    if (newGroup.members.length >= 10) {
      newGroup.status = 'visible';
    }

    setGroups(prev => [newGroup, ...prev]);
    return newGroup;
  };

  const getGroupById = (groupId) => {
    return groups.find(g => g.id === groupId);
  };
  
  const getGroupsForUser = (userId) => {
     return groups.filter(g => g.members.includes(userId));
  };
  
  const startNewDiscussion = (groupId, discussionData) => {
    if(!user) return null;

    const newDiscussion = {
      id: `d${Date.now()}`,
      authorId: user.id,
      authorName: user.name || user.email.split('@')[0],
      authorAvatarUrl: user.avatarUrl,
      timestamp: new Date().toISOString(),
      replies: [],
      ...discussionData
    };
    
    setGroups(prevGroups => prevGroups.map(group => {
      if (group.id === groupId) {
        return { ...group, discussions: [newDiscussion, ...(group.discussions || [])]};
      }
      return group;
    }));
    
    return newDiscussion;
  };

  const addReplyToDiscussion = (groupId, discussionId, replyText) => {
    if (!user) return null;

    const newReply = {
      id: `r${Date.now()}`,
      authorId: user.id,
      authorName: user.name || user.email.split('@')[0],
      authorAvatarUrl: user.avatarUrl,
      text: replyText,
      timestamp: new Date().toISOString(),
    };

    setGroups(prevGroups => prevGroups.map(group => {
      if (group.id === groupId) {
        const updatedDiscussions = group.discussions.map(discussion => {
          if (discussion.id === discussionId) {
            return { ...discussion, replies: [...(discussion.replies || []), newReply] };
          }
          return discussion;
        });
        return { ...group, discussions: updatedDiscussions };
      }
      return group;
    }));
  };

  const value = {
    groups,
    createGroup,
    getGroupById,
    getGroupsForUser,
    startNewDiscussion,
    addReplyToDiscussion,
  };

  return <GroupContext.Provider value={value}>{children}</GroupContext.Provider>;
};
