
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext.jsx';

const BanterContext = createContext(null);

export const useBanter = () => {
  const context = useContext(BanterContext);
  if (!context) {
    throw new Error('useBanter must be used within a BanterProvider');
  }
  return context;
};

const initialBanterRoomPostsData = [
  {id: 'b1', authorId: 'userDemo123', authorName: 'PoliticalGuru', authorAvatarUrl: 'https://avatar.vercel.sh/politicalguru.png?size=40', title: 'Fuel Subsidy Removal: Necessary Evil or Policy Blunder?', details: 'The recent removal of fuel subsidy has sparked widespread debate. Supporters argue it frees up funds for critical sectors, while critics point to the immediate hardship on citizens. What are the long-term implications for Nigeria\'s economy and its people? Is there a middle ground?', createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), upvotes: 15, downvotes: 2, comments: [{commentId:'bc1', userId:'user789', userName:'AnalystAnna', text:'The timing is terrible, but it was inevitable. The palliative measures need to be swift and effective.', timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), upvotes: 5, downvotes: 0, userAvatarUrl: 'https://avatar.vercel.sh/analystanna.png?size=32', media: [{name: "report.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-*************-7f9fcf423740?w=500"}] }], tags: {topic: 'Economy', location: 'Nationwide'}, sourceLink: 'https://www.channelstv.com/tag/fuel-subsidy/', media: []},
  {id: 'b2', authorId: 'user456', authorName: 'CitizenVoice', authorAvatarUrl: 'https://avatar.vercel.sh/citizenvoice.png?size=40', title: 'State Police: A Solution to Insecurity or Recipe for Disaster?', details: 'The call for state-level police forces is growing louder. Proponents believe it will improve local security and accountability. Opponents fear potential abuse by state governors and a rise in ethnic tensions. Can Nigeria implement this effectively?', createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), upvotes: 22, downvotes: 1, comments: [], tags: {topic: 'Security', party: 'All Parties'}, sourceLink: '', media: [{name: "protest.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-*************-9df7fd2f337e?w=500"}, {name: "discussion.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-*************-66273c2fd55f?w=500"}]},
  {id: 'b3', authorId: 'user789', authorName: 'TechInnovator', authorAvatarUrl: 'https://avatar.vercel.sh/techinnovator.png?size=40', title: 'Digital Voting: Can Nigeria Overcome Trust and Infrastructure Hurdles?', details: 'With advancements in technology, digital voting seems like a logical step for future elections. However, concerns about cybersecurity, data privacy, and reliable infrastructure (especially in rural areas) are significant. What needs to happen for Nigerians to trust a fully digital electoral process?', createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), upvotes: 18, downvotes: 0, comments: [], tags: {topic: 'Elections', leader: 'INEC Chairman'}, sourceLink: '', media: [{name: "server_room.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-1580894894513-541e068a3e2b?w=500"}, {name: "voting_booth.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-1590846426793-2a4b3d7b88e1?w=500"}, {name: "nigerian_flag.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-1587803123293-34e8f75a7112?w=500"}]},
  {id: 'b4', authorId: 'user101', authorName: 'EcoWarrior', authorAvatarUrl: 'https://avatar.vercel.sh/ecowarrior.png?size=40', title: 'The Deforestation Crisis in the South-West: Who is to Blame?', details: 'Large swathes of forests in the South-Western states are disappearing at an alarming rate due to illegal logging and agricultural expansion. What are the roles of government agencies, local communities, and businesses in this environmental disaster? Are current policies effective?', createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), upvotes: 25, downvotes: 3, comments: [], tags: {topic: 'Environment', location: 'Ogun State'}, sourceLink: '', media: [{name: "forest1.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=500"}, {name: "forest2.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-1476231682828-37e571bc172f?w=500"}, {name: "forest3.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-1425913397330-cf8af2ff40a1?w=500"}, {name: "forest4.jpg", type: "image/jpeg", url: "https://images.unsplash.com/photo-1511497584788-876760111969?w=500"}]},
  {id: 'b5', authorId: 'userDemo123', authorName: 'Demo User', authorAvatarUrl: 'https://avatar.vercel.sh/demouser.png?size=40', title: 'Impact of Social Media on Nigerian Elections', details: 'Social media platforms played an unprecedented role in the last election cycle. Was it a net positive for democracy, or did it amplify misinformation and division? How can we harness its power for good while mitigating the risks? Let\'s discuss!', createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), upvotes: 30, downvotes: 1, comments: [{commentId:'bc2', userId:'editorDemo789', userName:'Editor User', text:'Good points. Regulation is tricky but necessary.', timestamp: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(), upvotes: 7, downvotes: 0, userAvatarUrl: 'https://avatar.vercel.sh/editoruser.png?size=32', media: []}], tags: {topic: 'Elections', technology: 'Social Media'}, sourceLink: '', media: []}
];

export const BanterProvider = ({ children }) => {
  const { user } = useAuth();
  const [banters, setBanters] = useState([]);
  const [userVotes, setUserVotes] = useState({}); 

  useEffect(() => {
    const storedBanter = localStorage.getItem('leaderboxBanters');
    const storedUserVotes = localStorage.getItem('leaderboxBanterUserVotes');
    if (!storedBanter) {
      setBanters(initialBanterRoomPostsData.map(b => ({...b, media: b.media || [], comments: (b.comments || []).map(c => ({...c, media: c.media || []})) })));
      localStorage.setItem('leaderboxBanters', JSON.stringify(initialBanterRoomPostsData));
    } else {
      setBanters(JSON.parse(storedBanter).map(b => ({...b, media: b.media || [], comments: (b.comments || []).map(c => ({...c, media: c.media || []})) })));
    }
    if (storedUserVotes) {
      setUserVotes(JSON.parse(storedUserVotes));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('leaderboxBanterUserVotes', JSON.stringify(userVotes));
  }, [userVotes]);

  const addBanter = (banterData) => {
    if (!user) return null;
    const newBanter = {
      id: `b${Date.now()}`,
      authorId: user.id,
      authorName: user.name || user.email.split('@')[0],
      authorAvatarUrl: user.avatarUrl,
      ...banterData,
      media: banterData.media || [],
      createdAt: new Date().toISOString(),
      upvotes: 0,
      downvotes: 0,
      comments: []
    };
    setBanters(prev => {
      const updated = [newBanter, ...prev];
      localStorage.setItem('leaderboxBanters', JSON.stringify(updated));
      return updated;
    });
    return newBanter;
  };
  
  const updateBanter = (banterId, updatedData) => {
    setBanters(prev => {
      const updated = prev.map(banter => 
        banter.id === banterId ? { ...banter, ...updatedData } : banter
      );
      localStorage.setItem('leaderboxBanters', JSON.stringify(updated));
      return updated;
    });
  };

  const getBanterById = (banterId) => {
    return banters.find(b => b.id === banterId);
  };

  const addCommentToBanter = (banterId, text, parentCommentId = null, media = []) => {
    if (!user) return;
    setBanters(prevBanters => {
      const updatedBanters = prevBanters.map(b => {
        if (b.id === banterId) {
          const newComment = {
            commentId: `bc${Date.now()}-${Math.random().toString(36).substring(2,9)}`,
            userId: user.id,
            userName: user.name || user.email.split('@')[0],
            userAvatarUrl: user.avatarUrl,
            text,
            timestamp: new Date().toISOString(),
            upvotes: 0,
            downvotes: 0,
            media: media || [],
            replies: parentCommentId ? undefined : []
          };

          if (parentCommentId) {
            const commentsWithReplies = (b.comments || []).map(existingComment => {
              if (existingComment.commentId === parentCommentId) {
                return { ...existingComment, replies: [...(existingComment.replies || []), newComment] };
              }
              return existingComment;
            });
             return { ...b, comments: commentsWithReplies };
          } else {
            return { ...b, comments: [...(b.comments || []), newComment] };
          }
        }
        return b;
      });
      localStorage.setItem('leaderboxBanters', JSON.stringify(updatedBanters));
      return updatedBanters;
    });
  };

  const voteOnBanterItem = (itemId, voteType, itemType = 'banter') => { 
    if (!user) return;
    const voteKey = `${itemType}-${itemId}-${user.id}`;
    const currentVote = userVotes[voteKey]; 

    let newUpvotesChange = 0;
    let newDownvotesChange = 0;
    let newUserVoteStatus = undefined;

    if (voteType === 'upvote') {
      if (currentVote === 'upvote') { 
        newUpvotesChange = -1;
        newUserVoteStatus = undefined;
      } else { 
        newUpvotesChange = 1;
        if (currentVote === 'downvote') newDownvotesChange = -1;
        newUserVoteStatus = 'upvote';
      }
    } else if (voteType === 'downvote') {
      if (currentVote === 'downvote') { 
        newDownvotesChange = -1;
        newUserVoteStatus = undefined;
      } else { 
        newDownvotesChange = 1;
        if (currentVote === 'upvote') newUpvotesChange = -1;
        newUserVoteStatus = 'downvote';
      }
    }
    
    setUserVotes(prev => ({...prev, [voteKey]: newUserVoteStatus }));

    setBanters(prevBanters => {
      const updatedBanters = prevBanters.map(b => {
        if (itemType === 'banter' && b.id === itemId) {
          return { ...b, upvotes: (b.upvotes || 0) + newUpvotesChange, downvotes: (b.downvotes || 0) + newDownvotesChange };
        } else if (itemType === 'comment' && b.comments) {
            const updatedComments = (b.comments || []).map(c => {
                if (c.commentId === itemId) {
                    return { ...c, upvotes: (c.upvotes || 0) + newUpvotesChange, downvotes: (c.downvotes || 0) + newDownvotesChange };
                } else if (c.replies) {
                    const updatedReplies = (c.replies || []).map(r => 
                        r.commentId === itemId ? { ...r, upvotes: (r.upvotes || 0) + newUpvotesChange, downvotes: (r.downvotes || 0) + newDownvotesChange } : r
                    );
                    if (updatedReplies.some(r => r.commentId === itemId)) {
                        return { ...c, replies: updatedReplies };
                    }
                }
                return c;
            });
            return { ...b, comments: updatedComments };
        }
        return b;
      });
      localStorage.setItem('leaderboxBanters', JSON.stringify(updatedBanters));
      return updatedBanters;
    });
  };
  
  const flagBanter = (banterId) => {
    setBanters(prevBanters => {
      const updatedBanters = prevBanters.map(b => 
        b.id === banterId ? { ...b, isFlagged: true } : b
      );
      localStorage.setItem('leaderboxBanters', JSON.stringify(updatedBanters));
      return updatedBanters;
    });
  };

  const deleteBanter = (banterId) => {
    setBanters(prevBanters => {
      const updated = prevBanters.filter(b => b.id !== banterId);
      localStorage.setItem('leaderboxBanters', JSON.stringify(updated));
      return updated;
    });
  };

  const deleteBanterComment = (banterId, commentId) => {
    setBanters(prevBanters => {
      const updatedBanters = prevBanters.map(b => {
        if (b.id === banterId) {
          return { ...b, comments: (b.comments || []).filter(c => c.commentId !== commentId) };
        }
        return b;
      });
      localStorage.setItem('leaderboxBanters', JSON.stringify(updatedBanters));
      return updatedBanters;
    });
  };

  const value = {
    banters,
    addBanter,
    getBanterById,
    addCommentToBanter,
    voteOnBanterItem,
    userVotes,
    flagBanter,
    deleteBanter,
    deleteBanterComment,
    updateBanter,
    muteBanterThread: (banterId, muteState) => console.log(`Mute state for ${banterId}: ${muteState}`),
    blockBanterUser: (authorId, blockState) => console.log(`Block state for ${authorId}: ${blockState}`),
  };

  return <BanterContext.Provider value={value}>{children}</BanterContext.Provider>;
};
