import React, { createContext, useContext, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext.jsx'; // Example: If AppState depends on Auth

const AppStateContext = createContext(null);

export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};

export const AppStateProvider = ({ children }) => {
  const { user } = useAuth(); // Example: Using auth state in app state
  const [theme, setTheme] = useState('light'); // Example state: theme
  const [notifications, setNotifications] = useState([]); // Example state: notifications

  // Example function to toggle theme
  const toggleTheme = () => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  // Example function to add a notification
  const addNotification = (message, type = 'info') => {
    const newNotification = { id: Date.now(), message, type };
    setNotifications(prev => [...prev, newNotification]);
    // Optional: auto-remove notification after some time
    setTimeout(() => {
      setNotifications(current => current.filter(n => n.id !== newNotification.id));
    }, 5000);
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // You can derive some state based on other contexts, e.g., user preferences
  // useEffect(() => {
  //   if (user && user.preferences && user.preferences.theme) {
  //     setTheme(user.preferences.theme);
  //   }
  // }, [user]);

  const value = {
    theme,
    toggleTheme,
    notifications,
    addNotification,
    removeNotification,
    // Potentially expose user from AuthContext if needed frequently by AppState consumers
    //authUser: user 
  };

  return (
    <AppStateContext.Provider value={value}>
      {children}
    </AppStateContext.Provider>
  );
};