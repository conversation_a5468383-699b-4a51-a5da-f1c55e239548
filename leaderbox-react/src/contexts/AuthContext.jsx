import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const initialDemoUsers = {
  "<EMAIL>": { 
    id: 'userDemo123', 
    email: "<EMAIL>", 
    name: "Demo User", 
    state: "Lagos", 
    lga: "Ikeja", 
    gender: "Male", 
    party: "Neutral", 
    isLoggedIn: true, 
    isAdmin: false,
    role: "User",
    status: "active",
    avatarUrl: '', // Intentionally left blank for reminder testing
    followedLeaders: ['1', '3'],
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    petitionsCreated: 12,
    profileLikesCount: 1500,
    groupMembersCount: 50,
  },
  "<EMAIL>": {
    id: 'adminDemo456',
    email: "<EMAIL>",
    name: "Admin User",
    state: "FCT",
    lga: "Abuja Municipal",
    gender: "Female",
    party: "APC",
    isLoggedIn: true,
    isAdmin: true,
    role: "Overall Admin", 
    status: "active",
    avatarUrl: 'https://avatar.vercel.sh/adminuser.png?size=40',
    followedLeaders: [],
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
    petitionsCreated: 1,
    profileLikesCount: 50,
    groupMembersCount: 5,
  },
  "<EMAIL>": {
    id: 'editorDemo789',
    email: "<EMAIL>",
    name: "Editor User",
    state: "Kano",
    lga: "Kano Municipal",
    gender: "Male",
    party: "NNPP",
    isLoggedIn: true,
    isAdmin: true,
    role: "Editor", 
    status: "active",
    avatarUrl: 'https://avatar.vercel.sh/editoruser.png?size=40',
    followedLeaders: ['5'],
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    petitionsCreated: 0,
    profileLikesCount: 100,
    groupMembersCount: 0,
  },
  "<EMAIL>": {
    id: 'creatorDemo101',
    email: "<EMAIL>",
    name: "Creator User",
    state: "Rivers",
    lga: "Port Harcourt",
    gender: "Female",
    party: "PDP",
    isLoggedIn: true,
    isAdmin: true, 
    role: "Content Creator", 
    status: "active",
    avatarUrl: 'https://avatar.vercel.sh/creatoruser.png?size=40',
    followedLeaders: ['2'],
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    petitionsCreated: 5,
    profileLikesCount: 2000,
    groupMembersCount: 1100,
  }
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [allRegisteredUsers, setAllRegisteredUsers] = useState([]);
  const [onboardingComplete, setOnboardingComplete] = useState(false);
  const [userProfileLikes, setUserProfileLikes] = useState({});

  useEffect(() => {
    let initialUsers = [];
    const storedUsers = localStorage.getItem('leaderboxAllUsers');
    if (storedUsers) {
      try {
        initialUsers = JSON.parse(storedUsers);
        if (!Array.isArray(initialUsers)) {
          initialUsers = Object.values(initialDemoUsers);
        }
      } catch (error) {
        initialUsers = Object.values(initialDemoUsers);
      }
    } else {
      initialUsers = Object.values(initialDemoUsers);
    }
    setAllRegisteredUsers(initialUsers);

    const storedUser = localStorage.getItem('leaderboxUser');
    const storedOnboardingStatus = localStorage.getItem('leaderboxOnboardingComplete');
    
    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      const existingUser = initialUsers.find(u => u.id === parsedUser.id);

      if (existingUser) {
        setUser({ ...existingUser, ...parsedUser }); 
        if(storedOnboardingStatus === 'true') {
            setOnboardingComplete(true);
        }
      } else {
        localStorage.removeItem('leaderboxUser');
        localStorage.removeItem('leaderboxOnboardingComplete');
      }
    }

    const storedLikes = localStorage.getItem('leaderboxUserProfileLikes');
    if (storedLikes) {
      setUserProfileLikes(JSON.parse(storedLikes));
    }
  }, []); 

  useEffect(() => {
    if (allRegisteredUsers.length > 0) {
      const isDifferentFromInitialDemo = JSON.stringify(allRegisteredUsers) !== JSON.stringify(Object.values(initialDemoUsers));
      const hasNonDemoUsers = allRegisteredUsers.some(u => u.id.startsWith('userReg'));

      if(isDifferentFromInitialDemo || hasNonDemoUsers || !localStorage.getItem('leaderboxAllUsers')) {
         localStorage.setItem('leaderboxAllUsers', JSON.stringify(allRegisteredUsers));
      }
    }
  }, [allRegisteredUsers]);

  useEffect(() => {
    localStorage.setItem('leaderboxUserProfileLikes', JSON.stringify(userProfileLikes));
  }, [userProfileLikes]);


  const login = (email, password) => {
    const foundUser = allRegisteredUsers.find(u => u.email === email);
    let isValidPassword = false;

    if (foundUser) {
        if (initialDemoUsers[email]) {
            if (email === "<EMAIL>" && password === "password123") isValidPassword = true;
            else if (email === "<EMAIL>" && password === "adminpass123") isValidPassword = true;
            else if (email === "<EMAIL>" && password === "editorpass") isValidPassword = true;
            else if (email === "<EMAIL>" && password === "creatorpass") isValidPassword = true;
        } else if (foundUser.password === password) { 
            isValidPassword = true;
        }
    }
    
    if (foundUser && isValidPassword) {
      const userToLogin = {...foundUser, isLoggedIn: true};
      localStorage.setItem('leaderboxUser', JSON.stringify(userToLogin));
      setUser(userToLogin);
      const storedOnboardingStatus = localStorage.getItem(`leaderboxOnboardingComplete_${userToLogin.id}`);
      if (storedOnboardingStatus === 'true') {
        setOnboardingComplete(true);
      }
      return userToLogin;
    }
    return null; 
  };

  const logout = () => {
    if(user) {
      localStorage.removeItem(`leaderboxOnboardingComplete_${user.id}`);
    }
    localStorage.removeItem('leaderboxUser');
    setUser(null);
    setOnboardingComplete(false);
  };

  const register = (userData) => {
    if (allRegisteredUsers.find(u => u.email === userData.email)) {
        throw new Error("This email is already registered. Please use a different email or log in.");
    }
    const newUser = { 
      id: `userReg${Date.now()}`, 
      ...userData, 
      isLoggedIn: true, 
      isAdmin: false,
      role: "User",
      status: "active",
      avatarUrl: '', // Start with no avatar
      followedLeaders: [],
      createdAt: new Date().toISOString(),
      petitionsCreated: 0,
      profileLikesCount: 0,
      groupMembersCount: 0,
    };
    setAllRegisteredUsers(prevUsers => [...prevUsers, newUser]);
    localStorage.setItem('leaderboxUser', JSON.stringify(newUser));
    setUser(newUser);
    setOnboardingComplete(false); 
    return newUser;
  };
  
  const completeOnboarding = (updatedUserData) => {
    setUser(prevUser => {
      const fullyOnboardedUser = { ...prevUser, ...updatedUserData };
      localStorage.setItem('leaderboxUser', JSON.stringify(fullyOnboardedUser));
      localStorage.setItem(`leaderboxOnboardingComplete_${fullyOnboardedUser.id}`, 'true');
      setOnboardingComplete(true);
      
      setAllRegisteredUsers(prevAll => prevAll.map(u => u.id === fullyOnboardedUser.id ? fullyOnboardedUser : u));
      return fullyOnboardedUser;
    });
  };

  const updateUserStatus = (userId, status) => {
    setAllRegisteredUsers(prevUsers => 
      prevUsers.map(u => u.id === userId ? { ...u, status } : u)
    );
    if (user?.id === userId) {
      const updatedCurrentUserDetails = {...user, status};
      setUser(updatedCurrentUserDetails);
      localStorage.setItem('leaderboxUser', JSON.stringify(updatedCurrentUserDetails));
    }
  };

  const deleteRegisteredUser = (userId) => {
    setAllRegisteredUsers(prevUsers => prevUsers.filter(u => u.id !== userId));
    if (user?.id === userId) { 
      logout();
    }
  };

  const updateRegisteredUserDetails = (userId, details) => {
    setAllRegisteredUsers(prevUsers => 
      prevUsers.map(u => u.id === userId ? { ...u, ...details } : u)
    );
    if (user?.id === userId) {
      const updatedCurrentUserDetails = {...user, ...details};
      setUser(updatedCurrentUserDetails);
      localStorage.setItem('leaderboxUser', JSON.stringify(updatedCurrentUserDetails));
    }
  };

  const toggleProfileLike = (profileId) => {
    if (!user) return;

    setUserProfileLikes(prevLikes => {
      const profileLikerIds = prevLikes[profileId] || [];
      const hasLiked = profileLikerIds.includes(user.id);
      let newLikerIds;

      if (hasLiked) {
        newLikerIds = profileLikerIds.filter(id => id !== user.id);
      } else {
        newLikerIds = [...profileLikerIds, user.id];
      }
      
      setAllRegisteredUsers(prevAllUsers => 
        prevAllUsers.map(u => 
          u.id === profileId ? { ...u, profileLikesCount: newLikerIds.length } : u
        )
      );

      return { ...prevLikes, [profileId]: newLikerIds };
    });
  };

  const value = {
    user,
    users: allRegisteredUsers, 
    setUser,
    login,
    logout,
    register,
    onboardingComplete,
    completeOnboarding,
    updateUserStatus,
    deleteRegisteredUser,
    updateRegisteredUserDetails,
    userProfileLikes,
    toggleProfileLike,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};