import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext.jsx';

const LeaderContext = createContext(null);

export const useLeaderData = () => {
  const context = useContext(LeaderContext);
  if (!context) throw new Error('useLeaderData must be used within a LeaderProvider');
  return context;
};

const initialLeadersData = [
  { id: '1', name: '<PERSON><PERSON><PERSON><PERSON>', position: 'Minister of Power', party: 'APC', state: 'Oyo', avatarUrl: 'https://i.pravatar.cc/150?u=adebayo', followers: 1250, currentRating: 4.5, totalRatings: 150, publicSentiment: 'Positive', bio: '<PERSON><PERSON><PERSON><PERSON> is a Nigerian politician currently serving as the Minister of Power. He has a background in finance and previously served as Deputy Governor of the Central Bank of Nigeria.', youtubeVideoUrl: 'https://www.youtube.com/watch?v=ysz5S6PUM-U', detailedBio: 'Educated at the University of Lagos, <PERSON><PERSON><PERSON> has had a distinguished career in both the private and public sectors before his ministerial appointment.', education: 'B.Sc. Accounting, MBA Finance', background: 'Ex-Deputy Governor, CBN.', popularityScore: 85, isFollowed: false, isVerified: true },
  { id: '2', name: 'Nyesom Wike', position: 'Minister of FCT', party: 'PDP', state: 'Rivers', avatarUrl: 'https://i.pravatar.cc/150?u=wike', followers: 2580, currentRating: 4.2, totalRatings: 200, publicSentiment: 'Neutral', bio: 'Nyesom Wike is the current Minister of the Federal Capital Territory. He is the former Governor of Rivers State, known for his infrastructural projects.', youtubeVideoUrl: 'https://youtu.be/ysz5S6PUM-U', detailedBio: 'As governor, he was nicknamed "Mr. Projects" for his focus on infrastructure. His tenure was marked by significant urban renewal and development initiatives in Rivers State.', education: 'LL.B Law, BL', background: 'Former Governor of Rivers State.', popularityScore: 90, isFollowed: false, isVerified: true },
  { id: '3', name: 'Festus Keyamo', position: 'Minister of Aviation', party: 'APC', state: 'Delta', avatarUrl: 'https://i.pravatar.cc/150?u=keyamo', followers: 930, currentRating: 2.8, totalRatings: 100, publicSentiment: 'Negative', bio: 'Festus Keyamo, SAN, is a Nigerian lawyer and politician. He is the current Minister of Aviation and Aerospace Development.', youtubeVideoUrl: '', detailedBio: 'A Senior Advocate of Nigeria, Keyamo has been a vocal figure in Nigerian law and politics for decades, known for his human rights activism before joining the government.', education: 'LL.M, SAN', background: 'Legal Practitioner, Human Rights Activist.', popularityScore: 70, isFollowed: false, isVerified: false },
  { id: '4', name: 'Peter Obi', position: 'Presidential Candidate', party: 'LP', state: 'Anambra', avatarUrl: 'https://i.pravatar.cc/150?u=peterobi', followers: 5500, currentRating: 4.8, totalRatings: 350, publicSentiment: 'Positive', bio: 'Peter Obi is a Nigerian businessman and politician who served as Governor of Anambra State. He was the Labour Party candidate for President in 2023.', youtubeVideoUrl: 'https://www.youtube.com/watch?v=ysz5S6PUM-U', detailedBio: 'Known for his frugal approach to governance and extensive knowledge of economics, Peter Obi gained a massive youth following during his presidential campaign, centered on shifting Nigeria from consumption to production.', education: 'B.A. Philosophy, Various Executive Programs', background: 'Former Governor of Anambra State, Businessman.', popularityScore: 98, isFollowed: false, isVerified: true },
  { id: '5', name: 'Bola Ahmed Tinubu', position: 'President', party: 'APC', state: 'Lagos', avatarUrl: 'https://i.pravatar.cc/150?u=tinubu', followers: 10200, currentRating: 3.5, totalRatings: 500, publicSentiment: 'Neutral', bio: 'Bola Ahmed Tinubu is the current President of Nigeria. He previously served as the Governor of Lagos State from 1999 to 2007.', youtubeVideoUrl: 'https://www.youtube.com/embed/ysz5S6PUM-U', detailedBio: 'A master strategist, Tinubu is credited with laying the blueprint for modern Lagos. His political influence has been a dominant force in Nigeria for over two decades.', education: 'B.Sc. Business Administration', background: 'Former Governor of Lagos State.', popularityScore: 95, isFollowed: false, isVerified: true },
  { id: '6', name: 'Atiku Abubakar', position: 'Presidential Candidate', party: 'PDP', state: 'Adamawa', avatarUrl: 'https://i.pravatar.cc/150?u=atiku', followers: 7500, currentRating: 4.1, totalRatings: 400, publicSentiment: 'Neutral', bio: 'Atiku Abubakar is a Nigerian politician and businessman who served as the Vice President of Nigeria from 1999 to 2007.', youtubeVideoUrl: '', detailedBio: 'A perennial presidential contender, Atiku Abubakar has a vast business empire and has been a central figure in Nigeria\'s political landscape since the return to democracy.', education: 'Diploma in Law', background: 'Former Vice President of Nigeria.', popularityScore: 92, isFollowed: false, isVerified: true },
  { id: '7', name: 'Nasir El-Rufai', position: 'Former Governor', party: 'APC', state: 'Kaduna', avatarUrl: 'https://i.pravatar.cc/150?u=elrufai', followers: 3100, currentRating: 4.3, totalRatings: 280, publicSentiment: 'Positive', bio: 'Nasir El-Rufai served as Governor of Kaduna State. Known for reforms and urban renewal projects.', youtubeVideoUrl: '', detailedBio: 'A quantity surveyor by training, El-Rufai is known for his bold and often controversial reforms in public service and governance.', education: 'MBA, Harvard University', background: 'Former Minister of FCT.', popularityScore: 88, isFollowed: false, isVerified: false },
  { id: '8', name: 'Seyi Makinde', position: 'Governor', party: 'PDP', state: 'Oyo', avatarUrl: 'https://i.pravatar.cc/150?u=makinde', followers: 2800, currentRating: 4.6, totalRatings: 250, publicSentiment: 'Positive', bio: 'Seyi Makinde is the current Governor of Oyo State, focusing on education and infrastructure.', youtubeVideoUrl: '', detailedBio: 'An engineer and businessman, Makinde\'s populist policies and focus on public infrastructure have made him popular in Oyo State.', education: 'B.Sc. Electrical Engineering', background: 'Businessman.', popularityScore: 89, isFollowed: false, isVerified: true },
  { id: '9', name: 'Babajide Sanwo-Olu', position: 'Governor', party: 'APC', state: 'Lagos', avatarUrl: 'https://i.pravatar.cc/150?u=sanwoolu', followers: 4200, currentRating: 2.1, totalRatings: 320, publicSentiment: 'Neutral', bio: 'Babajide Sanwo-Olu is the Governor of Lagos State, overseeing various mega-projects.', youtubeVideoUrl: '', detailedBio: 'Tasked with governing Nigeria\'s economic hub, Sanwo-Olu\'s administration focuses on the T.H.E.M.E.S. agenda, covering key development areas for Lagos.', education: 'MBA, University of Lagos', background: 'Banking, Public Administration.', popularityScore: 91, isFollowed: false, isVerified: true },
  { id: '10', name: 'Rabiu Kwankwaso', position: 'Presidential Candidate', party: 'NNPP', state: 'Kano', avatarUrl: 'https://i.pravatar.cc/150?u=kwankwaso', followers: 6200, currentRating: 4.4, totalRatings: 300, publicSentiment: 'Positive', bio: 'Rabiu Kwankwaso is a prominent politician from Kano, former Governor and Minister.', youtubeVideoUrl: '', detailedBio: 'Founder of the Kwankwasiyya movement, he is known for his signature red cap and has a strong grassroots following, particularly in Northern Nigeria.', education: 'PhD Water Engineering', background: 'Former Governor of Kano State.', popularityScore: 93, isFollowed: false, isVerified: true },
  { id: '11', name: 'David Umahi', position: 'Minister of Works', party: 'APC', state: 'Ebonyi', avatarUrl: 'https://i.pravatar.cc/150?u=umahi', followers: 1500, currentRating: 4.0, totalRatings: 180, publicSentiment: 'Neutral', bio: 'David Umahi, former Governor of Ebonyi State, now Minister of Works, known for concrete road technology.', youtubeVideoUrl: '', detailedBio: 'An engineer by profession, he gained national attention for his use of concrete in road construction during his time as governor.', education: 'B.Sc. Civil Engineering', background: 'Former Governor of Ebonyi State.', popularityScore: 80, isFollowed: false, isVerified: true },
  { id: '12', name: 'Charles Soludo', position: 'Governor', party: 'APGA', state: 'Anambra', avatarUrl: 'https://i.pravatar.cc/150?u=soludo', followers: 3500, currentRating: 4.7, totalRatings: 290, publicSentiment: 'Positive', bio: 'Charles Chukwuma Soludo is an economics professor and the current Governor of Anambra State.', youtubeVideoUrl: '', detailedBio: 'A former Governor of the Central Bank of Nigeria, Soludo is bringing his economic expertise to bear on the governance of Anambra State.', education: 'PhD Economics', background: 'Former CBN Governor.', popularityScore: 94, isFollowed: false, isVerified: true },
  { id: '13', name: 'Hope Uzodinma', position: 'Governor', party: 'APC', state: 'Imo', avatarUrl: 'https://i.pravatar.cc/150?u=uzodinma', followers: 1900, currentRating: 3.7, totalRatings: 220, publicSentiment: 'Negative', bio: 'Hope Uzodinma is the current Governor of Imo State.', youtubeVideoUrl: '', detailedBio: 'His assumption of office was through a Supreme Court ruling, and his tenure has focused on what he terms the "3R" agenda: Reconstruction, Rehabilitation, and Recovery.', education: 'Diploma, Maritime Management', background: 'Businessman, Politician.', popularityScore: 75, isFollowed: false, isVerified: false },
  { id: '14', name: 'Yahaya Bello', position: 'Former Governor', party: 'APC', state: 'Kogi', avatarUrl: 'https://i.pravatar.cc/150?u=bello', followers: 2200, currentRating: 3.9, totalRatings: 260, publicSentiment: 'Neutral', bio: 'Yahaya Bello is the immediate past Governor of Kogi State.', youtubeVideoUrl: '', detailedBio: 'Known as one of the youngest governors in Nigeria, his administration was notable for its emphasis on youth and women inclusion in governance.', education: 'B.Sc. Accounting, MBA', background: 'Businessman.', popularityScore: 78, isFollowed: false, isVerified: false },
  { id: '15', name: 'Babatunde Fashola', position: 'Former Minister', party: 'APC', state: 'Lagos', avatarUrl: 'https://i.pravatar.cc/150?u=fashola', followers: 4100, currentRating: 4.6, totalRatings: 340, publicSentiment: 'Positive', bio: 'Babatunde Fashola, SAN, is a former Governor of Lagos and recently served as the Minister of Works and Housing.', youtubeVideoUrl: '', detailedBio: 'Highly regarded for his performance as Lagos Governor, Fashola is seen by many as a technocrat focused on performance and efficiency.', education: 'LL.B, SAN', background: 'Former Governor, Former Minister.', popularityScore: 93, isFollowed: false, isVerified: true },
  { id: '16', name: 'Aminu Tambuwal', position: 'Senator', party: 'PDP', state: 'Sokoto', avatarUrl: 'https://i.pravatar.cc/150?u=tambuwal', followers: 3300, currentRating: 4.2, totalRatings: 270, publicSentiment: 'Positive', bio: 'Aminu Tambuwal, former Governor of Sokoto State and former Speaker of the House of Representatives.', youtubeVideoUrl: '', detailedBio: 'A lawyer by training, Tambuwal has held high-ranking positions in both the legislative and executive arms of government.', education: 'LL.B Law', background: 'Former Governor, Former Speaker.', popularityScore: 87, isFollowed: false, isVerified: true },
  { id: '17', name: 'Godwin Obaseki', position: 'Governor', party: 'PDP', state: 'Edo', avatarUrl: 'https://i.pravatar.cc/150?u=obaseki', followers: 2900, currentRating: 4.3, totalRatings: 240, publicSentiment: 'Positive', bio: 'Godwin Obaseki is the current Governor of Edo State, focusing on technology and investment.', youtubeVideoUrl: '', detailedBio: 'With a background in investment banking, Governor Obaseki\'s policies are geared towards making Edo State an attractive destination for technology and industrial investment.', education: 'MA Finance and International Business', background: 'Investment Banker.', popularityScore: 86, isFollowed: false, isVerified: true },
  { id: '18', name: 'Kayode Fayemi', position: 'Former Governor', party: 'APC', state: 'Ekiti', avatarUrl: 'https://i.pravatar.cc/150?u=fayemi', followers: 2400, currentRating: 4.5, totalRatings: 230, publicSentiment: 'Positive', bio: 'Kayode Fayemi is a former Governor of Ekiti State and former Minister of Solid Minerals Development.', youtubeVideoUrl: '', detailedBio: 'A scholar in civil-military relations, Fayemi is also the former Chairman of the Nigerian Governors\' Forum.', education: 'PhD War Studies', background: 'Academic, Politician.', popularityScore: 84, isFollowed: false, isVerified: false },
  { id: '19', name: 'Ifeanyi Okowa', position: 'Former Governor', party: 'PDP', state: 'Delta', avatarUrl: 'https://i.pravatar.cc/150?u=okowa', followers: 2600, currentRating: 4.1, totalRatings: 210, publicSentiment: 'Neutral', bio: 'Ifeanyi Okowa is the immediate past Governor of Delta State and was the PDP vice-presidential candidate in 2023.', youtubeVideoUrl: '', detailedBio: 'A medical doctor turned politician, Okowa rose through the ranks from local government to the governorship.', education: 'MBBS Medicine', background: 'Medical Doctor, Politician.', popularityScore: 82, isFollowed: false, isVerified: true },
  { id: '20', name: 'Abdullahi Ganduje', position: 'National Chairman', party: 'APC', state: 'Kano', avatarUrl: 'https://i.pravatar.cc/150?u=ganduje', followers: 3800, currentRating: 3.9, totalRatings: 310, publicSentiment: 'Neutral', bio: 'Abdullahi Umar Ganduje is the National Chairman of APC and former Governor of Kano State.', youtubeVideoUrl: '', detailedBio: 'Ganduje\'s political career spans several decades, including multiple terms as a deputy governor before becoming governor himself.', education: 'PhD Public Administration', background: 'Former Governor.', popularityScore: 85, isFollowed: false, isVerified: true },
  { id: '21', name: 'Mai Mala Buni', position: 'Governor', party: 'APC', state: 'Yobe', avatarUrl: 'https://i.pravatar.cc/150?u=buni', followers: 1700, currentRating: 4.0, totalRatings: 190, publicSentiment: 'Neutral', bio: 'Mai Mala Buni is the current Governor of Yobe State and former APC Caretaker Chairman.', youtubeVideoUrl: '', detailedBio: 'Buni played a crucial role in managing the APC at the national level as its caretaker chairman, overseeing the party\'s transition.', education: 'B.Sc. International Relations', background: 'Businessman, Politician.', popularityScore: 79, isFollowed: false, isVerified: false },
];

export const LeaderProvider = ({ children }) => {
  const { user, setUser: setAuthUser } = useAuth();
  const [leaders, setLeaders] = useState(() => {
    const localLeaders = localStorage.getItem('leaders');
    const parsedLeaders = localLeaders ? JSON.parse(localLeaders) : initialLeadersData;
    return parsedLeaders.map(l => ({...l, isFollowed: user?.followedLeaders?.includes(l.id) || false, detailedBio: l.detailedBio || `More details about ${l.name} coming soon.` }));
  });

  const [leaderUserRatings, setLeaderUserRatings] = useState(() => {
    const storedRatings = localStorage.getItem('leaderUserRatings');
    return storedRatings ? JSON.parse(storedRatings) : {};
  });

  const [leaderComments, setLeaderComments] = useState(() => {
    const storedComments = localStorage.getItem('leaderComments');
    return storedComments ? JSON.parse(storedComments) : {};
  });

  const [userLeaderCommentVotes, setUserLeaderCommentVotes] = useState(() => {
    const storedVotes = localStorage.getItem('userLeaderCommentVotes');
    return storedVotes ? JSON.parse(storedVotes) : {};
  });

  useEffect(() => {
    setLeaders(prevLeaders => 
      prevLeaders.map(l => ({
        ...l,
        isFollowed: user?.followedLeaders?.includes(l.id) || false
      }))
    );
  }, [user]);

  useEffect(() => {
    localStorage.setItem('leaders', JSON.stringify(leaders));
  }, [leaders]);

  useEffect(() => {
    localStorage.setItem('leaderUserRatings', JSON.stringify(leaderUserRatings));
  }, [leaderUserRatings]);

  useEffect(() => {
    localStorage.setItem('leaderComments', JSON.stringify(leaderComments));
  }, [leaderComments]);

  useEffect(() => {
    localStorage.setItem('userLeaderCommentVotes', JSON.stringify(userLeaderCommentVotes));
  }, [userLeaderCommentVotes]);


  const followLeader = (leaderId) => {
    if (!user) return;
    setLeaders(prevLeaders => 
      prevLeaders.map(leader => 
        leader.id === leaderId ? { ...leader, followers: (leader.followers || 0) + 1, isFollowed: true } : leader
      )
    );
    setAuthUser(prevUser => {
      const updatedUser = { ...prevUser, followedLeaders: [...(prevUser?.followedLeaders || []), leaderId] };
      localStorage.setItem('leaderboxUser', JSON.stringify(updatedUser));
      return updatedUser;
    });
  };

  const unfollowLeader = (leaderId) => {
    if (!user) return;
    setLeaders(prevLeaders => 
      prevLeaders.map(leader => 
        leader.id === leaderId ? { ...leader, followers: Math.max(0, (leader.followers || 1) - 1), isFollowed: false } : leader
      )
    );
    setAuthUser(prevUser => {
      const updatedUser = { ...prevUser, followedLeaders: (prevUser?.followedLeaders || []).filter(id => id !== leaderId) };
      localStorage.setItem('leaderboxUser', JSON.stringify(updatedUser));
      return updatedUser;
    });
  };

  const rateLeader = (leaderId, rating, commentText) => {
    if (!user) return;
    const now = Date.now();
    
    setLeaderUserRatings(prevRatings => {
      const newRatings = {
        ...prevRatings,
        [leaderId]: { ...prevRatings[leaderId], [user.id]: { rating, comment: commentText, lastRated: now } }
      };
      return newRatings;
    });

    if (commentText && commentText.trim() !== "") {
      const newComment = {
        commentId: `cmt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        userId: user.id,
        userName: user.name || user.email.split('@')[0],
        userAvatarUrl: user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`,
        text: commentText,
        ratingGiven: rating,
        upvotes: 0,
        downvotes: 0,
        timestamp: new Date().toISOString(),
        replies: [],
        media: [] 
      };
      setLeaderComments(prevComments => {
        const updatedCommentsForLeader = [...(prevComments[leaderId] || []), newComment];
        return { ...prevComments, [leaderId]: updatedCommentsForLeader };
      });
    }

    setLeaders(prevLeaders => prevLeaders.map(l => {
      if (l.id === leaderId) {
        const userPreviousRatingData = leaderUserRatings[leaderId]?.[user.id];
        let newTotalRatings = l.totalRatings || 0;
        let sumOfRatings = (l.currentRating || 0) * newTotalRatings;

        if (userPreviousRatingData && userPreviousRatingData.rating !== undefined && typeof userPreviousRatingData.rating === 'number') { 
          sumOfRatings = sumOfRatings - userPreviousRatingData.rating + rating;
        } else { 
          sumOfRatings += rating;
          if (!(userPreviousRatingData && typeof userPreviousRatingData.rating === 'number')) {
            newTotalRatings += 1;
          }
        }
        const newAverageRating = newTotalRatings > 0 ? parseFloat((sumOfRatings / newTotalRatings).toFixed(1)) : 0;
        
        let newPublicSentiment = 'Neutral';
        if (newAverageRating >= 4) {
          newPublicSentiment = 'Positive';
        } else if (newAverageRating < 3) {
          newPublicSentiment = 'Negative';
        }
        return { ...l, currentRating: newAverageRating, totalRatings: newTotalRatings, publicSentiment: newPublicSentiment };
      }
      return l;
    }));
  };

  const getLeaderRatingByUser = (leaderId) => {
    if (user && leaderUserRatings[leaderId] && leaderUserRatings[leaderId][user.id]) {
      return leaderUserRatings[leaderId][user.id];
    }
    return null;
  };

  const getCommentsForLeader = (leaderId) => {
    return leaderComments[leaderId] || [];
  };

  const addCommentToLeaderProfile = (leaderId, text, parentCommentId = null, media = []) => {
    if (!user) return;

    const newCommentOrReply = {
      commentId: `cmt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, 
      replyId: parentCommentId ? `rpl-${Date.now()}-${Math.random().toString(36).substr(2, 9)}` : undefined,
      userId: user.id,
      userName: user.name || user.email.split('@')[0],
      userAvatarUrl: user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`,
      text: text,
      timestamp: new Date().toISOString(),
      upvotes: 0,
      downvotes: 0,
      replies: parentCommentId ? undefined : [],
      media: media 
    };

    setLeaderComments(prevComments => {
      const currentLeaderComments = prevComments[leaderId] ? [...prevComments[leaderId]] : [];
      if (parentCommentId) {
        const parentIndex = currentLeaderComments.findIndex(c => c.commentId === parentCommentId);
        if (parentIndex !== -1) {
          const parent = currentLeaderComments[parentIndex];
          parent.replies = [...(parent.replies || []), newCommentOrReply];
        }
      } else {
        currentLeaderComments.push(newCommentOrReply);
      }
      return { ...prevComments, [leaderId]: currentLeaderComments };
    });
  };

  const voteOnLeaderComment = (leaderId, commentId, voteType) => {
    if (!user) return;
    const voteKey = `${leaderId}-${commentId}-${user.id}`;
    const currentVoteStatus = userLeaderCommentVotes[voteKey];

    setLeaderComments(prevComments => {
      const updatedLeaderComments = (prevComments[leaderId] || []).map(comment => {
        if (comment.commentId === commentId) {
          let newUpvotes = comment.upvotes || 0;
          let newDownvotes = comment.downvotes || 0;

          if (currentVoteStatus === 'upvote') {
            newUpvotes = Math.max(0, newUpvotes - 1);
          } else if (currentVoteStatus === 'downvote') {
            newDownvotes = Math.max(0, newDownvotes - 1);
          }

          if (currentVoteStatus !== voteType) {
            if (voteType === 'upvote') newUpvotes++;
            if (voteType === 'downvote') newDownvotes++;
          }
          
          return { ...comment, upvotes: newUpvotes, downvotes: newDownvotes };
        }
        return comment;
      });
      return { ...prevComments, [leaderId]: updatedLeaderComments };
    });
    
    setUserLeaderCommentVotes(prevVotes => ({
      ...prevVotes,
      [voteKey]: currentVoteStatus === voteType ? null : voteType 
    }));
  };
  
  const value = {
    leaders,
    setLeaders,
    leaderUserRatings,
    leaderComments,
    userLeaderCommentVotes,
    followLeader,
    unfollowLeader,
    rateLeader,
    getLeaderRatingByUser,
    getCommentsForLeader,
    addCommentToLeaderProfile,
    voteOnLeaderComment,
  };

  return <LeaderContext.Provider value={value}>{children}</LeaderContext.Provider>;
};