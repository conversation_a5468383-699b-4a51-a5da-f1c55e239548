import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext.jsx';

const PetitionContext = createContext(null);

export const usePetitions = () => {
  const context = useContext(PetitionContext);
  if (!context) {
    throw new Error('usePetitions must be used within a PetitionProvider');
  }
  return context;
};

const initialPetitionsData = [
  {id: 'pet1', title: 'Improve Road Infrastructure in Aba, Abia State', createdBy: 'AbaResident', signatures: 1200, target: 5000, description: 'The roads in Aba, particularly key commercial routes like Ariaria Market access road, are in dire need of comprehensive repair and reconstruction. This impacts daily commute, businesses, and overall quality of life. We urge the Abia State government and the Federal Ministry of Works to prioritize these repairs.', timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), status: 'active', signers: Array.from({length: 1200}, (_, i) => `Signer ${i+1}`), taggedLeader: '<PERSON>', media: [] },
  {id: 'pet2', title: 'Increase Funding for Public Primary Schools Nationwide', createdBy: 'EduAdvocate', signatures: 3500, target: 10000, description: 'Our public primary schools are the bedrock of our education system, yet they suffer from chronic underfunding. This petition calls for a significant increase in federal and state budgetary allocations to improve teacher salaries, provide updated learning materials, and rehabilitate dilapidated school facilities across all 774 LGAs.', timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), status: 'active', signers: Array.from({length: 3500}, (_, i) => `Supporter ${i+1}`), taggedLeader: 'Tahir Mamman', media: [] },
  {id: 'pet3', title: 'Urgent Action on Youth Unemployment in Nigeria', createdBy: 'YouthEmpowerNG', signatures: 780, target: 2000, description: 'Youth unemployment is a ticking time bomb. We demand concrete government policies and programs that create sustainable jobs, support entrepreneurship, and provide relevant skills training for young Nigerians. This includes reviving Ajaokuta Steel, textile industries, and investing in tech hubs.', timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), status: 'active', signers: Array.from({length: 780}, (_, i) => `Concerned Youth ${i+1}`), taggedLeader: 'President Bola Tinubu', media: [] },
  {id: 'pet4', title: 'Enforce Stricter Environmental Protection Laws Against Oil Spills in Niger Delta', createdBy: 'DeltaVoice', signatures: 25, target: 1000, description: 'The continuous oil spills in the Niger Delta are devastating our environment and livelihoods. We demand stricter enforcement of existing environmental laws, increased penalties for offending companies, and immediate cleanup/remediation of affected areas. NOSDRA and Ministry of Environment must act decisively.', timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), status: 'pending', signers: Array.from({length: 25}, (_, i) => `Eco Defender ${i+1}`), taggedLeader: 'Minister of Environment', media: [] },
];

export const PetitionProvider = ({ children }) => {
  const { user, updateRegisteredUserDetails } = useAuth();
  const [petitions, setPetitions] = useState([]);

  useEffect(() => {
    const storedPetitions = localStorage.getItem('leaderboxPetitions');
    if (!storedPetitions) {
      setPetitions(initialPetitionsData.map(p => ({...p, media: p.media || []})));
      localStorage.setItem('leaderboxPetitions', JSON.stringify(initialPetitionsData));
    } else {
      setPetitions(JSON.parse(storedPetitions).map(p => ({...p, media: p.media || []})));
    }
  }, []);

  const addPetition = (petitionData) => {
    if (!user) return null;
    const newPetition = {
      id: `pet${Date.now()}`,
      createdBy: user.name || user.email.split('@')[0],
      signatures: 0,
      signers: [],
      status: 'pending',
      ...petitionData,
      media: petitionData.media || [],
      timestamp: new Date().toISOString()
    };
    setPetitions(prev => {
      const updated = [newPetition, ...prev];
      localStorage.setItem('leaderboxPetitions', JSON.stringify(updated));
      return updated;
    });
    
    const newPetitionsCount = (user.petitionsCreated || 0) + 1;
    updateRegisteredUserDetails(user.id, { petitionsCreated: newPetitionsCount });

    return newPetition;
  };

  const signPetition = (petitionId) => {
    if (!user) return null;
    let signedPetition = null;
    setPetitions(prevPetitions => {
      const updatedPetitions = prevPetitions.map(p => {
        if (p.id === petitionId && !(p.signers || []).includes(user.name || user.email)) {
          const newSignatures = (p.signatures || 0) + 1;
          const newSigners = [...(p.signers || []), user.name || user.email];
          let newStatus = p.status;
          if (newStatus === 'pending' && newSignatures >= 20) {
            newStatus = 'active';
          }
          signedPetition = { ...p, signatures: newSignatures, signers: newSigners, status: newStatus };
          return signedPetition;
        }
        return p;
      });
      if(signedPetition){
        localStorage.setItem('leaderboxPetitions', JSON.stringify(updatedPetitions));
      }
      return updatedPetitions;
    });
    return signedPetition; 
  };

  const getPetitionById = (petitionId) => {
    return petitions.find(p => p.id === petitionId);
  };

  const value = {
    petitions,
    addPetition,
    signPetition,
    getPetitionById,
  };

  return <PetitionContext.Provider value={value}>{children}</PetitionContext.Provider>;
};