import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext.jsx';

const DataContext = createContext(null);

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  if (context === null) {
     throw new Error('DataProvider not found in the component tree');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  const { user } = useAuth();


  const value = {
    user,
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
};