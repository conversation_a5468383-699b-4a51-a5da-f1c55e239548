@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%; /* White */
  --foreground: 222.2 84% 4.9%; /* Dark Blue/Black */

  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;

  --primary: 221.2 83.2% 53.3%; /* Brighter Blue */
  --primary-foreground: 210 40% 98%;

  --secondary: 210 40% 96.1%; /* Light Grayish Blue */
  --secondary-foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --accent: 142.1 70.6% 45.3%; /* Green */
  --accent-foreground: 210 40% 98%;

  --destructive: 0 84.2% 60.2%; /* Red */
  --destructive-foreground: 210 40% 98%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;

  --radius: 0.75rem; /* Increased border radius for modern feel */

  --poll-option-1: 221 83% 53%; /* primary */
  --poll-option-2: 142 70% 45%; /* accent (green) */
  --poll-option-3: 38 92% 50%; /* orange */
  --poll-option-4: 340 82% 52%; /* pink */
  --poll-option-5: 270 70% 50%; /* purple */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;

  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;

  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;

  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 47.4% 11.2%;

  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 217.2 91.2% 59.8%;
  --accent-foreground: 210 40% 98%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 216 34% 17%;

  --poll-option-1: 217 91% 59%;
  --poll-option-2: 142 70% 35%;
  --poll-option-3: 38 92% 40%;
  --poll-option-4: 340 82% 42%;
  --poll-option-5: 270 70% 40%;
}

body {
  @apply bg-background text-foreground antialiased;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

h1, h2, h3, h4, h5, h6 {
  @apply font-bold tracking-tight;
}

.gradient-text {
  @apply text-transparent bg-clip-text bg-gradient-to-r;
}

/* Modern Card Style */
.modern-card {
  @apply bg-card border border-border rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 ease-in-out;
}
.modern-card-header {
  @apply p-5 md:p-6;
}
.modern-card-content {
  @apply p-5 md:p-6 pt-0;
}
.modern-card-footer {
  @apply p-5 md:p-6 bg-secondary/30 rounded-b-xl;
}

/* Modern Input Style */
.modern-input {
  @apply w-full px-4 py-3 rounded-lg border border-input bg-background placeholder-muted-foreground focus:ring-2 focus:ring-ring focus:border-ring transition-colors duration-200;
}

/* Modern Button Styles */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
}
.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
}
.btn-accent {
  @apply bg-accent text-accent-foreground hover:bg-accent/90;
}
.btn-destructive {
  @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
}
.btn-outline-primary {
  @apply border border-primary text-primary hover:bg-primary/10;
}


/* Custom scrollbar for a more modern look */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background) / 0.5);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}

/* Global focus visible style for accessibility */
*:focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* General page container */
.page-container {
  @apply container mx-auto px-4 py-6 md:py-8;
}

.header-override {
  @apply bg-card shadow-lg text-foreground sticky top-0 z-50 border-b border-border;
}
.header-override a {
  @apply hover:text-primary;
}
.header-override .logo-override {
   @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent;
}

.mobile-nav-override {
  @apply bg-card border-t border-border shadow-[0_-2px_10px_rgba(0,0,0,0.05)];
}
.mobile-nav-override a {
  @apply text-muted-foreground hover:text-primary;
}
.mobile-nav-override a.active {
  @apply text-primary;
}

.footer-override {
    @apply bg-secondary/50 text-muted-foreground border-t border-border;
}
.footer-override a {
    @apply hover:text-primary;
}

.input-override {
  @apply bg-background border-input text-foreground placeholder-muted-foreground focus:ring-primary focus:border-primary;
}

.select-trigger-override {
  @apply bg-background border-input text-foreground;
}
.select-content-override {
  @apply bg-popover text-popover-foreground border-border shadow-lg;
}

.button-outline-override {
  @apply text-primary border-primary hover:bg-primary/10 hover:text-primary;
}

.card-override {
  @apply bg-card border-border text-card-foreground shadow-lg hover:shadow-xl transition-shadow;
}
.card-override .card-title-override {
  @apply text-primary;
}
.card-override .card-content-override p {
  @apply text-foreground/80;
}
.card-override .card-footer-override {
  @apply bg-secondary/30;
}

/* Sentiment Colors based on rating */
.sentiment-rating-positive { @apply bg-green-500; } /* 4-5 stars */
.sentiment-rating-neutral { @apply bg-yellow-400; } /* 3-3.9 stars */
.sentiment-rating-negative { @apply bg-red-500; } /* < 3 stars */

.sentiment-text-positive { @apply text-green-600; }
.sentiment-text-neutral { @apply text-yellow-600; }
.sentiment-text-negative { @apply text-red-600; }

/* General Sentiment Breakdown Bar Colors (can be different from rating-based ones if needed) */
.sentiment-bar-positive { @apply bg-green-500; }
.sentiment-bar-neutral { @apply bg-yellow-400; }
.sentiment-bar-negative { @apply bg-red-500; }

/* Homepage Civic Icons Overlay */
.civic-icons-bg {
  position: relative;
  overflow: hidden; /* To contain pseudo-elements if they overflow */
}

.civic-icons-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 24 24' fill='none' stroke='%23e5e7eb' stroke-width='0.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16'%3E%3C/path%3E%3Cpath d='M12 20h4'%3E%3C/path%3E%3Cpath d='M12 4H8'%3E%3C/path%3E%3Cpath d='M18 12h-8'%3E%3C/path%3E%3C/svg%3E"), /* Ballot box icon */
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='50' height='50' viewBox='0 0 24 24' fill='none' stroke='%23e5e7eb' stroke-width='0.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cline x1='10' y1='9' x2='8' y2='9'%3E%3C/line%3E%3C/svg%3E"), /* Document/Petition icon */
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24' fill='none' stroke='%23e5e7eb' stroke-width='0.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='9' cy='7' r='4'%3E%3C/circle%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87'%3E%3C/path%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75'%3E%3C/path%3E%3C/svg%3E"); /* Users/Group icon */
  background-repeat: space, space, space; /* Control repetition for each image */
  background-position: 10% 20%, 50% 50%, 80% 70%; /* Vary positions */
  background-size: 30px, 40px, 35px; /* Vary sizes */
  opacity: 0.07; /* Very faint */
  z-index: -1; /* Behind content */
  pointer-events: none; /* Allow clicks through */
}

/* Poll option button colors */
.poll-option-btn-1 { @apply bg-[hsl(var(--poll-option-1))] hover:bg-[hsl(var(--poll-option-1)/0.9)] text-primary-foreground; }
.poll-option-btn-2 { @apply bg-[hsl(var(--poll-option-2))] hover:bg-[hsl(var(--poll-option-2)/0.9)] text-primary-foreground; }
.poll-option-btn-3 { @apply bg-[hsl(var(--poll-option-3))] hover:bg-[hsl(var(--poll-option-3)/0.9)] text-primary-foreground; }
.poll-option-btn-4 { @apply bg-[hsl(var(--poll-option-4))] hover:bg-[hsl(var(--poll-option-4)/0.9)] text-primary-foreground; }
.poll-option-btn-5 { @apply bg-[hsl(var(--poll-option-5))] hover:bg-[hsl(var(--poll-option-5)/0.9)] text-primary-foreground; }

/* Mobile specific leader activity tabs */
.mobile-activity-tabs .radix-tabs-list {
  @apply grid grid-cols-2 gap-1; /* Example: 2 columns on mobile for better fit */
}

.mobile-activity-tabs .radix-tabs-trigger {
  @apply px-2 py-2 text-xs; /* Smaller text and padding */
}