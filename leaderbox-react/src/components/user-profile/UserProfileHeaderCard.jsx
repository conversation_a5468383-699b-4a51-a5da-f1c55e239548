
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Settings, Mail, MapPin, Briefcase, ShieldCheck, CalendarDays, Heart, Megaphone, Users } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip.jsx';

const getBadges = (user) => {
  const badges = [];
  if ((user.petitionsCreated || 0) >= 10) {
    badges.push({ name: 'Activist', icon: <Megaphone size={12} className="mr-1" />, color: 'bg-orange-500/20 text-orange-500 border-orange-500/30', description: 'Posted 10+ petitions' });
  }
  if ((user.profileLikesCount || 0) >= 1000) {
    badges.push({ name: 'Influencer', icon: <Heart size={12} className="mr-1" />, color: 'bg-pink-500/20 text-pink-500 border-pink-500/30', description: 'Over 1000 profile likes' });
  }
  if ((user.groupMembersCount || 0) >= 1000) {
    badges.push({ name: 'Organizer', icon: <Users size={12} className="mr-1" />, color: 'bg-blue-500/20 text-blue-500 border-blue-500/30', description: 'Over 1000 group members' });
  }
  return badges;
};

const UserProfileHeaderCard = ({ profileUser, isCurrentUserProfile, onSendMessage, onEditProfile, onLikeToggle, isLikedByCurrentUser }) => {
  if (!profileUser) return null;

  const userJoinedDate = profileUser.createdAt ? new Date(profileUser.createdAt).toLocaleDateString() : "Earlier";
  const userBadges = getBadges(profileUser);

  return (
    <div className="modern-card overflow-hidden shadow-xl">
      <div className="relative">
        <div className="h-40 md:h-48 bg-gradient-to-br from-primary/60 via-accent/40 to-secondary/50"></div>
        <div className="absolute left-1/2 md:left-8 transform -translate-x-1/2 md:translate-x-0 -bottom-16 md:-bottom-12 z-10">
          <Avatar className="h-32 w-32 md:h-36 md:w-36 border-4 border-card shadow-lg bg-secondary rounded-full">
            <AvatarImage src={profileUser.avatarUrl || `https://avatar.vercel.sh/${profileUser.name || profileUser.email}.png?size=144`} alt={profileUser.name} />
            <AvatarFallback className="text-5xl text-primary">{profileUser.name ? profileUser.name.substring(0, 2).toUpperCase() : (profileUser.email ? profileUser.email.substring(0,2).toUpperCase() : 'U')}</AvatarFallback>
          </Avatar>
        </div>
      </div>
      <div className="pt-20 md:pt-8 pb-6 px-6 md:pl-[calc(2rem+9.5rem)] text-center md:text-left">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-foreground flex items-center justify-center md:justify-start">
              {profileUser.name || "User"}
              {profileUser.isLeader && <ShieldCheck size={24} className="ml-2 text-blue-500" title="Verified Leader"/>}
            </h1>
            <p className="text-muted-foreground text-sm">@{profileUser.name?.toLowerCase().replace(/\s+/g, '') || profileUser.email?.split('@')[0]}</p>
          </div>
          <div className="flex items-center gap-2">
            {!isCurrentUserProfile && (
              <Button onClick={onLikeToggle} variant={isLikedByCurrentUser ? "default" : "outline"} className={`transition-all ${isLikedByCurrentUser ? 'btn-primary' : 'button-outline-override'}`}>
                <Heart size={18} className={`mr-2 ${isLikedByCurrentUser ? 'fill-current' : ''}`} />
                {profileUser.profileLikesCount || 0}
              </Button>
            )}
            {isCurrentUserProfile ? (
              <Button variant="outline" onClick={onEditProfile} className="button-outline-override">
                <Settings size={18} className="mr-2" /> Edit Profile
              </Button>
            ) : (
              <Button onClick={onSendMessage} className="btn-primary">
                <Mail size={18} className="mr-2" /> Message
              </Button>
            )}
          </div>
        </div>
        {userBadges.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2 justify-center md:justify-start">
            <TooltipProvider>
              {userBadges.map(badge => (
                <Tooltip key={badge.name}>
                  <TooltipTrigger>
                    <Badge variant="outline" className={`font-semibold ${badge.color}`}>
                      {badge.icon}
                      {badge.name}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{badge.description}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </TooltipProvider>
          </div>
        )}
        {profileUser.bio && <p className="mt-3 text-sm text-foreground/80 max-w-xl">{profileUser.bio}</p>}
        <div className="mt-4 flex flex-wrap gap-x-4 gap-y-2 text-xs text-muted-foreground justify-center md:justify-start">
          {profileUser.position && <span className="flex items-center"><Briefcase size={14} className="mr-1.5"/> {profileUser.position}</span>}
          {profileUser.state && <span className="flex items-center"><MapPin size={14} className="mr-1.5"/> {profileUser.state}{profileUser.lga && `, ${profileUser.lga}`}</span>}
          {profileUser.party && <span className="flex items-center"><ShieldCheck size={14} className="mr-1.5"/> {profileUser.party}</span>}
          <span className="flex items-center"><CalendarDays size={14} className="mr-1.5"/> Joined {userJoinedDate}</span>
        </div>
      </div>
    </div>
  );
};

export default UserProfileHeaderCard;
