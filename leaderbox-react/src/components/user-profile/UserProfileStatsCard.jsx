
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>T<PERSON>le, CardContent } from '@/components/ui/card.jsx';
import { UserCheck as UserCheckIcon, Heart } from 'lucide-react';

const UserProfileStatsCard = ({ profileUser }) => {
  const leadersFollowedCount = !profileUser.isLeader ? (profileUser.followedLeaders?.length || 0) : undefined;

  return (
    <Card className="modern-card">
      <CardHeader><CardTitle className="text-lg text-primary">Profile Stats</CardTitle></CardHeader>
      <CardContent className="space-y-2 text-sm">
        <div className="flex justify-between">
            <span className="flex items-center"><Heart size={14} className="mr-1.5 text-muted-foreground"/> Profile Likes:</span> 
            <span className="font-semibold">{profileUser.profileLikesCount || 0}</span>
        </div>
        {profileUser.isLeader && (
           <div className="flex justify-between">
            <span className="flex items-center"><UserCheckIcon size={14} className="mr-1.5 text-muted-foreground"/> Followers:</span> 
            <span className="font-semibold">{profileUser.followers}</span>
          </div>
        )}
        {leadersFollowedCount !== undefined && (
          <div className="flex justify-between">
            <span className="flex items-center"><UserCheckIcon size={14} className="mr-1.5 text-muted-foreground"/> Leaders Followed:</span> 
            <span className="font-semibold">{leadersFollowedCount}</span>
          </div>
        )}
        <div className="flex justify-between"><span>Posts/Activities:</span> <span className="font-semibold">{profileUser.activityCount || Math.floor(Math.random()*50)}</span></div>
      </CardContent>
    </Card>
  );
};

export default UserProfileStatsCard;
