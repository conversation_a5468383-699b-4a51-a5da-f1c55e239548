import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, CardHeader, Card<PERSON>itle, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const UserActivityItem = ({ type, content, date }) => (
  <div className="p-3 border-b border-border last:border-b-0 hover:bg-secondary/50 rounded-md">
    <p className="text-sm text-foreground">{content}</p>
    <p className="text-xs text-muted-foreground">{type} &bull; {new Date(date).toLocaleDateString()}</p>
  </div>
);

const UserActivityTabsContent = ({ profileUser, leaders }) => {
  return (
    <>
      <Tabs defaultValue="activity" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 bg-secondary/50 rounded-lg p-1 mb-4 overflow-x-auto no-scrollbar">
          <TabsTrigger value="activity" className="dashboard-tab-trigger">Activity</TabsTrigger>
          <TabsTrigger value="banters" className="dashboard-tab-trigger">Banters</TabsTrigger>
          {!profileUser.isLeader && <TabsTrigger value="followedLeaders" className="dashboard-tab-trigger">Following</TabsTrigger>}
        </TabsList>
        <TabsContent value="activity">
          <Card className="modern-card">
            <CardHeader><CardTitle className="text-lg text-primary">Recent Activity</CardTitle></CardHeader>
            <CardContent>
              <UserActivityItem type="Comment" content={`Replied to "Fuel Subsidy Debate"`} date={new Date(Date.now() - 1 * 60 * 60 * 1000)} />
              <UserActivityItem type="Vote" content={`Voted on "Best approach to urban development?"`} date={new Date(Date.now() - 5 * 60 * 60 * 1000)} />
              {!profileUser.isLeader && profileUser.followedLeaders?.length > 0 && (
                <UserActivityItem type="Follow" content={`Started following ${leaders.find(l => l.id === profileUser.followedLeaders[0])?.name || 'a Leader'}`} date={new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)} />
              )}
              <p className="text-center text-muted-foreground pt-4">More activity coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="banters"><Card className="modern-card p-4"><p className="text-muted-foreground">User's banters will appear here.</p></Card></TabsContent>
        {!profileUser.isLeader && (
            <TabsContent value="followedLeaders">
                <Card className="modern-card p-4">
                    <CardTitle className="text-lg text-primary mb-2">Leaders Followed</CardTitle>
                    {profileUser.followedLeaders && profileUser.followedLeaders.length > 0 ? (
                        <ul className="space-y-2">
                            {profileUser.followedLeaders.map(leaderId => {
                                const leader = leaders.find(l => l.id === leaderId);
                                return leader ? (
                                    <li key={leaderId} className="flex items-center space-x-2 p-2 hover:bg-secondary/30 rounded-md">
                                        <Avatar className="h-8 w-8">
                                            <AvatarImage src={leader.avatarUrl} alt={leader.name}/>
                                            <AvatarFallback>{leader.name.substring(0,1)}</AvatarFallback>
                                        </Avatar>
                                        <Link to={`/leader/${leader.id}`} className="text-sm hover:underline text-foreground">{leader.name}</Link>
                                    </li>
                                ) : null;
                            })}
                        </ul>
                    ) : (
                        <p className="text-muted-foreground">Not following any leaders yet.</p>
                    )}
                </Card>
            </TabsContent>
        )}
      </Tabs>
      <style jsx>{`
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .no-scrollbar {
            -ms-overflow-style: none; 
            scrollbar-width: none; 
        }
        .dashboard-tab-trigger {
            @apply data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-3 py-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1.5 whitespace-nowrap;
        }
      `}</style>
    </>
  );
};

export default UserActivityTabsContent;