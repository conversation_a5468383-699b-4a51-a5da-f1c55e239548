
import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { PlusCircle, ThumbsDown, Users, MapPin, Tag as TagIcon, Paperclip } from 'lucide-react';
import { Combobox } from '@/components/ui/combobox';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import MediaUpload from '@/components/ui/MediaUpload.jsx';


const CreatePollDialog = ({ isOpen, onClose, onCreatePoll }) => {
  const [topic, setTopic] = useState('');
  const [description, setDescription] = useState('');
  const [options, setOptions] = useState(['', '']);
  const [taggedLeader, setTaggedLeader] = useState('');
  const [taggedLocation, setTaggedLocation] = useState('');
  const [generalTags, setGeneralTags] = useState(''); 
  const [attachedMedia, setAttachedMedia] = useState([]);

  const { leaders } = useLeaderData();
  const leaderOptions = useMemo(() => leaders.map(l => ({ value: l.name, label: l.name })), [leaders]);
  const stateOptions = useMemo(() => {
    const states = new Set(leaders.map(l => l.state).filter(Boolean));
    return Array.from(states).map(s => ({ value: s, label: s }));
  }, [leaders]);

  const handleAddOption = () => {
    if (options.length < 5) { 
      setOptions([...options, '']);
    }
  };

  const handleOptionChange = (index, value) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const handleRemoveOption = (index) => {
    if (options.length > 2) { 
      const newOptions = options.filter((_, i) => i !== index);
      setOptions(newOptions);
    }
  };

  const handleMediaChange = (files) => {
    setAttachedMedia(files.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
      url: URL.createObjectURL(file) 
    })));
  };

  const handleSubmit = () => {
    if (!topic.trim() || options.some(opt => !opt.trim()) || options.length < 2) {
      alert("Please fill in the topic and at least two non-empty options.");
      return;
    }
    onCreatePoll({ 
      topic, 
      description, 
      options: options.filter(opt => opt.trim()), 
      taggedLeader,
      taggedLocation,
      generalTags: generalTags.split(',').map(tag => tag.trim()).filter(tag => tag),
      media: attachedMedia,
    });
    setTopic('');
    setDescription('');
    setOptions(['', '']);
    setTaggedLeader('');
    setTaggedLocation('');
    setGeneralTags('');
    setAttachedMedia([]);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-2xl">Create New Poll</DialogTitle>
          <DialogDescription>
            Engage the community with your questions. Fill in the details below.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
          <div>
            <Label htmlFor="poll-topic" className="text-foreground">Poll Topic</Label>
            <Input id="poll-topic" value={topic} onChange={(e) => setTopic(e.target.value)} placeholder="e.g., Best approach to urban development?" className="modern-input mt-1" />
          </div>
          <div>
            <Label htmlFor="poll-description" className="text-foreground">Background Info (Optional, max 150 words)</Label>
            <Textarea id="poll-description" value={description} onChange={(e) => setDescription(e.target.value)} placeholder="Provide brief context or details for your poll..." className="modern-input mt-1 min-h-[80px]" maxLength={800} />
          </div>
          <div>
            <Label className="text-foreground">Poll Options (Min 2, Max 5)</Label>
            {options.map((option, index) => (
              <div key={index} className="flex items-center gap-2 mt-1">
                <Input value={option} onChange={(e) => handleOptionChange(index, e.target.value)} placeholder={`Option ${index + 1}`} className="modern-input" />
                {options.length > 2 && (
                  <Button variant="ghost" size="icon" onClick={() => handleRemoveOption(index)} className="text-destructive hover:bg-destructive/10 h-9 w-9">
                    <ThumbsDown size={16} />
                  </Button>
                )}
              </div>
            ))}
            {options.length < 5 && (
              <Button variant="outline" onClick={handleAddOption} className="mt-2 text-sm btn-outline-primary">
                <PlusCircle size={16} className="mr-2" /> Add Option
              </Button>
            )}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
             <div>
                <Label htmlFor="tag-leader-poll" className="text-foreground flex items-center"><Users size={14} className="mr-1.5 text-muted-foreground" /> Tag Leader (Optional)</Label>
                <Combobox
                    options={leaderOptions}
                    value={taggedLeader}
                    onChange={setTaggedLeader}
                    placeholder="Select or type leader..."
                    inputClassName="modern-input mt-1"
                />
            </div>
            <div>
                <Label htmlFor="tag-location-poll" className="text-foreground flex items-center"><MapPin size={14} className="mr-1.5 text-muted-foreground" /> Tag State/Location (Optional)</Label>
                <Combobox
                    options={stateOptions}
                    value={taggedLocation}
                    onChange={setTaggedLocation}
                    placeholder="Select or type state..."
                    inputClassName="modern-input mt-1"
                />
            </div>
          </div>
           <div>
            <Label htmlFor="poll-general-tags" className="text-foreground flex items-center"><TagIcon size={14} className="mr-1.5 text-muted-foreground" />General Topic Tags (Optional, comma-separated)</Label>
            <Input id="poll-general-tags" value={generalTags} onChange={(e) => setGeneralTags(e.target.value)} placeholder="e.g., economy, education, lagos" className="modern-input mt-1" />
          </div>
          <div>
            <Label htmlFor="poll-media" className="text-foreground flex items-center"><Paperclip size={14} className="mr-1.5 text-muted-foreground" /> Attach Media (Optional)</Label>
            <MediaUpload onFilesChange={handleMediaChange} />
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="button" onClick={handleSubmit} className="btn-primary">Create Poll</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePollDialog;
