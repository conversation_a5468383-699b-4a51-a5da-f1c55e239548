import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

const CommentAfterVoteDialog = ({ isOpen, onClose, pollTopic, onSubmitComment }) => {
  const [commentText, setCommentText] = useState('');

  const handleSubmit = () => {
    onSubmitComment(commentText);
    setCommentText(''); // Reset for next time
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-xl">Thanks for voting!</DialogTitle>
          <DialogDescription>
            You voted on "{pollTopic}". Would you like to add a comment? (Optional)
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Label htmlFor="vote-comment" className="text-foreground">Your Comment</Label>
          <Textarea
            id="vote-comment"
            value={commentText}
            onChange={(e) => setCommentText(e.target.value)}
            placeholder="Share your thoughts or reasons for your vote..."
            className="modern-input mt-1 min-h-[100px]"
          />
        </div>
        <DialogFooter className="gap-2 sm:justify-end">
           <DialogClose asChild>
            <Button type="button" variant="outline" onClick={() => { onSubmitComment(''); onClose(); }}> 
              Skip Comment
            </Button>
          </DialogClose>
          <Button type="button" onClick={handleSubmit} className="btn-primary">
            Submit Vote & Comment
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CommentAfterVoteDialog;