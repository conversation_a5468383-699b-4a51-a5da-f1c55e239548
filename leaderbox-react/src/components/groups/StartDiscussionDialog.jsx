
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { MessageSquare, Paperclip } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import MediaUpload from '@/components/ui/MediaUpload.jsx';

const StartDiscussionDialog = ({ isOpen, onClose, onSubmit }) => {
  const { toast } = useToast();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [attachedMedia, setAttachedMedia] = useState([]);

  const handleMediaChange = (files) => {
    setAttachedMedia(files.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
      url: URL.createObjectURL(file)
    })));
  };

  const handleSubmit = () => {
    if (!title.trim() || !content.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please provide both a title and content for your discussion.',
        variant: 'destructive',
      });
      return;
    }
    onSubmit({ title, content, media: attachedMedia });
    setTitle('');
    setContent('');
    setAttachedMedia([]);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-2xl flex items-center">
            <MessageSquare size={24} className="mr-2" /> Start a New Discussion
          </DialogTitle>
          <DialogDescription>
            Share your topic with the group. Keep it relevant and constructive.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
          <div>
            <Label htmlFor="discussion-title" className="font-semibold">Discussion Title</Label>
            <Input id="discussion-title" name="title" value={title} onChange={(e) => setTitle(e.target.value)} className="modern-input mt-1" />
          </div>
          <div>
            <Label htmlFor="discussion-content" className="font-semibold">Your Post</Label>
            <Textarea 
              id="discussion-content" 
              name="content" 
              value={content} 
              onChange={(e) => setContent(e.target.value)} 
              className="modern-input min-h-[120px] mt-1"
              placeholder="Elaborate on your topic, ask a question, or share your perspective..."
            />
          </div>
          <div>
            <Label htmlFor="discussion-media" className="text-foreground font-semibold flex items-center">
              <Paperclip size={14} className="mr-1.5 text-muted-foreground" /> Attach Media (Optional)
            </Label>
            <MediaUpload onFilesChange={handleMediaChange} />
          </div>
        </div>
        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="button" onClick={handleSubmit} className="btn-primary">
            Post Discussion
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StartDiscussionDialog;
