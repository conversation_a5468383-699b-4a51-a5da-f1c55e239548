import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Users, Globe, Lock } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useGroups } from '@/contexts/GroupContext.jsx';
import { useNavigate } from 'react-router-dom';

const CreateGroupDialog = ({ isOpen, onClose }) => {
  const { createGroup } = useGroups();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: true,
    tags: { leader: '', location: '', party: '', keywords: [] }
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith("tag-")) {
      const tagName = name.split("-")[1];
      if (tagName === 'keywords') {
        setFormData(prev => ({...prev, tags: {...prev.tags, keywords: value.split(',').map(kw => kw.trim()).filter(Boolean)}}));
      } else {
        setFormData(prev => ({...prev, tags: {...prev.tags, [tagName]: value}}));
      }
    } else {
      setFormData(prev => ({...prev, [name]: value}));
    }
  };

  const handleSubmit = () => {
    if (!formData.name.trim() || !formData.description.trim()) {
      toast({
        title: "Missing Information",
        description: "Group name and description are required.",
        variant: "destructive",
      });
      return;
    }

    const newGroup = createGroup(formData);
    toast({
      title: "Group Created!",
      description: `Your group "${newGroup.name}" has been created. You need to invite 10 members for it to be visible to others.`,
    });
    
    toast({
      title: "Next Goal: Political Organizer",
      description: "Increase your group membership to 1,000 members to earn the Organizer badge!",
      duration: 7000,
    });

    setFormData({
        name: '',
        description: '',
        isPublic: true,
        tags: { leader: '', location: '', party: '', keywords: [] }
    });
    onClose();
    navigate(`/group/${newGroup.id}`);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-2xl flex items-center">
            <Users size={24} className="mr-2" /> Create a New Group
          </DialogTitle>
          <DialogDescription>
            Build a community around a shared interest, leader, or cause. 
            Groups require 10 members to become visible.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
          <div>
            <Label htmlFor="group-name" className="font-semibold">Group Name</Label>
            <Input id="group-name" name="name" value={formData.name} onChange={handleChange} className="modern-input mt-1" />
          </div>
          <div>
            <Label htmlFor="group-description" className="font-semibold">Description</Label>
            <Textarea id="group-description" name="description" value={formData.description} onChange={handleChange} className="modern-input min-h-[100px] mt-1" />
          </div>
          <div className="flex items-center space-x-3 mt-2 rounded-lg p-3 bg-secondary/50">
            <Switch id="isPublic" checked={formData.isPublic} onCheckedChange={(checked) => setFormData(p => ({...p, isPublic: checked}))}/>
            <div>
              <Label htmlFor="isPublic" className="font-semibold flex items-center">
                {formData.isPublic ? <Globe size={14} className="mr-2 text-green-500" /> : <Lock size={14} className="mr-2 text-red-500" />}
                {formData.isPublic ? 'Public Group' : 'Private Group'}
              </Label>
              <p className="text-xs text-muted-foreground">
                {formData.isPublic ? "Anyone can find and join this group." : "Only invited members can find and join."}
              </p>
            </div>
          </div>
          <div className="space-y-3 mt-2">
            <h4 className="font-semibold">Tags (Optional)</h4>
            <div>
              <Label htmlFor="tag-leader">Tag Leader</Label>
              <Input id="tag-leader" name="tag-leader" value={formData.tags.leader} onChange={handleChange} className="modern-input mt-1" placeholder="e.g., Peter Obi"/>
            </div>
            <div>
              <Label htmlFor="tag-location">Tag Location</Label>
              <Input id="tag-location" name="tag-location" value={formData.tags.location} onChange={handleChange} className="modern-input mt-1" placeholder="e.g., Lagos"/>
            </div>
            <div>
              <Label htmlFor="tag-party">Tag Party</Label>
              <Input id="tag-party" name="tag-party" value={formData.tags.party} onChange={handleChange} className="modern-input mt-1" placeholder="e.g., LP"/>
            </div>
            <div>
              <Label htmlFor="tag-keywords">Keywords (comma-separated)</Label>
              <Input id="tag-keywords" name="tag-keywords" value={formData.tags.keywords.join(', ')} onChange={handleChange} className="modern-input mt-1" placeholder="e.g., economy, security, youths"/>
            </div>
          </div>
        </div>
        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="button" onClick={handleSubmit} className="btn-primary">
            Create Group
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateGroupDialog;