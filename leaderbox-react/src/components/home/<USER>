
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { UserPlus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const SuggestLeaderDialog = ({ isOpen, onClose, leaderName }) => {
  const [name, setName] = useState('');
  const [position, setPosition] = useState('');
  const [party, setParty] = useState('');
  const [notes, setNotes] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    if (leaderName) {
      setName(leaderName);
    }
  }, [leaderN<PERSON>, isOpen]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!name.trim()) {
      toast({ title: "Error", description: "Leader's name is required.", variant: "destructive" });
      return;
    }
    
    console.log("Leader Suggestion Submitted:", { name, position, party, notes });
    
    toast({
      title: "Suggestion Submitted!",
      description: `Thanks for suggesting ${name}. We'll review it shortly.`,
    });

    setName('');
    setPosition('');
    setParty('');
    setNotes('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-2xl flex items-center">
            <UserPlus size={24} className="mr-2" /> Suggest a New Leader
          </DialogTitle>
          <DialogDescription>
            Help us expand our database. If you know a political leader not listed, please provide their details below.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
          <div>
            <Label htmlFor="suggest-leader-name" className="text-foreground font-semibold">Leader's Full Name</Label>
            <Input
              id="suggest-leader-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="modern-input mt-1"
              required
            />
          </div>
          <div>
            <Label htmlFor="suggest-leader-position" className="text-foreground font-semibold">Position (e.g., Governor, Senator)</Label>
            <Input
              id="suggest-leader-position"
              value={position}
              onChange={(e) => setPosition(e.target.value)}
              placeholder="Current or former political position"
              className="modern-input mt-1"
            />
          </div>
          <div>
            <Label htmlFor="suggest-leader-party" className="text-foreground font-semibold">Political Party</Label>
            <Input
              id="suggest-leader-party"
              value={party}
              onChange={(e) => setParty(e.target.value)}
              placeholder="e.g., APC, PDP, LP"
              className="modern-input mt-1"
            />
          </div>
          <div>
            <Label htmlFor="suggest-leader-notes" className="text-foreground font-semibold">Additional Notes (Optional)</Label>
            <Textarea
              id="suggest-leader-notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any other relevant information, like state, links to profiles, etc."
              className="modern-input mt-1 min-h-[100px]"
            />
          </div>
        </form>
        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="submit" onClick={handleSubmit} className="btn-primary">
            Submit Suggestion
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SuggestLeaderDialog;
