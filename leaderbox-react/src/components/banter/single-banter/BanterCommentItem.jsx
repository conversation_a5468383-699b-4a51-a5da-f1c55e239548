import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { ThumbsUp, ThumbsDown, MessageSquare, MoreVertical, Edit3, Trash2, UserX, Flag } from 'lucide-react';
import { useBanter } from '@/contexts/BanterContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useToast } from "@/components/ui/use-toast";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import MediaGrid from '@/components/shared/MediaGrid.jsx';

const BanterCommentItem = ({ banterId, comment, onReply }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { voteOnBanterItem, userVotes, deleteBanterComment, blockBanterUser } = useBanter();
  
  const isCommentAuthor = user && comment.userId === user.id;
  const userVoteStatus = userVotes[`comment-${comment.commentId}-${user?.id}`];

  const handleVote = (voteType) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to vote.", variant: "destructive" });
      return;
    }
    voteOnBanterItem(comment.commentId, voteType, 'comment');
  };

  const handleBlock = () => {
    if (!user) return;
    blockBanterUser(comment.userId, true);
    toast({ title: "User Blocked", description: `Content from ${comment.userName} will be hidden.` });
  };
  
  const handleDelete = () => {
    deleteBanterComment(banterId, comment.commentId);
    toast({ title: "Comment Deleted" });
  };

  return (
    <div>
      <Card className="bg-background/50 border-border shadow-sm">
        <CardHeader className="p-3 pb-1 flex flex-row items-start space-x-3">
          <Link to={`/user/${comment.userId}`}><Avatar className="h-9 w-9"><AvatarImage src={comment.userAvatarUrl} /><AvatarFallback>{comment.userName?.[0]}</AvatarFallback></Avatar></Link>
          <div className="flex-grow">
            <Link to={`/user/${comment.userId}`} className="font-semibold text-sm text-primary hover:underline">{comment.userName}</Link>
            <p className="text-xs text-muted-foreground">{new Date(comment.timestamp).toLocaleString()}</p>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-7 w-7 text-muted-foreground hover:text-primary"><MoreVertical size={14} /></Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="text-xs">
              {isCommentAuthor && (
                <>
                  <DropdownMenuItem onClick={() => toast({ title: "🚧 Not Implemented", description: "Editing comments isn't available yet."})}><Edit3 size={12} className="mr-2"/>Edit Reply</DropdownMenuItem>
                  <AlertDialog>
                    <AlertDialogTrigger asChild><DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-destructive focus:bg-destructive/10 focus:text-destructive"><Trash2 size={12} className="mr-2"/>Delete Reply</DropdownMenuItem></AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader><AlertDialogTitle>Delete this reply?</AlertDialogTitle><AlertDialogDescription>This action cannot be undone.</AlertDialogDescription></AlertDialogHeader>
                      <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/90">Delete</AlertDialogAction></AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                  <DropdownMenuSeparator />
                </>
              )}
              {!isCommentAuthor && user && (
                <AlertDialog>
                  <AlertDialogTrigger asChild><DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-destructive focus:bg-destructive/10 focus:text-destructive"><UserX size={12} className="mr-2"/>Block {comment.userName}</DropdownMenuItem></AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader><AlertDialogTitle>Block {comment.userName}?</AlertDialogTitle><AlertDialogDescription>You will no longer see their content.</AlertDialogDescription></AlertDialogHeader>
                    <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={handleBlock} className="bg-destructive hover:bg-destructive/90">Yes, Block</AlertDialogAction></AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
              <DropdownMenuItem onClick={() => toast({ title: "🚧 Not Implemented"})}><Flag size={12} className="mr-2"/>Flag Reply</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent className="p-3 pt-1">
          <p className="text-sm text-foreground/90 whitespace-pre-wrap">{comment.text}</p>
          {comment.media && comment.media.length > 0 && (
            <div className="mt-2 rounded-lg overflow-hidden border border-border">
              <MediaGrid mediaItems={comment.media} />
            </div>
          )}
        </CardContent>
        <CardFooter className="p-3 pt-1 border-t border-border/50 flex items-center gap-1">
          <Button variant="ghost" size="sm" onClick={() => handleVote('upvote')} className={`text-xs hover:text-green-500 ${userVoteStatus === 'upvote' ? 'text-green-500' : 'text-muted-foreground'}`}>
            <ThumbsUp size={14} className="mr-1.5" /> <span className="hidden sm:inline mr-0.5">Valid Point</span> ({comment.upvotes || 0})
          </Button>
          <Button variant="ghost" size="sm" onClick={() => handleVote('downvote')} className={`text-xs hover:text-red-500 ${userVoteStatus === 'downvote' ? 'text-red-500' : 'text-muted-foreground'}`}>
            <ThumbsDown size={14} className="mr-1.5" /> <span className="hidden sm:inline mr-0.5">Trash</span> ({comment.downvotes || 0})
          </Button>
          {user && (
            <Button variant="ghost" size="sm" onClick={() => onReply({ parentCommentId: comment.commentId, userNameToReply: comment.userName })} className="text-xs text-muted-foreground hover:text-primary">
              <MessageSquare size={14} className="mr-1.5" /> Reply
            </Button>
          )}
        </CardFooter>
      </Card>
      {comment.children && comment.children.length > 0 && (
        <div className="ml-6 mt-3 pl-4 border-l-2 border-border/50 space-y-3">
          {comment.children
            .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
            .map(reply => (
              <BanterCommentItem
                key={reply.commentId}
                banterId={banterId}
                comment={reply}
                onReply={onReply}
              />
            ))}
        </div>
      )}
    </div>
  );
};

export default BanterCommentItem;