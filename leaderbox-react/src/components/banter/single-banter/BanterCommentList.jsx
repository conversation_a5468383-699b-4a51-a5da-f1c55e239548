import React from 'react';
import BanterCommentItem from './BanterCommentItem.jsx';

const BanterCommentList = ({ banterId, comments, onReply }) => {
  if (!comments || comments.length === 0) {
    return <p className="text-muted-foreground text-center py-4">Looks like no one has dropped a reply yet. Add your voice to the room.</p>;
  }

  const commentTree = comments.reduce((acc, comment) => {
    acc[comment.commentId] = { ...comment, children: [] };
    return acc;
  }, {});

  const rootComments = [];
  Object.values(commentTree).forEach(comment => {
    if (comment.parentCommentId && commentTree[comment.parentCommentId]) {
      commentTree[comment.parentCommentId].children.push(comment);
    } else {
      rootComments.push(comment);
    }
  });

  rootComments.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  return (
    <div className="space-y-4">
      {rootComments.map(comment => (
        <BanterCommentItem
          key={comment.commentId}
          banterId={banterId}
          comment={comment}
          onReply={onReply}
        />
      ))}
    </div>
  );
};

export default BanterCommentList;