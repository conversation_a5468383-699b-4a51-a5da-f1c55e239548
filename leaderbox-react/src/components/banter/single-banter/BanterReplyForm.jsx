
import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useToast } from "@/components/ui/use-toast";
import { Link, useNavigate } from 'react-router-dom';
import MediaUpload from '@/components/ui/MediaUpload.jsx';

const BanterReplyForm = ({ onSubmit, replyingTo, onCancelReply }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [commentText, setCommentText] = useState('');
  const [attachedMedia, setAttachedMedia] = useState([]);
  const commentInputRef = useRef(null);
  
  React.useEffect(() => {
    if (replyingTo) {
      setCommentText(`@${replyingTo.userNameToReply} `);
      commentInputRef.current?.focus();
    } else {
      setCommentText('');
    }
  }, [replyingTo]);
  
  const handleMediaChange = (files) => {
    setAttachedMedia(files.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
      url: URL.createObjectURL(file)
    })));
  };

  const handlePostComment = () => {
    if (!commentText.trim() && attachedMedia.length === 0) {
      toast({ title: "Empty Comment", description: "Cannot post an empty comment.", variant: "destructive" });
      return;
    }
    onSubmit(commentText, attachedMedia);
    setCommentText('');
    setAttachedMedia([]);
  };

  if (!user) {
    return (
      <p className="text-center text-muted-foreground mb-4">
        <Link to="/login" className="text-primary hover:underline">Log in</Link> or <Link to="/register" className="text-primary hover:underline">register</Link> to drop your reply.
      </p>
    );
  }

  return (
    <div className="mb-6 p-4 border border-border rounded-lg bg-secondary/30 space-y-3">
      <Textarea
        ref={commentInputRef}
        value={commentText}
        onChange={(e) => setCommentText(e.target.value)}
        placeholder={replyingTo ? `Replying to ${replyingTo.userNameToReply}...` : "What's your take on this? Keep it civil..."}
        className="modern-input min-h-[100px]"
      />
      <MediaUpload onFilesChange={handleMediaChange} />
      <div className="flex justify-between items-center">
        <Button onClick={handlePostComment} className="btn-primary">Post Reply</Button>
        {replyingTo && (
          <Button variant="ghost" onClick={() => { onCancelReply(); setCommentText(''); setAttachedMedia([]);}} className="text-xs">Cancel Reply</Button>
        )}
      </div>
    </div>
  );
};

export default BanterReplyForm;
