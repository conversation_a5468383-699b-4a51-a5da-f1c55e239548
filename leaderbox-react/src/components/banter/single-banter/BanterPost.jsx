import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ThumbsUp, ThumbsDown, Share2, Flag, Edit3, Trash2, Link as LinkIcon, Tag, UserCircle, MapPin, Flag as PartyFlag } from 'lucide-react';
import { useBanter } from '@/contexts/BanterContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useToast } from "@/components/ui/use-toast";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import MediaGrid from '@/components/shared/MediaGrid.jsx';

const BanterPost = ({ banter, onEdit }) => {
  const { user } = useAuth();
  const { voteOnBanterItem, userVotes, flagBanter, deleteBanter } = useBanter();
  const { toast } = useToast();
  const navigate = useNavigate();

  const isBanterAuthor = user && banter.authorId === user.id;
  const userVoteStatus = userVotes[`banter-${banter.id}-${user?.id}`];

  const handleVote = (voteType) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to vote.", variant: "destructive" });
      return;
    }
    voteOnBanterItem(banter.id, voteType, 'banter');
  };

  const handleFlag = () => {
    if (!user) return;
    flagBanter(banter.id);
    toast({ title: "Banter Flagged", description: "This banter has been flagged for review." });
  };
  
  const handleDelete = () => {
    deleteBanter(banter.id);
    toast({ title: "Banter Deleted", description: "The banter has been removed." });
    navigate('/banter-room');
  };

  const handleShare = () => {
    const shareUrl = window.location.href;
    navigator.clipboard.writeText(shareUrl)
      .then(() => toast({ title: "Link Copied!" }))
      .catch(() => toast({ title: "Error copying link.", variant: "destructive" }));
  };
  
  return (
    <Card className="modern-card overflow-hidden">
      <CardHeader className="modern-card-header bg-gradient-to-r from-primary/10 via-card to-accent/10">
        <div className="flex justify-between items-start">
          <CardTitle className="text-2xl md:text-3xl font-bold text-primary">{banter.title}</CardTitle>
          {isBanterAuthor && (
            <div className="flex gap-2">
              <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary h-8 w-8" onClick={onEdit}>
                <Edit3 size={16} />
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="icon" className="text-destructive hover:bg-destructive/10 h-8 w-8">
                    <Trash2 size={16} />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader><AlertDialogTitle>Are you sure?</AlertDialogTitle><AlertDialogDescription>This will permanently delete this banter.</AlertDialogDescription></AlertDialogHeader>
                  <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/90">Delete</AlertDialogAction></AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2 mt-2">
          <Link to={`/user/${banter.authorId || 'default'}`}><Avatar className="h-8 w-8"><AvatarImage src={banter.authorAvatarUrl} /><AvatarFallback>{banter.authorName?.[0]}</AvatarFallback></Avatar></Link>
          <div>
            <Link to={`/user/${banter.authorId || 'default'}`} className="text-sm font-medium text-foreground hover:underline">{banter.authorName}</Link>
            <p className="text-xs text-muted-foreground">{new Date(banter.createdAt).toLocaleString()}</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="modern-card-content space-y-4">
        <p className="text-md text-foreground/90 whitespace-pre-wrap leading-relaxed">{banter.details}</p>
        {banter.media && banter.media.length > 0 && (
          <div className="mt-3 rounded-lg overflow-hidden border border-border">
             <MediaGrid mediaItems={banter.media} />
          </div>
        )}
        {banter.sourceLink && (
          <a href={banter.sourceLink} target="_blank" rel="noopener noreferrer" className="text-sm text-primary hover:underline flex items-center"><LinkIcon size={14} className="mr-1.5" /> Source</a>
        )}
        <div className="flex flex-wrap gap-2 pt-2">
          {banter.tags?.topic && <span className="tag-display"><Tag size={12} className="mr-1"/>{banter.tags.topic}</span>}
          {banter.tags?.leader && <span className="tag-display"><UserCircle size={12} className="mr-1"/>{banter.tags.leader}</span>}
          {banter.tags?.location && <span className="tag-display"><MapPin size={12} className="mr-1"/>{banter.tags.location}</span>}
          {banter.tags?.party && <span className="tag-display"><PartyFlag size={12} className="mr-1"/>{banter.tags.party}</span>}
        </div>
      </CardContent>

      <CardFooter className="modern-card-footer flex items-center justify-between gap-2">
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" onClick={() => handleVote('upvote')} className={`text-xs hover:text-green-500 ${userVoteStatus === 'upvote' ? 'text-green-500 bg-green-500/10' : 'text-muted-foreground'}`}>
            <ThumbsUp size={14} className="mr-1.5" /> Valid ({banter.upvotes || 0})
          </Button>
          <Button variant="ghost" size="sm" onClick={() => handleVote('downvote')} className={`text-xs hover:text-red-500 ${userVoteStatus === 'downvote' ? 'text-red-500 bg-red-500/10' : 'text-muted-foreground'}`}>
            <ThumbsDown size={14} className="mr-1.5" /> Trash ({banter.downvotes || 0})
          </Button>
        </div>
        <div className="flex items-center gap-1">
           <Button variant="ghost" size="sm" onClick={handleShare} className="text-muted-foreground hover:text-primary text-xs"><Share2 size={14} className="mr-1.5" /> Share</Button>
           <AlertDialog>
              <AlertDialogTrigger asChild><Button variant="ghost" size="sm" className="text-muted-foreground hover:text-destructive text-xs"><Flag size={14} className="mr-1.5" /> Flag</Button></AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader><AlertDialogTitle>Flag this Banter?</AlertDialogTitle><AlertDialogDescription>This helps us maintain community standards.</AlertDialogDescription></AlertDialogHeader>
                <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={handleFlag} className="bg-destructive hover:bg-destructive/90">Yes, Flag</AlertDialogAction></AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
        </div>
      </CardFooter>
    </Card>
  );
};

export default BanterPost;