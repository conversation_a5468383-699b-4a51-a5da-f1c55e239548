import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageSquare, ThumbsUp, Clock, Tag, ThumbsDown } from 'lucide-react';

const BanterCard = ({ banter }) => {
  if (!banter) return null;

  const {
    id,
    title,
    details,
    authorId,
    authorName,
    authorAvatarUrl,
    createdAt,
    upvotes = 0,
    downvotes = 0,
    comments = [],
    tags = {}
  } = banter;

  const displayTags = [
    tags.leader,
    tags.party,
    tags.location,
    tags.topic
  ].filter(Boolean).slice(0, 3);

  const handleTagClick = (e, tagType, tagValue) => {
    e.preventDefault(); 
    e.stopPropagation(); 
    console.log(`Tag clicked: ${tagType} - ${tagValue}`);
    // Future: navigate(`/search?tagType=${tagType}&tagValue=${tagValue}`);
  };

  return (
    <motion.div
      className="modern-card overflow-hidden"
      whileHover={{ y: -5, boxShadow: "0 10px 20px rgba(0,0,0,0.08)" }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className="modern-card-header">
        <div className="flex items-start space-x-3 mb-2">
          <Link to={`/user/${authorId || 'default-user-id'}`} onClick={(e) => e.stopPropagation()}>
            <Avatar className="h-10 w-10 border-2 border-primary/20">
              <AvatarImage src={authorAvatarUrl || `https://avatar.vercel.sh/${authorName || 'anon'}.png?size=40`} alt={authorName} />
              <AvatarFallback>{authorName ? authorName.substring(0, 1).toUpperCase() : 'A'}</AvatarFallback>
            </Avatar>
          </Link>
          <div>
            <Link to={`/user/${authorId || 'default-user-id'}`} onClick={(e) => e.stopPropagation()} className="text-sm font-semibold text-primary hover:underline">
              {authorName || "Anonymous User"}
            </Link>
            <p className="text-xs text-muted-foreground flex items-center">
              <Clock size={12} className="mr-1" /> {new Date(createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
        <Link to={`/banter/${id}`} className="block hover:no-underline">
          <h3 className="text-lg md:text-xl font-semibold text-foreground hover:text-primary transition-colors">
            {title}
          </h3>
        </Link>
      </div>

      <Link to={`/banter/${id}`} className="block hover:no-underline">
        <div className="modern-card-content pt-0">
          <p className="text-sm text-muted-foreground line-clamp-3 mb-3">
            {details}
          </p>
          {displayTags.length > 0 && (
            <div className="flex flex-wrap gap-1.5 mb-1">
              {displayTags.map((tag, index) => (
                <button 
                  key={index} 
                  onClick={(e) => handleTagClick(e, Object.keys(tags).find(key => tags[key] === tag), tag)}
                  className="px-2 py-0.5 text-xs bg-secondary text-secondary-foreground rounded-full flex items-center hover:bg-primary/20 transition-colors focus:outline-none focus:ring-1 focus:ring-primary"
                >
                  <Tag size={10} className="mr-1 opacity-70" /> {tag}
                </button>
              ))}
            </div>
          )}
        </div>

        <div className="modern-card-footer flex justify-between items-center text-xs text-muted-foreground">
          <div className="flex items-center space-x-3">
            <span className="flex items-center">
              <ThumbsUp size={14} className="mr-0.5 text-green-500" /> 
              <span className="hidden sm:inline mr-0.5">Valid Points:</span> {upvotes}
            </span>
             <span className="flex items-center">
              <ThumbsDown size={14} className="mr-0.5 text-red-500" /> 
              <span className="hidden sm:inline mr-0.5">Trash:</span> {downvotes || 0}
            </span>
            <span className="flex items-center">
              <MessageSquare size={14} className="mr-0.5 text-blue-500" /> 
              <span className="hidden sm:inline mr-0.5">Replies:</span> {comments.length}
            </span>
          </div>
          <span className="text-primary hover:underline font-medium">Join the Banter &rarr;</span>
        </div>
      </Link>
    </motion.div>
  );
};

export default BanterCard;