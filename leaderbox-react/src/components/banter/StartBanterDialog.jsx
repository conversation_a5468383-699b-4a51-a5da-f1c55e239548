import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tag, Users, MapPin, Flag, BookOpen, Link as LinkIcon, Paperclip } from 'lucide-react';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { Combobox } from '@/components/ui/combobox';
import MediaUpload from '@/components/ui/MediaUpload.jsx'; // Added

const StartBanterDialog = ({ isOpen, onClose, onSubmit }) => {
  const [title, setTitle] = useState('');
  const [details, setDetails] = useState('');
  const [tagLeader, setTagLeader] = useState('');
  const [tagLocation, setTagLocation] = useState('');
  const [tagParty, setTagParty] = useState('');
  const [tagTopic, setTagTopic] = useState('');
  const [sourceLink, setSourceLink] = useState('');
  const [attachedMedia, setAttachedMedia] = useState([]); // Added

  const { leaders } = useLeaderData();

  const leaderOptions = useMemo(() => leaders.map(l => ({ value: l.name, label: l.name })), [leaders]);
  
  const stateOptions = useMemo(() => {
    const states = new Set(leaders.map(l => l.state).filter(Boolean));
    return Array.from(states).map(s => ({ value: s, label: s }));
  }, [leaders]);

  const partyOptions = useMemo(() => {
    const parties = new Set(leaders.map(l => l.party).filter(Boolean));
    return Array.from(parties).map(p => ({ value: p, label: p }));
  }, [leaders]);


  const handleSubmit = (e) => {
    e.preventDefault();
    if (!title.trim() || !details.trim()) {
      alert("Title and Details are required.");
      return;
    }
    onSubmit({
      title,
      details,
      tags: {
        leader: tagLeader,
        location: tagLocation,
        party: tagParty,
        topic: tagTopic,
      },
      sourceLink,
      media: attachedMedia, // Added
    });
    setTitle('');
    setDetails('');
    setTagLeader('');
    setTagLocation('');
    setTagParty('');
    setTagTopic('');
    setSourceLink('');
    setAttachedMedia([]); // Added
    onClose();
  };

  const handleMediaChange = (files) => {
    setAttachedMedia(files.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
      // For localStorage, actual file content isn't stored, just metadata
      // For a real backend, you'd upload and get a URL here.
      url: URL.createObjectURL(file) // Temporary URL for client-side preview/handling
    })));
  };


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg md:max-w-xl bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-2xl flex items-center">
            <Tag size={24} className="mr-2" /> Start a New Banter
          </DialogTitle>
          <DialogDescription>
            Ignite the conversation! Share your thoughts, questions, or observations. Keep it civil and constructive.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
          <div>
            <Label htmlFor="banter-title" className="text-foreground font-semibold">Banter Title</Label>
            <Input
              id="banter-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="e.g., Fuel Subsidy: Necessary Pain or Policy Error?"
              className="modern-input mt-1"
              required
            />
          </div>
          <div>
            <Label htmlFor="banter-details" className="text-foreground font-semibold">Details / Your Perspective</Label>
            <Textarea
              id="banter-details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              placeholder="Elaborate on your topic, ask a question, or share your perspective..."
              className="modern-input mt-1 min-h-[120px]"
              required
            />
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
            <div>
              <Label htmlFor="tag-leader" className="text-foreground flex items-center"><Users size={14} className="mr-1.5 text-muted-foreground" /> Tag Leader (Optional)</Label>
              <Combobox
                options={leaderOptions}
                value={tagLeader}
                onChange={setTagLeader}
                placeholder="Select or type leader..."
                inputClassName="modern-input mt-1"
              />
            </div>
            <div>
              <Label htmlFor="tag-location" className="text-foreground flex items-center"><MapPin size={14} className="mr-1.5 text-muted-foreground" /> Tag State/Location (Optional)</Label>
               <Combobox
                options={stateOptions}
                value={tagLocation}
                onChange={setTagLocation}
                placeholder="Select or type state..."
                inputClassName="modern-input mt-1"
              />
            </div>
            <div>
              <Label htmlFor="tag-party" className="text-foreground flex items-center"><Flag size={14} className="mr-1.5 text-muted-foreground" /> Tag Party (Optional)</Label>
               <Combobox
                options={partyOptions}
                value={tagParty}
                onChange={setTagParty}
                placeholder="Select or type party..."
                inputClassName="modern-input mt-1"
              />
            </div>
            <div>
              <Label htmlFor="tag-topic" className="text-foreground flex items-center"><BookOpen size={14} className="mr-1.5 text-muted-foreground" /> Tag Topic (Optional)</Label>
              <Input id="tag-topic" value={tagTopic} onChange={(e) => setTagTopic(e.target.value)} placeholder="e.g., Economy, Security" className="modern-input mt-1" />
            </div>
          </div>
          <div>
            <Label htmlFor="source-link" className="text-foreground flex items-center"><LinkIcon size={14} className="mr-1.5 text-muted-foreground" /> Source Link (Optional)</Label>
            <Input
              id="source-link"
              type="url"
              value={sourceLink}
              onChange={(e) => setSourceLink(e.target.value)}
              placeholder="e.g., https://news-article.com/story"
              className="modern-input mt-1"
            />
          </div>

          <div>
            <Label htmlFor="banter-media" className="text-foreground font-semibold flex items-center">
              <Paperclip size={14} className="mr-1.5 text-muted-foreground" /> Attach Media (Optional)
            </Label>
            <MediaUpload onFilesChange={handleMediaChange} />
          </div>

          <p className="text-xs text-muted-foreground mt-2">Tags help others find your banter. Use relevant keywords.</p>
        </form>
        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="submit" onClick={handleSubmit} className="btn-primary">
            Submit Banter
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StartBanterDialog;