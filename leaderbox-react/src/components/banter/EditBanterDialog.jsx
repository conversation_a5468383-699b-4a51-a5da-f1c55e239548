
import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tag, Edit3 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { Combobox } from '@/components/ui/combobox';
import MediaUpload from '@/components/ui/MediaUpload.jsx';

const EditBanterDialog = ({ isOpen, onClose, banter, onSubmit }) => {
  const [formData, setFormData] = useState({});
  const { toast } = useToast();
  const { leaders } = useLeaderData();

  const leaderOptions = useMemo(() => leaders.map(l => ({ value: l.name, label: l.name })), [leaders]);
  const stateOptions = useMemo(() => {
    const states = new Set(leaders.map(l => l.state).filter(Boolean));
    return Array.from(states).map(s => ({ value: s, label: s }));
  }, [leaders]);
  const partyOptions = useMemo(() => {
    const parties = new Set(leaders.map(l => l.party).filter(Boolean));
    return Array.from(parties).map(p => ({ value: p, label: p }));
  }, [leaders]);

  useEffect(() => {
    if (banter) {
      setFormData({
        title: banter.title || '',
        details: banter.details || '',
        tags: {
          leader: banter.tags?.leader || '',
          location: banter.tags?.location || '',
          party: banter.tags?.party || '',
          topic: banter.tags?.topic || '',
        },
        sourceLink: banter.sourceLink || '',
        media: banter.media || [],
      });
    }
  }, [banter, isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTagChange = (tagName, value) => {
    setFormData(prev => ({...prev, tags: {...prev.tags, [tagName]: value}}));
  };
  
  const handleMediaChange = (files) => {
     setFormData(prev => ({ ...prev, media: files.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        url: URL.createObjectURL(file) 
      })) 
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.title?.trim() || !formData.details?.trim()) {
      toast({ title: "Error", description: "Title and details cannot be empty.", variant: "destructive" });
      return;
    }
    onSubmit(formData);
    onClose();
  };
  
  if (!banter) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg md:max-w-xl bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-2xl flex items-center">
            <Edit3 size={24} className="mr-2" /> Edit Your Banter
          </DialogTitle>
          <DialogDescription>
            Refine your post. You can edit the content and tags of your banter here.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
          <div>
            <Label htmlFor="edit-banter-title" className="text-foreground font-semibold">Banter Title</Label>
            <Input
              id="edit-banter-title"
              name="title"
              value={formData.title || ''}
              onChange={handleChange}
              className="modern-input mt-1"
              required
            />
          </div>
          <div>
            <Label htmlFor="edit-banter-details" className="text-foreground font-semibold">Details</Label>
            <Textarea
              id="edit-banter-details"
              name="details"
              value={formData.details || ''}
              onChange={handleChange}
              className="modern-input mt-1 min-h-[120px]"
              required
            />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
            <div>
              <Label>Tag Leader</Label>
              <Combobox options={leaderOptions} value={formData.tags?.leader} onChange={(val) => handleTagChange('leader', val)} placeholder="Select leader..."/>
            </div>
            <div>
              <Label>Tag State</Label>
              <Combobox options={stateOptions} value={formData.tags?.location} onChange={(val) => handleTagChange('location', val)} placeholder="Select state..."/>
            </div>
             <div>
              <Label>Tag Party</Label>
              <Combobox options={partyOptions} value={formData.tags?.party} onChange={(val) => handleTagChange('party', val)} placeholder="Select party..."/>
            </div>
             <div>
              <Label>Tag Topic</Label>
              <Input value={formData.tags?.topic || ''} onChange={(e) => handleTagChange('topic', e.target.value)} placeholder="e.g. Economy" />
            </div>
          </div>
          <div>
            <Label>Media</Label>
            <MediaUpload onFilesChange={handleMediaChange} existingFiles={formData.media} />
          </div>
        </form>
        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="submit" onClick={handleSubmit} className="btn-primary">
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditBanterDialog;
