import React, { useState } from 'react';
import { FileImage as <PERSON><PERSON><PERSON>Lucide, Video as <PERSON>IconLucide, FileText as FileTextIconLucide, Paperclip as PaperclipIconLucide } from 'lucide-react';
import ImageViewerModal from '@/components/ui/ImageViewerModal.jsx';

const getMediaIconElement = (fileName, type) => {
  const commonClass = "h-5 w-5 flex-shrink-0";
  if (type?.startsWith('image/')) return <ImageIconLucide className={`${commonClass} text-blue-500`} />;
  if (type?.startsWith('video/')) return <VideoIconLucide className={`${commonClass} text-purple-500`} />;
  if (type === 'application/pdf') return <FileTextIconLucide className={`${commonClass} text-red-500`} />;
  if (type === 'application/msword' || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return <FileTextIconLucide className={`${commonClass} text-sky-500`} />;
  
  if (/\.(jpe?g|png|gif|webp)$/i.test(fileName)) return <ImageIconLucide className={`${commonClass} text-blue-500`} />;
  if (/\.(mp4|mov|avi|wmv)$/i.test(fileName)) return <VideoIconLucide className={`${commonClass} text-purple-500`} />;
  if (/\.(pdf)$/i.test(fileName)) return <FileTextIconLucide className={`${commonClass} text-red-500`} />;
  if (/\.(doc|docx)$/i.test(fileName)) return <FileTextIconLucide className={`${commonClass} text-sky-500`} />;
  return <PaperclipIconLucide className={`${commonClass} text-gray-500`} />;
};

const MediaDisplayItem = ({ mediaItem }) => {
  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState('');

  if (!mediaItem || !mediaItem.name) return null;

  const isImage = mediaItem.type?.startsWith('image/') || /\.(jpe?g|png|gif|webp)$/i.test(mediaItem.name);
  const displayUrl = mediaItem.url || mediaItem.previewUrl;

  const handleImageClick = (e) => {
    if (isImage && displayUrl) {
      e.preventDefault(); 
      setSelectedImageUrl(displayUrl);
      setIsImageViewerOpen(true);
    }
  };

  const commonClasses = "flex items-center gap-1.5 text-xs p-1.5 bg-secondary/60 rounded-md hover:bg-secondary transition-colors max-w-[150px] sm:max-w-[180px] truncate text-left w-full";

  const content = (
    <>
      {isImage && displayUrl ? (
        <img src={displayUrl} alt={mediaItem.name} className="h-6 w-6 rounded object-cover flex-shrink-0" />
      ) : (
        getMediaIconElement(mediaItem.name, mediaItem.type)
      )}
      <span className="truncate text-foreground/90">{mediaItem.name}</span>
    </>
  );

  return (
    <>
      {isImage ? (
        <button type="button" onClick={handleImageClick} className={commonClasses} title={`View ${mediaItem.name}`}>
          {content}
        </button>
      ) : (
        <a 
          href={displayUrl || '#'} 
          target="_blank" 
          rel="noopener noreferrer"
          className={commonClasses}
          title={`Open ${mediaItem.name}`}
        >
          {content}
        </a>
      )}
      {isImage && (
        <ImageViewerModal 
          isOpen={isImageViewerOpen} 
          onClose={() => setIsImageViewerOpen(false)} 
          imageUrl={selectedImageUrl} 
          imageAlt={mediaItem.name}
        />
      )}
    </>
  );
};

export default MediaDisplayItem;