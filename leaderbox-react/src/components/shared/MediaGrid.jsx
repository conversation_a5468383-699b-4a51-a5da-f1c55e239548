import React, { useState } from 'react';
import ImageViewerModal from '@/components/ui/ImageViewerModal.jsx';
import { cn } from '@/lib/utils.js';

const MediaItem = ({ item, onClick, className, overlayText }) => {
  const isImage = item.type?.startsWith('image/') || /\.(jpe?g|png|gif|webp|svg)$/i.test(item.name);
  const isVideo = item.type?.startsWith('video/') || /\.(mp4|mov|avi|wmv)$/i.test(item.name);

  return (
    <div className={cn("relative w-full h-full cursor-pointer overflow-hidden", className)} onClick={onClick}>
      {isImage ? (
        <img src={item.url} alt={item.name} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
      ) : isVideo ? (
        <video src={item.url} className="w-full h-full object-cover" controls={false} />
      ) : (
        <div className="w-full h-full bg-secondary flex items-center justify-center">
          <p className="text-xs text-muted-foreground p-2">{item.name}</p>
        </div>
      )}
      {overlayText && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
          <span className="text-white text-2xl font-bold">{overlayText}</span>
        </div>
      )}
    </div>
  );
};

const MediaGrid = ({ mediaItems }) => {
  const [viewerState, setViewerState] = useState({ isOpen: false, imageUrl: '' });

  if (!mediaItems || mediaItems.length === 0) {
    return null;
  }
  
  const handleItemClick = (item) => {
    const isImage = item.type?.startsWith('image/') || /\.(jpe?g|png|gif|webp|svg)$/i.test(item.name);
    if (isImage) {
      setViewerState({ isOpen: true, imageUrl: item.url });
    } else {
      // For non-image files, maybe open in new tab
      window.open(item.url, '_blank');
    }
  };
  
  const count = mediaItems.length;

  const baseClasses = "group aspect-square";

  const renderGrid = () => {
    switch (count) {
      case 1:
        return (
          <div className="grid grid-cols-1 aspect-[16/9]">
            <MediaItem item={mediaItems[0]} onClick={() => handleItemClick(mediaItems[0])} className="aspect-auto" />
          </div>
        );
      case 2:
        return (
          <div className="grid grid-cols-2 gap-0.5 aspect-[16/9]">
            <MediaItem item={mediaItems[0]} onClick={() => handleItemClick(mediaItems[0])} className={baseClasses} />
            <MediaItem item={mediaItems[1]} onClick={() => handleItemClick(mediaItems[1])} className={baseClasses} />
          </div>
        );
      case 3:
        return (
          <div className="grid grid-cols-2 grid-rows-2 gap-0.5 aspect-[16/9]">
            <div className="row-span-2">
              <MediaItem item={mediaItems[0]} onClick={() => handleItemClick(mediaItems[0])} className="h-full" />
            </div>
            <MediaItem item={mediaItems[1]} onClick={() => handleItemClick(mediaItems[1])} className={baseClasses} />
            <MediaItem item={mediaItems[2]} onClick={() => handleItemClick(mediaItems[2])} className={baseClasses} />
          </div>
        );
      default: // 4 or more
        return (
          <div className="grid grid-cols-2 grid-rows-2 gap-0.5 aspect-square">
            {mediaItems.slice(0, 4).map((item, index) => (
              <MediaItem 
                key={item.url || index} 
                item={item} 
                onClick={() => handleItemClick(item)} 
                className={baseClasses}
                overlayText={index === 3 && count > 4 ? `+${count - 4}` : null}
              />
            ))}
          </div>
        );
    }
  };

  return (
    <>
      {renderGrid()}
      <ImageViewerModal
        isOpen={viewerState.isOpen}
        onClose={() => setViewerState({ isOpen: false, imageUrl: '' })}
        imageUrl={viewerState.imageUrl}
      />
    </>
  );
};

export default MediaGrid;