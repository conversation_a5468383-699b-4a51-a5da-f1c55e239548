import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { X, Image as ImageIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const ProfileImageReminder = ({ onDismiss }) => {
  const navigate = useNavigate();

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50, height: 0 }}
        transition={{ duration: 0.5, ease: 'easeInOut' }}
        className="bg-primary/10 border-b border-primary/20 text-primary-foreground p-2 text-center text-sm z-40 relative"
      >
        <div className="container mx-auto flex justify-center items-center gap-2">
          <ImageIcon className="h-5 w-5 text-primary" />
          <span className="text-foreground">Your profile is missing a picture!</span>
          <Button
            size="sm"
            variant="link"
            className="text-primary font-semibold hover:underline"
            onClick={() => navigate('/settings')}
          >
            Add one now
          </Button>
          <Button
            size="icon"
            variant="ghost"
            className="h-7 w-7 absolute right-2 top-1/2 -translate-y-1/2 text-foreground/70 hover:text-foreground"
            onClick={onDismiss}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ProfileImageReminder;