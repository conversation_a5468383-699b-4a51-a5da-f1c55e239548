
import React, { useState } from 'react';
import { NavLink, Outlet, Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import {
  LayoutDashboard,
  Users,
  UserCheck,
  BarChart2,
  MessageSquare,
  ShieldCheck,
  FileText,
  Settings,
  Menu,
  Bell,
  Mail,
  Lightbulb,
  UserPlus,
  Tag,
  BookOpen,
  Group,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const navLinks = [
  { to: '/admin', icon: LayoutDashboard, text: 'Dashboard' },
  {
    category: 'Content Management',
    links: [
      { to: '/admin/leaders', icon: User<PERSON><PERSON><PERSON>, text: 'Leaders' },
      { to: '/admin/suggested-leaders', icon: UserPlus, text: 'Suggested Leaders' },
      { to: '/admin/polls', icon: BarChart2, text: 'Polls' },
      { to: '/admin/petitions', icon: BookOpen, text: 'Petitions' },
      { to: '/admin/banters', icon: MessageSquare, text: 'Banter Room' },
      { to: '/admin/groups', icon: Group, text: 'Groups' },
      { to: '/admin/profile-categories', icon: Tag, text: 'Profile Categories' },
    ],
  },
  {
    category: 'User & Engagement',
    links: [
      { to: '/admin/registered-users', icon: Users, text: 'Registered Users' },
      { to: '/admin/moderation', icon: ShieldCheck, text: 'Moderation' },
      { to: '/admin/suggested-edits', icon: Lightbulb, text: 'Suggested Edits' },
      { to: '/admin/metrics', icon: FileText, text: 'Engagement Metrics' },
    ],
  },
  {
    category: 'System',
    links: [
      { to: '/admin/keywords', icon: Tag, text: 'Keyword Management' },
      { to: '/admin/email-settings', icon: Mail, text: 'Email Settings' },
    ],
  },
];

const AdminSidebar = () => (
  <aside className="hidden lg:block w-64 bg-card border-r border-border p-4">
    <div className="flex flex-col h-full">
      <div className="mb-6">
        <Link to="/" className="flex items-center gap-2">
          <img  alt="LeaderBox Logo" className="h-8 w-auto" src="https://images.unsplash.com/photo-1485803278843-6547a34a0d1b" />
          <span className="text-xl font-bold text-primary">LeaderBox Admin</span>
        </Link>
      </div>
      <nav className="flex-grow space-y-4 overflow-y-auto">
        {navLinks.map((item, index) =>
          item.category ? (
            <div key={index}>
              <h3 className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                {item.category}
              </h3>
              <div className="space-y-1">
                {item.links.map((link) => (
                  <NavLink
                    key={link.to}
                    to={link.to}
                    end={link.to === '/admin'}
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        isActive
                          ? 'bg-primary/10 text-primary'
                          : 'text-muted-foreground hover:bg-secondary/50 hover:text-foreground'
                      }`
                    }
                  >
                    <link.icon className="mr-3 h-5 w-5" />
                    <span>{link.text}</span>
                  </NavLink>
                ))}
              </div>
            </div>
          ) : (
            <NavLink
              key={item.to}
              to={item.to}
              end={item.to === '/admin'}
              className={({ isActive }) =>
                `flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  isActive
                    ? 'bg-primary/10 text-primary'
                    : 'text-muted-foreground hover:bg-secondary/50 hover:text-foreground'
                }`
              }
            >
              <item.icon className="mr-3 h-5 w-5" />
              <span>{item.text}</span>
            </NavLink>
          )
        )}
      </nav>
    </div>
  </aside>
);

const AdminHeader = () => {
  const { user, logout } = useAuth();
  return (
    <header className="sticky top-0 z-40 flex h-16 items-center justify-between lg:justify-end gap-4 border-b bg-card px-4 md:px-6">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="lg:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 bg-card p-4">
          <AdminSidebar />
        </SheetContent>
      </Sheet>
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" className="rounded-full">
          <Bell className="h-5 w-5" />
          <span className="sr-only">Toggle notifications</span>
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-10 w-10 rounded-full">
              <Avatar className="h-10 w-10">
                <AvatarImage src={user?.avatarUrl} alt={user?.name} />
                <AvatarFallback>{user?.name ? user.name.substring(0, 1).toUpperCase() : 'A'}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link to="/settings">Settings</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/">View Site</Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

const AdminLayout = () => {
  return (
    <div className="min-h-screen w-full flex bg-background">
      <AdminSidebar />
      <div className="flex flex-col flex-1">
        <AdminHeader />
        <main className="flex-1 p-4 md:p-6 lg:p-8">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
