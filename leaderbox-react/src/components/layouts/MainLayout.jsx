import React from 'react';
import { Outlet, Link, useNavigate, NavLink } from 'react-router-dom';
import { Home, BarChart2, FileText, MessageSquare, User as UserIcon, LogIn, UserPlus, LogOut, Bell, Settings, Shield, LayoutDashboard, Mail, Users } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext.jsx'; 
import { useAppState } from '@/contexts/AppStateContext.jsx';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import { ScrollArea } from '@/components/ui/scroll-area';
import ProfileImageReminder from '@/components/shared/ProfileImageReminder.jsx';

const MainLayout = () => {
  const { user, logout } = useAuth(); 
  const { notifications, removeNotification, isProfileReminderDismissed, dismissProfileReminder } = useAppState();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleNotificationItemClick = (notification) => {
    toast({
      title: notification.title || "Notification",
      description: notification.message,
    });
    removeNotification(notification.id); 
  };

  const baseNavItems = [
    { path: "/", label: "Home", icon: Home },
    { path: "/polls", label: "Polls", icon: BarChart2 },
    { path: "/petitions", label: "Petitions", icon: FileText },
    { path: "/banter-room", label: "Banter Room", icon: MessageSquare },
    { path: "/groups", label: "Groups", icon: Users },
  ];
  
  const desktopNavItems = user 
    ? [
        ...baseNavItems,
        { path: "/dashboard", label: "My Feed", icon: LayoutDashboard },
      ]
    : baseNavItems;


  const mobileNavItems = [
    { path: "/", label: "Home", icon: Home },
    { path: "/polls", label: "Polls", icon: BarChart2 },
    { path: "/petitions", label: "Petitions", icon: FileText },
    { path: "/banter-room", label: "Banter Room", icon: MessageSquare },
    { path: "/groups", label: "Groups", icon: Users },
  ];


  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      {/* Desktop Header */}
      <header className="hidden md:flex header-override p-4 sticky top-0 z-50">
         <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-3xl font-extrabold logo-override">
            LeaderBox
          </Link>
          <nav className="flex space-x-6 items-center">
            {desktopNavItems.map(item => (
              <NavLink 
                key={item.path} 
                to={item.path} 
                className={({ isActive }) => 
                  `text-sm font-medium transition-colors hover:text-primary ${isActive ? 'text-primary' : 'text-muted-foreground'}`
                }
              >
                {item.label}
              </NavLink>
            ))}
          </nav>
          <div className="flex items-center space-x-3">
            {!user ? (
              <>
                <Button variant="ghost" onClick={() => navigate('/login')} className="text-primary hover:bg-primary/10">
                  <LogIn size={18} className="mr-2" /> Login
                </Button>
                <Button onClick={() => navigate('/register')} className="btn-primary rounded-full px-6 py-2 text-sm">
                  <UserPlus size={18} className="mr-2" /> Register
                </Button>
              </>
            ) : (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary relative">
                      <Bell size={20} />
                      {notifications.length > 0 && (
                        <span className="absolute -top-1 -right-1 flex h-3 w-3">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                        </span>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-80" align="end">
                    <DropdownMenuLabel className="flex justify-between items-center">
                      Notifications 
                      {notifications.length > 0 && <span className="text-xs text-primary">({notifications.length} new)</span>}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {notifications.length > 0 ? (
                      <ScrollArea className="h-[200px]">
                        {notifications.map(notif => (
                          <DropdownMenuItem key={notif.id} onClick={() => handleNotificationItemClick(notif)} className="text-xs cursor-pointer">
                            <div className="flex flex-col">
                                <span className="font-medium">{notif.title || 'New Update'}</span>
                                <span className="text-muted-foreground">{notif.message}</span>
                            </div>
                          </DropdownMenuItem>
                        ))}
                      </ScrollArea>
                    ) : (
                      <DropdownMenuItem disabled className="text-xs text-center text-muted-foreground">No new notifications</DropdownMenuItem>
                    )}
                     <DropdownMenuSeparator />
                     <DropdownMenuItem onClick={() => notifications.forEach(n => removeNotification(n.id))} className="text-xs text-center cursor-pointer hover:bg-secondary">Mark all as read</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary" onClick={() => navigate(user ? `/messages` : '/login')}>
                  <Mail size={20} />
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                      <Avatar className="h-9 w-9">
                        <AvatarImage src={user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=40`} alt={user.name} />
                        <AvatarFallback>{user.name ? user.name.substring(0,1).toUpperCase() : user.email.substring(0,1).toUpperCase()}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{user.name || "User"}</p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => navigate(`/user/${user.id}`)}>
                      <UserIcon className="mr-2 h-4 w-4" />
                      <span>My Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/dashboard')}>
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      <span>My Feed</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/settings')}>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                    {user.isAdmin && ( 
                       <DropdownMenuItem onClick={() => navigate('/admin')}>
                        <Shield className="mr-2 h-4 w-4" />
                        <span>Admin Panel</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </div>
      </header>
      
      {/* Mobile Header */}
      <header className="md:hidden header-override p-3 sticky top-0 z-50">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-extrabold logo-override">
            LeaderBox
          </Link>
          <div className="flex items-center space-x-1">
             {!user ? (
              <>
                <Button variant="ghost" size="sm" onClick={() => navigate('/login')} className="text-primary px-2">
                  <LogIn size={20} />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => navigate('/register')} className="text-accent px-2">
                  <UserPlus size={20} />
                </Button>
              </>
            ) : (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary relative">
                      <Bell size={22} />
                       {notifications.length > 0 && (
                        <span className="absolute -top-0.5 -right-0.5 flex h-2.5 w-2.5">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-2.5 w-2.5 bg-red-500"></span>
                        </span>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64" align="end">
                    <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                     {notifications.length > 0 ? (
                      <ScrollArea className="h-[150px]">
                        {notifications.map(notif => (
                          <DropdownMenuItem key={notif.id} onClick={() => handleNotificationItemClick(notif)} className="text-xs">
                            {notif.message}
                          </DropdownMenuItem>
                        ))}
                      </ScrollArea>
                    ) : (
                      <DropdownMenuItem disabled className="text-xs text-center text-muted-foreground">No new notifications</DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                     <DropdownMenuItem onClick={() => notifications.forEach(n => removeNotification(n.id))} className="text-xs text-center cursor-pointer hover:bg-secondary">Mark all as read</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary" onClick={() => navigate(user ? `/messages` : '/login')}>
                  <Mail size={22} />
                </Button>

                 <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-9 w-9 rounded-full p-0">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=32`} alt={user.name} />
                        <AvatarFallback>{user.name ? user.name.substring(0,1).toUpperCase() : user.email.substring(0,1).toUpperCase()}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-48" align="end" forceMount>
                     <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{user.name || "User"}</p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => navigate(`/user/${user.id}`)}>My Profile</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/dashboard')}>My Feed</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/settings')}>Settings</DropdownMenuItem>
                     {user.isAdmin && (
                       <DropdownMenuItem onClick={() => navigate('/admin')}>Admin Panel</DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={handleLogout}>Log out</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </div>
      </header>

      {user && !user.avatarUrl && !isProfileReminderDismissed && (
        <ProfileImageReminder onDismiss={dismissProfileReminder} />
      )}

      <main className="flex-grow page-container pb-[calc(theme(spacing.24)+60px)] md:pb-8">
        <Outlet />
      </main>

      {/* Mobile Bottom Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 mobile-nav-override p-2 flex justify-around items-center z-40">
        {mobileNavItems.map(item => (
          <NavLink 
            key={item.path} 
            to={item.path} 
            className={({ isActive }) => 
              `flex flex-col items-center p-1 rounded-md transition-colors w-1/5 ${isActive ? 'text-primary bg-primary/10' : 'text-muted-foreground hover:text-primary hover:bg-primary/5'}`
            }
          >
            {({ isActive }) => ( 
              <>
                <item.icon size={22} strokeWidth={isActive ? 2.5 : 2} />
                <span className={`mt-0.5 text-[11px] text-center leading-tight whitespace-nowrap ${isActive ? 'font-semibold' : 'font-normal'}`}>{item.label}</span>
              </>
            )}
          </NavLink>
        ))}
      </nav>

      {/* Desktop Footer */}
      <footer className="hidden md:block footer-override p-6 text-center">
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
          <p className="text-xs mb-4 md:mb-0">&copy; {new Date().getFullYear()} LeaderBox. All rights reserved.</p>
          <div className="flex flex-wrap justify-center items-center space-x-4">
            <Link to="/about" className="text-sm">About</Link>
            <Link to="/contact" className="text-sm">Contact</Link>
            <Link to="/terms" className="text-sm">Terms</Link>
            <Link to="/privacy" className="text-sm">Privacy</Link>
            <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-sm">Facebook</a>
            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-sm">Twitter</a>
            <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-sm">Instagram</a>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MainLayout;