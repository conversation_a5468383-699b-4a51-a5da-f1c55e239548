import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Youtube } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const LeaderBioCard = ({ leader }) => {
  const [showDetailedBio, setShowDetailedBio] = useState(false);

  if (!leader) return null;

  const detailedBioText = leader.detailedBio || `This leader has not provided a detailed biography yet. A detailed biography provides deeper insight into a leader's career, accomplishments, and political journey. It can include information about their early life, education, pre-political career, key legislative achievements, major projects undertaken, and their stance on various critical issues. For example, it might detail their involvement in landmark bills, their role in national or international events, or their vision for their constituency and the nation. This extended information helps citizens make more informed decisions and understand the context behind a leader's actions and policies.`;

  const getYouTubeEmbedUrl = (url) => {
    if (!url) return null;
    let videoId = null;
    try {
      const urlObj = new URL(url);
      if (urlObj.hostname === 'youtu.be') {
        videoId = urlObj.pathname.slice(1);
      } else if (urlObj.hostname.includes('youtube.com')) {
        if (urlObj.pathname === '/watch') {
          videoId = urlObj.searchParams.get('v');
        } else if (urlObj.pathname.startsWith('/embed/')) {
          videoId = urlObj.pathname.split('/embed/')[1];
        }
      }
    } catch (e) {
      // Handle cases where the URL is just an ID
      const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
      const match = url.match(regex);
      if (match) {
        videoId = match[1];
      } else {
        console.error("Invalid YouTube URL:", url);
        return null;
      }
    }
    return videoId ? `https://www.youtube.com/embed/${videoId}` : null;
  };
  
  const youtubeEmbedUrl = getYouTubeEmbedUrl(leader.youtubeVideoUrl || leader.profileVideoUrl);

  return (
    <Card className="modern-card">
      <CardHeader className="modern-card-header">
        <CardTitle className="text-xl text-primary">Biography & Profile</CardTitle>
      </CardHeader>
      <CardContent className="modern-card-content space-y-4 text-foreground/90">
        
        {youtubeEmbedUrl && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-foreground mb-2 flex items-center"><Youtube className="mr-2 text-red-600" /> Profile Video</h3>
            <div className="aspect-video rounded-lg overflow-hidden shadow-lg border border-border">
              <iframe
                width="100%"
                height="100%"
                src={youtubeEmbedUrl}
                title={`${leader.name} - Profile Video`}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
              ></iframe>
            </div>
          </div>
        )}

        <div>
            <h3 className="text-lg font-semibold text-foreground mb-1">Bio Summary</h3>
            <p className="whitespace-pre-line">{leader.bio || 'N/A'}</p>
        </div>
        
        <Button 
          variant="link" 
          onClick={() => setShowDetailedBio(!showDetailedBio)}
          className="px-0 text-primary hover:text-primary/80"
        >
          {showDetailedBio ? 'Hide Full Bio' : 'View Full Bio'}
          {showDetailedBio ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
        </Button>

        <AnimatePresence>
          {showDetailedBio && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="pt-3 border-t border-border mt-2">
                <h3 className="text-lg font-semibold text-foreground mb-1">Full Biography</h3>
                <div className="whitespace-pre-line prose prose-sm dark:prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: detailedBioText.replace(/\n/g, '<br />') }} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        <div className="pt-3 border-t border-border mt-2">
            <h3 className="text-lg font-semibold text-foreground mb-1">Education</h3>
            <p>{leader.education || 'N/A'}</p>
        </div>
        <div className="pt-3 border-t border-border mt-2">
            <h3 className="text-lg font-semibold text-foreground mb-1">Background</h3>
            <p>{leader.background || 'N/A'}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default LeaderBioCard;