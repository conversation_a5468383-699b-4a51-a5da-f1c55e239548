import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Send } from 'lucide-react';

const DirectMessageDialog = ({ isOpen, onClose, leaderName }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary flex items-center">
            <Send size={20} className="mr-2"/> Message {leaderName}
          </DialogTitle>
          <DialogDescription className="pt-2">
            This leader has not claimed their account yet. The direct messaging feature will be available soon once accounts are verified and claimed.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 text-center text-muted-foreground">
          <p>(Feature Coming Soon)</p>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DirectMessageDialog;