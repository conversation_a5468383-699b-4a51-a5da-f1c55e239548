import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, UserPlus, UserCheck, Send } from 'lucide-react';

const LeaderEngagementCard = ({ leaderName, isFollowing, onFollowToggle, onRate, canRateAgain, userRatingDetails, onSendMessage }) => {
  return (
    <Card className="modern-card md:col-span-2">
      <CardHeader className="modern-card-header pb-2">
        <CardTitle className="text-xl text-primary">Engage with {leaderName}</CardTitle>
      </CardHeader>
      <CardContent className="modern-card-content space-y-3">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          <Button 
            onClick={onFollowToggle} 
            className={`w-full transition-colors duration-200 ease-in-out text-base py-3 ${
              isFollowing ? 'btn-destructive' : 'btn-primary'
            }`}
          >
            {isFollowing ? <UserCheck className="mr-2 h-5 w-5" /> : <UserPlus className="mr-2 h-5 w-5" />}
            {isFollowing ? 'Unfollow' : 'Follow'}
          </Button>
          <Button 
            onClick={onRate} 
            className="w-full btn-accent text-base py-3"
            disabled={!canRateAgain && !!userRatingDetails}
          >
            <Star className="mr-2 h-5 w-5" />
            {userRatingDetails ? (canRateAgain ? 'Update Rating' : 'Rated') : 'Rate Leader'}
          </Button>
          <Button 
            onClick={onSendMessage}
            variant="outline"
            className="w-full text-base py-3 border-primary text-primary hover:bg-primary/10 hover:text-primary"
          >
            <Send className="mr-2 h-5 w-5" />
            Send Message
          </Button>
        </div>
        {userRatingDetails && !canRateAgain && (
            <p className="text-xs text-center text-muted-foreground">You can update your rating in 24 hours.</p>
        )}
        {userRatingDetails && (
            <p className="text-sm text-center text-muted-foreground mt-2">
              Your last rating: {userRatingDetails.rating} stars. 
              {userRatingDetails.comment && ` Comment: "${userRatingDetails.comment}"`}
            </p>
        )}
      </CardContent>
    </Card>
  );
};

export default LeaderEngagementCard;