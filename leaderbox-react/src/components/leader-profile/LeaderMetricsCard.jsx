import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

const LeaderMetricsCard = ({ leader }) => {
  if (!leader) return null;

  const getSentimentBarClassBasedOnRating = (rating) => {
    if (rating >= 4) return 'sentiment-rating-positive'; // Green
    if (rating >= 3) return 'sentiment-rating-neutral';  // Yellow
    return 'sentiment-rating-negative'; // Red
  };
  
  const getSentimentTextClassBasedOnRating = (rating) => {
    if (rating >= 4) return 'text-green-600';
    if (rating >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  const { positive = 0, neutral = 0, negative = 0 } = leader.sentimentBreakdown || {};
  const totalSentiments = positive + neutral + negative;
  const sentimentPercentages = totalSentiments > 0 ? {
    positive: (positive / totalSentiments) * 100,
    neutral: (neutral / totalSentiments) * 100,
    negative: (negative / totalSentiments) * 100,
  } : { positive: 0, neutral: 100, negative: 0 };

  const currentAvgRating = leader.currentRating || 0;

  return (
    <Card className="modern-card">
      <CardHeader className="modern-card-header pb-2">
        <CardTitle className="text-xl text-primary">Metrics</CardTitle>
      </CardHeader>
      <CardContent className="modern-card-content space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-muted-foreground">Followers:</span>
          <span className="font-semibold text-lg">{leader.followers?.toLocaleString() || 0}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-muted-foreground">Avg. Rating:</span>
          <div className="flex items-center">
            <Star className="h-5 w-5 text-yellow-400 fill-yellow-400 mr-1" />
            <span className="font-semibold text-lg">{currentAvgRating.toFixed(1)} ({leader.totalRatings || 0} ratings)</span>
          </div>
        </div>
        <div>
          <span className="text-muted-foreground text-sm mb-1 block">Overall Sentiment (Based on Avg. Rating):</span>
          <div className="w-full bg-muted rounded-full h-2.5 mt-1 overflow-hidden">
            <div 
              className={cn("h-full", getSentimentBarClassBasedOnRating(currentAvgRating))}
              style={{ width: `${(currentAvgRating / 5) * 100}%` }} 
            ></div>
          </div>
           <p className={cn("text-sm font-medium mt-1", getSentimentTextClassBasedOnRating(currentAvgRating))}>
            {currentAvgRating >= 4 ? 'Positive' : currentAvgRating >= 3 ? 'Neutral' : 'Negative'}
          </p>
        </div>
        <div>
          <span className="text-muted-foreground text-sm mb-1 block">Comment Sentiment Breakdown:</span>
           <div className="flex h-2.5 w-full rounded-full overflow-hidden bg-muted">
            <div className="sentiment-bar-positive" style={{ width: `${sentimentPercentages.positive}%` }} title={`Positive: ${sentimentPercentages.positive.toFixed(1)}%`}></div>
            <div className="sentiment-bar-neutral" style={{ width: `${sentimentPercentages.neutral}%` }} title={`Neutral: ${sentimentPercentages.neutral.toFixed(1)}%`}></div>
            <div className="sentiment-bar-negative" style={{ width: `${sentimentPercentages.negative}%` }} title={`Negative: ${sentimentPercentages.negative.toFixed(1)}%`}></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LeaderMetricsCard;