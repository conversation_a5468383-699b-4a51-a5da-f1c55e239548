import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Edit3 } from 'lucide-react';

const SuggestEditDialog = ({ isOpen, onClose, leader, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    party: '',
    state: '',
    bio: '',
    detailedBio: '',
    education: '',
    background: '',
    profileVideoUrl: '',
    suggestionReason: ''
  });
  const { toast } = useToast();

  useEffect(() => {
    if (leader) {
      setFormData({
        name: leader.name || '',
        position: leader.position || '',
        party: leader.party || '',
        state: leader.state || '',
        bio: leader.bio || '',
        detailedBio: leader.detailedBio || '',
        education: leader.education || '',
        background: leader.background || '',
        profileVideoUrl: leader.profileVideoUrl || '',
        suggestionReason: ''
      });
    }
  }, [leader, isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    if (!formData.suggestionReason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for your suggested edits.",
        variant: "destructive",
      });
      return;
    }
    
    const changes = {};
    for (const key in formData) {
      if (leader && formData[key] !== leader[key] && key !== 'suggestionReason') {
        changes[key] = { old: leader[key], new: formData[key] };
      } else if (!leader && formData[key] && key !== 'suggestionReason') {
         changes[key] = { new: formData[key] };
      }
    }
    
    if (Object.keys(changes).length === 0 && leader) {
        toast({
            title: "No Changes Detected",
            description: "Please make some changes before submitting.",
            variant: "default",
        });
        return;
    }

    onSubmit({ suggestedChanges: formData, reason: formData.suggestionReason, originalData: leader, changedFields: changes });
    onClose(); 
  };

  if (!leader) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-2xl flex items-center">
            <Edit3 size={24} className="mr-2" /> Suggest Edit/Update for {leader.name}
          </DialogTitle>
          <DialogDescription>
            Help keep this profile accurate. Enter your suggested changes below. All suggestions are reviewed by moderators.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="suggest-name">Name</Label>
              <Input id="suggest-name" name="name" value={formData.name} onChange={handleChange} className="modern-input" />
            </div>
            <div>
              <Label htmlFor="suggest-position">Position</Label>
              <Input id="suggest-position" name="position" value={formData.position} onChange={handleChange} className="modern-input" />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="suggest-party">Party</Label>
              <Input id="suggest-party" name="party" value={formData.party} onChange={handleChange} className="modern-input" />
            </div>
            <div>
              <Label htmlFor="suggest-state">State</Label>
              <Input id="suggest-state" name="state" value={formData.state} onChange={handleChange} className="modern-input" />
            </div>
          </div>
          <div>
            <Label htmlFor="suggest-bio">Short Bio</Label>
            <Textarea id="suggest-bio" name="bio" value={formData.bio} onChange={handleChange} className="modern-input min-h-[80px]" />
          </div>
          <div>
            <Label htmlFor="suggest-detailedBio">Detailed Bio</Label>
            <Textarea id="suggest-detailedBio" name="detailedBio" value={formData.detailedBio} onChange={handleChange} className="modern-input min-h-[120px]" />
          </div>
          <div>
            <Label htmlFor="suggest-education">Education</Label>
            <Input id="suggest-education" name="education" value={formData.education} onChange={handleChange} className="modern-input" />
          </div>
          <div>
            <Label htmlFor="suggest-background">Background</Label>
            <Input id="suggest-background" name="background" value={formData.background} onChange={handleChange} className="modern-input" />
          </div>
          <div>
            <Label htmlFor="suggest-profileVideoUrl">YouTube Profile Video URL</Label>
            <Input id="suggest-profileVideoUrl" name="profileVideoUrl" value={formData.profileVideoUrl} onChange={handleChange} className="modern-input" />
          </div>
          <div className="mt-2">
            <Label htmlFor="suggestion-reason" className="font-semibold text-primary">Reason for Suggestion / Source (Required)</Label>
            <Textarea 
              id="suggestion-reason" 
              name="suggestionReason" 
              value={formData.suggestionReason} 
              onChange={handleChange} 
              placeholder="e.g., Leader's official website updated, recent news article link, etc." 
              className="modern-input min-h-[80px] mt-1"
            />
          </div>
        </div>
        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="button" onClick={handleSubmit} className="btn-primary">
            Submit Suggestion
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SuggestEditDialog;