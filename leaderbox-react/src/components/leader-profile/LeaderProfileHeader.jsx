import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ShieldCheck } from 'lucide-react';

const LeaderProfileHeader = ({ leader }) => {
  if (!leader) return null;

  const partyColor = leader.party === 'APC' ? 'bg-blue-500' : 
                     leader.party === 'PDP' ? 'bg-green-500' :
                     leader.party === 'LP' ? 'bg-red-500' :
                     leader.party === 'NNPP' ? 'bg-orange-500' : 'bg-gray-500';

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }}
      className="relative"
    >
      <div className="h-48 md:h-64 w-full rounded-xl md:rounded-2xl overflow-hidden shadow-2xl 
                      bg-gradient-to-br from-primary/70 via-accent/50 to-secondary/60 
                      dark:from-primary/40 dark:via-accent/30 dark:to-secondary/40">
      </div>

      <div className="absolute inset-0 flex flex-col justify-end p-4 md:p-0">
        <div className="md:flex md:items-end md:space-x-6 w-full max-w-5xl mx-auto md:px-8">
          <motion.div 
            initial={{ scale: 0.5, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5, type: "spring", stiffness: 120 }}
            className="relative -mb-12 md:-mb-0 md:mb-[-3rem] z-10 mx-auto md:mx-0"
          >
            <Avatar className="h-32 w-32 md:h-40 md:w-40 border-4 border-card shadow-xl bg-secondary rounded-full">
              <AvatarImage src={leader.avatarUrl || `https://avatar.vercel.sh/${leader.name.replace(/\s/g, "")}.png?size=160`} alt={leader.name} className="rounded-full" />
              <AvatarFallback className="text-6xl text-primary rounded-full">{leader.name.substring(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            {leader.isVerified && (
              <ShieldCheck className="absolute bottom-1 right-1 h-8 w-8 text-blue-500 fill-white bg-card rounded-full p-0.5" />
            )}
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className={cn(
              "flex-grow min-w-0 mt-4 md:mt-0 md:pb-6 md:pl-4",
              "bg-card/80 dark:bg-card/70 backdrop-blur-md shadow-xl rounded-xl md:rounded-t-xl md:rounded-b-none",
              "p-4 pt-16 md:pt-6 text-center md:text-left"
            )}
          >
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold text-foreground truncate flex items-center justify-center md:justify-start" title={leader.name}>
              {leader.name}
            </h1>
            <p className="text-base md:text-lg text-muted-foreground mt-1 truncate" title={leader.position}>{leader.position}</p>
            <div className="flex items-center justify-center md:justify-start space-x-3 mt-2">
              <span className={cn("px-3 py-1 text-xs font-semibold rounded-full text-white", partyColor)}>
                {leader.party}
              </span>
              <span className="text-sm text-muted-foreground truncate" title={leader.state}>{leader.state} State</span>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default LeaderProfileHeader;