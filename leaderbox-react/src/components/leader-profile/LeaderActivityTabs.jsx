import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardFooter } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Star, ThumbsUp, ThumbsDown, Edit3, Trash2, MessageSquare, Send } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { useToast } from '@/components/ui/use-toast';
import { Link } from 'react-router-dom';
import MediaUpload from '@/components/ui/MediaUpload.jsx';
import MediaDisplayItem from '@/components/shared/MediaDisplayItem.jsx';

const CommentItem = ({ comment, user, onReply, onVote, leaderId }) => {
  const { toast } = useToast();
  const { userLeaderCommentVotes } = useLeaderData();
  
  const userVoteStatus = userLeaderCommentVotes && typeof userLeaderCommentVotes === 'object' 
    ? userLeaderCommentVotes[`${leaderId}-${comment.commentId}-${user?.id}`] 
    : null;

  const handleVoteClick = (voteType) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to vote.", variant: "destructive" });
      return;
    }
    onVote(comment.commentId, voteType);
  };

  return (
    <Card className="bg-background/50 border-border shadow-sm">
      <CardHeader className="p-3 pb-1 flex flex-row items-start space-x-3">
        <Link to={`/user/${comment.userId}`}>
          <Avatar className="h-9 w-9">
            <AvatarImage src={comment.userAvatarUrl || `https://avatar.vercel.sh/${comment.userName}.png?size=36`} alt={comment.userName} />
            <AvatarFallback>{comment.userName ? comment.userName.substring(0,1).toUpperCase() : 'U'}</AvatarFallback>
          </Avatar>
        </Link>
        <div>
          <div className="flex items-center gap-2">
            <Link to={`/user/${comment.userId}`} className="font-semibold text-sm text-primary hover:underline">
              {comment.userName}
            </Link>
            {comment.ratingGiven && (
                <span className="flex items-center text-xs bg-yellow-100 text-yellow-700 px-1.5 py-0.5 rounded-sm">
                    <Star size={12} className="mr-1 fill-current"/> {comment.ratingGiven}
                </span>
            )}
          </div>
          <p className="text-xs text-muted-foreground">{new Date(comment.timestamp).toLocaleString()}</p>
        </div>
      </CardHeader>
      <CardContent className="p-3 pt-1">
        <p className="text-sm text-foreground/90 whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: comment.text.replace(/@(\w+)/g, '<span class="text-accent font-semibold">@$1</span>') }}></p>
        {comment.media && comment.media.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-2">
            {comment.media.map((item, index) => (
              <MediaDisplayItem key={index} mediaItem={item} />
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="p-3 pt-1 flex justify-between items-center border-t border-border/50">
        <div className="flex gap-1">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => handleVoteClick('upvote')}
            className={`text-xs hover:text-green-500 px-2 ${userVoteStatus === 'upvote' ? 'text-green-500' : 'text-muted-foreground'}`}
          >
            <ThumbsUp size={14} className="mr-1" /> ({comment.upvotes || 0})
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => handleVoteClick('downvote')}
            className={`text-xs hover:text-red-500 px-2 ${userVoteStatus === 'downvote' ? 'text-red-500' : 'text-muted-foreground'}`}
          >
            <ThumbsDown size={14} className="mr-1" /> ({comment.downvotes || 0})
          </Button>
          {user && (
            <Button variant="ghost" size="sm" onClick={() => onReply(comment.commentId, comment.userName)} className="text-xs text-muted-foreground hover:text-primary px-2">
              <MessageSquare size={14} className="mr-1" /> Reply
            </Button>
          )}
        </div>
        {user && user.id === comment.userId && (
            <div className="flex gap-1">
                <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary h-7 w-7" onClick={() => toast({ title: "🚧 Feature Not Implemented", description: "Editing comments isn't available yet, but you can request it! 🚀" })}> <Edit3 size={12}/> </Button>
                <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-destructive h-7 w-7" onClick={() => toast({ title: "🚧 Feature Not Implemented", description: "Deleting comments isn't available yet, but you can request it! 🚀" })}> <Trash2 size={12}/> </Button>
            </div>
        )}
      </CardFooter>
      {comment.replies && comment.replies.length > 0 && (
        <div className="pl-8 pr-3 pb-3 space-y-2">
          {comment.replies.map(reply => (
            <div key={reply.replyId || reply.commentId} className="p-2 bg-secondary/30 rounded-md border-l-2 border-primary/50">
              <div className="flex items-center space-x-2 mb-1">
                <Link to={`/user/${reply.userId}`}>
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={reply.userAvatarUrl || `https://avatar.vercel.sh/${reply.userName}.png?size=24`} alt={reply.userName} />
                    <AvatarFallback className="text-xs">{reply.userName ? reply.userName.substring(0,1).toUpperCase() : 'U'}</AvatarFallback>
                  </Avatar>
                </Link>
                <Link to={`/user/${reply.userId}`} className="font-semibold text-xs text-primary hover:underline">
                  {reply.userName}
                </Link>
                <p className="text-xs text-muted-foreground">{new Date(reply.timestamp).toLocaleDateString()}</p>
              </div>
              <p className="text-xs text-foreground/80 whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: reply.text.replace(/@(\w+)/g, '<span class="text-accent font-semibold">@$1</span>') }}></p>
              {reply.media && reply.media.length > 0 && (
                <div className="mt-1.5 flex flex-wrap gap-1.5">
                  {reply.media.map((item, index) => (
                    <MediaDisplayItem key={index} mediaItem={item} />
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </Card>
  );
};


const LeaderActivityTabs = ({ leader, commentsList, user }) => {
  const { addCommentToLeaderProfile, voteOnLeaderComment } = useLeaderData();
  const { toast } = useToast();
  const [replyingTo, setReplyingTo] = useState(null); 
  const [commentText, setCommentText] = useState('');
  const [attachedMediaFiles, setAttachedMediaFiles] = useState([]); 
  const commentInputRef = React.useRef(null);

  if (!leader) return null;

  const handleReplyToComment = (commentId, userNameToReply) => {
    setReplyingTo({ commentId, userName: userNameToReply });
    setCommentText(`@${userNameToReply} `);
    setAttachedMediaFiles([]); 
    commentInputRef.current?.focus();
  };

  const handleMediaChangeForReply = (files) => {
    setAttachedMediaFiles(files);
  };

  const handlePostReply = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to reply.", variant: "destructive" });
      return;
    }
    if (!commentText.trim() && attachedMediaFiles.length === 0) {
      toast({ title: "Empty Reply", description: "Cannot post an empty reply without text or media.", variant: "destructive" });
      return;
    }
    
    const mediaDataForStorage = attachedMediaFiles.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        url: URL.createObjectURL(file) 
      }));

    addCommentToLeaderProfile(leader.id, commentText, replyingTo.commentId, mediaDataForStorage);
    toast({ title: "Reply Posted!", description: "Your reply has been added." });
    setCommentText('');
    setAttachedMediaFiles([]);
    setReplyingTo(null);
  };

  const handleVoteOnComment = (commentId, voteType) => {
    voteOnLeaderComment(leader.id, commentId, voteType);
  };

  return (
    <Tabs defaultValue="comments" className={cn("w-full", "md:mt-0 mt-4 mobile-activity-tabs")}>
      <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 md:w-auto md:inline-flex bg-secondary/50 rounded-lg p-1 mb-4 overflow-x-auto md:overflow-visible no-scrollbar">
        <TabsTrigger value="comments" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm font-medium whitespace-nowrap">Comments ({commentsList.length})</TabsTrigger>
        <TabsTrigger value="polls" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm font-medium whitespace-nowrap">Polls</TabsTrigger>
        <TabsTrigger value="petitions" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm font-medium whitespace-nowrap">Petitions</TabsTrigger>
        <TabsTrigger value="banter" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm font-medium whitespace-nowrap">Banter Mentions</TabsTrigger>
      </TabsList>
      
      <TabsContent value="comments" className="mt-0 p-0 md:p-4 modern-card md:border-0 md:shadow-none md:bg-transparent">
        <h3 className="text-lg font-semibold mb-4 px-4 md:px-0">Community Comments on {leader.name}</h3>
        
        {user && replyingTo && (
          <Card className="mb-6 modern-card">
            <CardHeader className="pb-2">
              <p className="text-sm font-medium text-primary">
                Replying to {replyingTo.userName}
              </p>
            </CardHeader>
            <CardContent className="space-y-3">
              <Textarea 
                ref={commentInputRef}
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                placeholder={`Type your reply to @${replyingTo.userName}...`}
                className="modern-input min-h-[80px]"
              />
              <MediaUpload onFilesChange={handleMediaChangeForReply} existingFiles={[]} />
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button variant="ghost" size="sm" onClick={() => { setReplyingTo(null); setCommentText(''); setAttachedMediaFiles([]); }}>Cancel Reply</Button>
              <Button onClick={handlePostReply} className="btn-primary">
                <Send size={16} className="mr-2"/> Post Reply
              </Button>
            </CardFooter>
          </Card>
        )}

        {commentsList && commentsList.length > 0 ? (
          <div className="space-y-4">
            {commentsList.sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp)).map(comment => (
              <CommentItem 
                key={comment.commentId} 
                comment={comment} 
                user={user} 
                onReply={handleReplyToComment}
                onVote={handleVoteOnComment}
                leaderId={leader.id}
              />
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground px-4 md:px-0">No comments yet. Be the first to rate and comment!</p>
        )}
      </TabsContent>

      <TabsContent value="polls" className="mt-0 p-4 modern-card md:bg-transparent">
        <h3 className="text-lg font-semibold mb-2">Polls related to {leader.name}</h3>
        <p className="text-muted-foreground">Polls involving this leader will appear here. (Coming Soon)</p>
      </TabsContent>
      <TabsContent value="petitions" className="mt-0 p-4 modern-card md:bg-transparent">
        <h3 className="text-lg font-semibold mb-2">Petitions involving {leader.name}</h3>
        <p className="text-muted-foreground">Petitions related to or started by this leader will appear here. (Coming Soon)</p>
      </TabsContent>
      <TabsContent value="banter" className="mt-0 p-4 modern-card md:bg-transparent">
        <h3 className="text-lg font-semibold mb-2">Banter Mentions for {leader.name}</h3>
        <p className="text-muted-foreground">Banter room posts where this leader is mentioned or tagged will appear here. (Coming Soon)</p>
      </TabsContent>
       <style jsx="true">{`
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .no-scrollbar {
            -ms-overflow-style: none; 
            scrollbar-width: none; 
        }
      `}</style>
    </Tabs>
  );
};

export default LeaderActivityTabs;