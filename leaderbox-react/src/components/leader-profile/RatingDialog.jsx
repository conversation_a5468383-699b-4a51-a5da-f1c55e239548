import React from 'react';
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Star } from 'lucide-react';

const RatingDialog = ({ 
  isOpen, 
  onClose, 
  leaderName, 
  currentRating, 
  setCurrentRating, 
  commentText, 
  setCommentText, 
  hoverRating, 
  setHoverRating, 
  onSubmit 
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary">Rate {leaderName}</DialogTitle>
          <DialogDescription>
            You selected {currentRating} star{currentRating !== 1 ? 's' : ''}. Add an optional comment (reason).
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex justify-center space-x-1 mb-4">
              {[1, 2, 3, 4, 5].map((starVal) => (
                <Star
                  key={starVal}
                  className={`h-10 w-10 cursor-pointer transition-colors duration-150 ${
                    (hoverRating || currentRating) >= starVal ? 'text-yellow-400 fill-yellow-400' : 'text-muted-foreground/30'
                  }`}
                  onMouseEnter={() => setHoverRating(starVal)}
                  onMouseLeave={() => setHoverRating(0)}
                  onClick={() => setCurrentRating(starVal)}
                />
              ))}
            </div>
          <div>
            <Label htmlFor="rating-comment" className="text-right sr-only">Comment</Label>
            <Textarea 
              id="rating-comment" 
              value={commentText} 
              onChange={(e) => setCommentText(e.target.value)} 
              placeholder="Why this rating? (Optional, public)"
              className="modern-input min-h-[100px]"
            />
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button type="button" onClick={onSubmit} className="btn-primary">Submit Rating</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RatingDialog;