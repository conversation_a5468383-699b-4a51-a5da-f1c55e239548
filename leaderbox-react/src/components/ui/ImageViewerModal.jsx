import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const ImageViewerModal = ({ isOpen, onClose, imageUrl, imageAlt = "Full view" }) => {
  if (!isOpen || !imageUrl) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl p-2 sm:p-4 bg-background/90 backdrop-blur-sm border-border shadow-2xl !rounded-lg w-[95vw] h-[90vh]">
        <DialogHeader className="relative sr-only">
          <DialogTitle>{imageAlt}</DialogTitle>
        </DialogHeader>
        <DialogClose asChild className="absolute top-2 right-2 z-[60]">
          <Button type="button" variant="ghost" size="icon" className="rounded-full bg-background/70 hover:bg-background text-foreground hover:text-primary">
            <X className="h-5 w-5" />
          </Button>
        </DialogClose>
        <div className="flex justify-center items-center w-full h-full overflow-hidden pt-8">
          <img src={imageUrl} alt={imageAlt} className="max-w-full max-h-full object-contain rounded-md" />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageViewerModal;