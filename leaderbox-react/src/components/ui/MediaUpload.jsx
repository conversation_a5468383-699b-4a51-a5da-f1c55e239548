
import React, { useState, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Label } from '@/components/ui/label';
import { Paperclip, Image as ImageIconLucide, Video as VideoIconLucide, FileText as FileTextIconLucide, X, UploadCloud } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import ImageViewerModal from '@/components/ui/ImageViewerModal.jsx';

const MediaUpload = ({ onFilesChange, existingFiles = [], maxFiles = 5, maxFileSizeMB = 10 }) => {
  const [selectedFilesData, setSelectedFilesData] = useState(() => 
    existingFiles.map(file => ({
      id: `existing-${file.name}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      file: file,
      previewUrl: file.url || (file.type?.startsWith('image/') ? URL.createObjectURL(file) : null)
    }))
  );
  const { toast } = useToast();

  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState('');
  const [selectedImageAlt, setSelectedImageAlt] = useState('');

  const handleFileChange = useCallback((event) => {
    const files = Array.from(event.target.files);
    if (selectedFilesData.length + files.length > maxFiles) {
      toast({
        title: "File Limit Exceeded",
        description: `You can only upload a maximum of ${maxFiles} files.`,
        variant: "destructive",
      });
      return;
    }

    const newFilesData = [];
    const currentFiles = selectedFilesData.map(f => f.file);

    files.forEach(file => {
      if (file.size > maxFileSizeMB * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: `File "${file.name}" exceeds the ${maxFileSizeMB}MB size limit.`,
          variant: "destructive",
        });
        return;
      }

      const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      let previewUrl = null;
      if (file.type.startsWith('image/')) {
        previewUrl = URL.createObjectURL(file);
      }
      newFilesData.push({ id: fileId, file, previewUrl });
    });
    
    const updatedFilesData = [...selectedFilesData, ...newFilesData];
    setSelectedFilesData(updatedFilesData);
    onFilesChange(updatedFilesData.map(f => f.file));
  }, [selectedFilesData, onFilesChange, toast, maxFiles, maxFileSizeMB]);

  const handleRemoveFile = (fileIdToRemove) => {
    const fileToRemove = selectedFilesData.find(f => f.id === fileIdToRemove);
    if (fileToRemove && fileToRemove.previewUrl && fileToRemove.previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(fileToRemove.previewUrl);
    }

    const updatedFilesData = selectedFilesData.filter(f => f.id !== fileIdToRemove);
    setSelectedFilesData(updatedFilesData);
    onFilesChange(updatedFilesData.map(f => f.file));
  };

  const handleImagePreviewClick = (fileWrapper) => {
    if (fileWrapper.previewUrl) {
      setSelectedImageUrl(fileWrapper.previewUrl);
      setSelectedImageAlt(fileWrapper.file.name);
      setIsImageViewerOpen(true);
    }
  };

  const getFileIconElement = (fileName, fileType) => {
    const commonClass = "h-5 w-5 flex-shrink-0";
    if (fileType?.startsWith('image/')) return <ImageIconLucide className={`${commonClass} text-blue-500`} />;
    if (fileType?.startsWith('video/')) return <VideoIconLucide className={`${commonClass} text-purple-500`} />;
    if (fileType === 'application/pdf') return <FileTextIconLucide className={`${commonClass} text-red-500`} />;
    if (fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return <FileTextIconLucide className={`${commonClass} text-sky-500`} />;
    
    if (/\.(jpe?g|png|gif|webp)$/i.test(fileName)) return <ImageIconLucide className={`${commonClass} text-blue-500`} />;
    if (/\.(mp4|mov|avi|wmv)$/i.test(fileName)) return <VideoIconLucide className={`${commonClass} text-purple-500`} />;
    if (/\.(pdf)$/i.test(fileName)) return <FileTextIconLucide className={`${commonClass} text-red-500`} />;
    if (/\.(doc|docx)$/i.test(fileName)) return <FileTextIconLucide className={`${commonClass} text-sky-500`} />;
    return <Paperclip className={`${commonClass} text-gray-500`} />;
  };


  return (
    <div className="space-y-3 mt-2">
      <Label htmlFor="media-upload-input" className="cursor-pointer block">
        <div className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-border rounded-lg hover:border-primary transition-colors bg-secondary/30">
            <UploadCloud className="h-10 w-10 text-muted-foreground mb-2" />
            <span className="text-sm font-medium text-foreground">Click to upload or drag & drop</span>
            <span className="text-xs text-muted-foreground">Images, Videos, PDF (Max {maxFileSizeMB}MB, {maxFiles} files)</span>
        </div>
        <Input
            id="media-upload-input"
            type="file"
            multiple
            accept="image/*,video/*,.pdf"
            onChange={handleFileChange}
            className="hidden"
            disabled={selectedFilesData.length >= maxFiles}
        />
      </Label>
      
      {selectedFilesData.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">Selected files ({selectedFilesData.length}/{maxFiles}):</p>
          <ScrollArea className="h-auto max-h-48 w-full rounded-md border border-border p-2 bg-background">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {selectedFilesData.map((fileWrapper) => (
                <div key={fileWrapper.id} className="flex items-center justify-between p-1.5 bg-secondary/50 rounded-md text-xs">
                  <div className="flex items-center gap-2 overflow-hidden">
                    {fileWrapper.previewUrl && fileWrapper.file.type.startsWith('image/') ? (
                       <button type="button" onClick={() => handleImagePreviewClick(fileWrapper)} className="block h-8 w-8 flex-shrink-0 rounded overflow-hidden">
                        <img src={fileWrapper.previewUrl} alt={fileWrapper.file.name} className="h-full w-full object-cover" />
                       </button>
                    ) : (
                      <span className="flex-shrink-0 p-1.5">{getFileIconElement(fileWrapper.file.name, fileWrapper.file.type)}</span>
                    )}
                    <span className="truncate text-foreground" title={fileWrapper.file.name}>{fileWrapper.file.name}</span>
                  </div>
                  <Button variant="ghost" size="icon" onClick={() => handleRemoveFile(fileWrapper.id)} className="h-6 w-6 text-destructive hover:bg-destructive/10">
                    <X size={14} />
                  </Button>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}
       <ImageViewerModal 
        isOpen={isImageViewerOpen}
        onClose={() => setIsImageViewerOpen(false)}
        imageUrl={selectedImageUrl}
        imageAlt={selectedImageAlt}
      />
    </div>
  );
};

export default MediaUpload;
