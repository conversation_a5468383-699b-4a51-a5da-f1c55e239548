import React, { useState, useRef, useEffect } from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export function Combobox({ options, value, onChange, placeholder = "Select option...", inputClassName, disabled }) {
  const [open, setOpen] = useState(false)
  const [inputValue, setInputValue] = useState(value || "") 
  const triggerRef = useRef(null);
  const [popoverWidth, setPopoverWidth] = useState(0);

  useEffect(() => {
    const selectedOption = options.find(opt => opt.value === value);
    setInputValue(selectedOption ? selectedOption.label : (value || ""));
  }, [value, options]);

  useEffect(() => {
    if (triggerRef.current) {
      setPopoverWidth(triggerRef.current.offsetWidth);
    }
  }, [open]);


  const handleSelect = (currentValue) => {
    const selectedOption = options.find(opt => opt.value === currentValue);
    onChange(selectedOption ? selectedOption.value : ""); // Store actual value
    setInputValue(selectedOption ? selectedOption.label : "");
    setOpen(false);
  }
  
  const handleInputChange = (e) => {
    const currentInput = e.target.value;
    setInputValue(currentInput);
    const matchedOption = options.find(opt => opt.label.toLowerCase() === currentInput.toLowerCase());
    if (matchedOption) {
      onChange(matchedOption.value);
    } else {
      onChange(currentInput); 
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild ref={triggerRef} disabled={disabled}>
        <div className="relative">
          <Input
            value={inputValue}
            onChange={handleInputChange}
            placeholder={placeholder}
            className={cn("w-full justify-between pr-10", inputClassName, disabled ? "cursor-not-allowed opacity-50" : "")}
            aria-expanded={open}
            role="combobox"
            disabled={disabled}
            onClick={() => !disabled && setOpen(true)} 
          />
          {!disabled && (
            <Button
              variant="ghost"
              role="combobox"
              aria-expanded={open}
              className="absolute right-0 top-0 h-full px-3"
              onClick={() => setOpen((prev) => !prev)}
            >
              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
            </Button>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="p-0" style={{ width: popoverWidth }} align="start">
        <Command filter={(itemValue, search) => {
          const option = options.find(opt => opt.label.toLowerCase() === itemValue.toLowerCase());
          if (option && option.label.toLowerCase().includes(search.toLowerCase())) return 1;
          return 0;
        }}>
          <CommandInput placeholder="Search..." />
          <CommandList>
            <CommandEmpty>No option found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label} 
                  onSelect={() => {
                    handleSelect(option.value)
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}