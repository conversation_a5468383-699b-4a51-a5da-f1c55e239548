import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Edit, Trash2, Flag, UserX, ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const UserTable = ({ users, sortConfig, requestSort, onEdit, onFlag, onBan, onUnban, onDelete }) => {
  
  const getSortIcon = (key) => {
    if (sortConfig.key !== key) return <ChevronDown size={14} className="opacity-0 group-hover:opacity-50"/>;
    return sortConfig.direction === 'ascending' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  if (users.length === 0) {
    return <p className="text-muted-foreground text-center py-10">No users match your criteria.</p>;
  }

  return (
    <table className="min-w-full divide-y divide-border text-sm">
      <thead className="bg-secondary/50">
        <tr>
          <th scope="col" className="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" onClick={() => requestSort('name')}>
            <div className="flex items-center gap-1">Name {getSortIcon('name')}</div>
          </th>
          <th scope="col" className="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" onClick={() => requestSort('email')}>
            <div className="flex items-center gap-1">Email {getSortIcon('email')}</div>
          </th>
          <th scope="col" className="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" onClick={() => requestSort('role')}>
            <div className="flex items-center gap-1">Role {getSortIcon('role')}</div>
          </th>
          <th scope="col" className="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" onClick={() => requestSort('status')}>
            <div className="flex items-center gap-1">Status {getSortIcon('status')}</div>
          </th>
          <th scope="col" className="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody className="bg-card divide-y divide-border">
        <AnimatePresence>
          {users.map((user) => (
            <motion.tr 
              key={user.id}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              layout
              className="hover:bg-secondary/30 transition-colors"
            >
              <td className="px-3 py-2.5 whitespace-nowrap">
                <div className="flex items-center">
                  <Avatar className="h-7 w-7 mr-2">
                    <AvatarImage src={user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=28`} alt={user.name} />
                    <AvatarFallback className="text-xs">{user.name ? user.name.substring(0,1).toUpperCase() : user.email.substring(0,1).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <span className="font-medium text-foreground">{user.name || 'N/A'}</span>
                </div>
              </td>
              <td className="px-3 py-2.5 whitespace-nowrap text-muted-foreground">{user.email}</td>
              <td className="px-3 py-2.5 whitespace-nowrap text-muted-foreground capitalize">{user.role || 'User'}</td>
              <td className="px-3 py-2.5 whitespace-nowrap">
                <span className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full 
                  ${user.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-700/30 dark:text-green-300' : 
                    user.status === 'flagged' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-700/30 dark:text-yellow-300' : 
                    user.status === 'banned' ? 'bg-red-100 text-red-800 dark:bg-red-700/30 dark:text-red-300' : 
                    'bg-gray-100 text-gray-800 dark:bg-gray-700/30 dark:text-gray-300'}`}>
                  {user.status || 'active'}
                </span>
              </td>
              <td className="px-3 py-2.5 whitespace-nowrap font-medium space-x-0.5">
                <Button variant="ghost" size="icon" onClick={() => onEdit(user)} className="text-primary hover:text-primary/80 h-7 w-7"><Edit size={14} /></Button>
                {user.status !== 'banned' ? (
                  <>
                    <Button variant="ghost" size="icon" onClick={() => onFlag(user.id, user.name || user.email)} className="text-yellow-500 hover:text-yellow-600 h-7 w-7"><Flag size={14} /></Button>
                    <Button variant="ghost" size="icon" onClick={() => onBan(user.id, user.name || user.email)} className="text-red-600 hover:text-red-700 h-7 w-7"><UserX size={14} /></Button>
                  </>
                ) : (
                   <Button variant="ghost" size="icon" onClick={() => onUnban(user.id, user.name || user.email)} className="text-green-500 hover:text-green-600 h-7 w-7"><UserX size={14} /></Button>
                )}
                <Button variant="ghost" size="icon" onClick={() => onDelete(user.id, user.name || user.email)} className="text-destructive hover:text-destructive/80 h-7 w-7"><Trash2 size={14} /></Button>
              </td>
            </motion.tr>
          ))}
        </AnimatePresence>
      </tbody>
    </table>
  );
};

export default UserTable;