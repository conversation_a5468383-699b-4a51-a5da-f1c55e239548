import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";

const EditUserDialog = ({ 
  isOpen, 
  onOpenChange, 
  editingUser, 
  editFormData, 
  onFormChange, 
  onSelectChange, 
  onSaveChanges 
}) => {
  if (!editingUser) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-primary text-lg">Edit User: {editingUser.name || editingUser.email}</DialogTitle>
          <DialogDescription className="text-xs">Modify user details and status.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-3 py-3 text-sm">
          <div>
            <Label htmlFor="edit-name">Full Name</Label>
            <Input id="edit-name" name="name" value={editFormData.name} onChange={onFormChange} className="modern-input mt-1 text-sm"/>
          </div>
          <div>
            <Label htmlFor="edit-email">Email Address</Label>
            <Input id="edit-email" name="email" type="email" value={editFormData.email} className="modern-input mt-1 text-sm" disabled/>
          </div>
          <div>
            <Label htmlFor="edit-role">Role</Label>
            <Select value={editFormData.role} onValueChange={(val) => onSelectChange('role', val)}>
              <SelectTrigger className="w-full modern-input mt-1 text-sm"><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="User">User</SelectItem>
                <SelectItem value="Editor">Editor</SelectItem>
                <SelectItem value="Content Creator">Content Creator</SelectItem>
                <SelectItem value="Overall Admin">Overall Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="edit-status">Status</Label>
            <Select value={editFormData.status} onValueChange={(val) => onSelectChange('status', val)}>
              <SelectTrigger className="w-full modern-input mt-1 text-sm"><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="flagged">Flagged</SelectItem>
                <SelectItem value="banned">Banned</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild><Button type="button" variant="outline" size="sm">Cancel</Button></DialogClose>
          <Button type="button" onClick={onSaveChanges} className="btn-primary" size="sm">Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditUserDialog;