import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search } from 'lucide-react';

const UserTableFilters = ({ 
  searchTerm, 
  onSearchTermChange, 
  filterRole, 
  onFilterRoleChange, 
  filterStatus, 
  onFilterStatusChange 
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 pt-3">
      <div className="relative">
        <Input 
          placeholder="Search by name or email..."
          value={searchTerm}
          onChange={(e) => onSearchTermChange(e.target.value)}
          className="modern-input pl-8 text-sm"
        />
        <Search size={16} className="absolute left-2.5 top-1/2 -translate-y-1/2 text-muted-foreground" />
      </div>
      <Select value={filterRole} onValueChange={onFilterRoleChange}>
        <SelectTrigger className="modern-input text-sm"><SelectValue placeholder="Filter by Role" /></SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Roles</SelectItem>
          <SelectItem value="User">User</SelectItem>
          <SelectItem value="Overall Admin">Overall Admin</SelectItem>
          <SelectItem value="Editor">Editor</SelectItem>
          <SelectItem value="Content Creator">Content Creator</SelectItem>
        </SelectContent>
      </Select>
      <Select value={filterStatus} onValueChange={onFilterStatusChange}>
        <SelectTrigger className="modern-input text-sm"><SelectValue placeholder="Filter by Status" /></SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Statuses</SelectItem>
          <SelectItem value="active">Active</SelectItem>
          <SelectItem value="flagged">Flagged</SelectItem>
          <SelectItem value="banned">Banned</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default UserTableFilters;