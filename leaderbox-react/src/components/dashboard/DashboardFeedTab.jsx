
import React from 'react';
import { Card, CardContent } from '@/components/ui/card.jsx';
import FeedItemCard from '@/components/dashboard/FeedItemCard.jsx';

const DashboardFeedTab = ({ items }) => (
  <Card className="modern-card">
    <CardContent className="space-y-4 pt-4">
      {items.length > 0 ? items.map((item, index) => (
        <FeedItemCard key={`${item.type}-${item.data.id}-${index}`} item={item} />
      )) : <p className="text-muted-foreground p-4 text-center">Your feed is looking a bit empty. Interact more to personalize it or check suggested actions!</p>}
    </CardContent>
  </Card>
);

export default DashboardFeedTab;
