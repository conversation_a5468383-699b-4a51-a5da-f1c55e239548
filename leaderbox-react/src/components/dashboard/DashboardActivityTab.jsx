
import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';

const DashboardActivityTab = ({ activity, type, linkPrefix, emptyMessage, ctaLink, ctaText }) => {
  const { user: currentUser } = useAuth(); 

  return (
    <Card className="modern-card">
      <CardHeader><CardTitle className="text-xl text-primary">Your {type} Activity</CardTitle></CardHeader>
      <CardContent className="space-y-3">
        {activity.length > 0 ? activity.map(item => (
          <Link key={item.id} to={`${linkPrefix}/${item.id}`} className="block p-3 hover:bg-secondary/50 rounded-md transition-colors border border-transparent hover:border-border">
            <p className="font-semibold text-foreground">{item.title || item.topic}</p>
            <p className="text-xs text-muted-foreground">
              {item.createdBy === (currentUser?.name || currentUser?.email.split('@')[0]) && type === 'Petition' ? `Created by you` : 
              type === 'Petition' ? `You signed` : `Last activity`}: {new Date(item.comments?.slice(-1)[0]?.timestamp || item.createdAt || item.timestamp).toLocaleDateString()}
            </p>
          </Link>
        )) : <p className="text-muted-foreground p-4 text-center">{emptyMessage} <Link to={ctaLink} className="text-primary hover:underline">{ctaText}</Link></p>}
      </CardContent>
    </Card>
  );
};

export default DashboardActivityTab;
