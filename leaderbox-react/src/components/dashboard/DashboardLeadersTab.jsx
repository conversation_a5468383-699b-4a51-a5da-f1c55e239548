
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card.jsx';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const DashboardLeadersTab = ({ leaders }) => (
  <Card className="modern-card">
    <CardHeader><CardTitle className="text-xl text-primary">Leaders You Follow ({leaders.length})</CardTitle></CardHeader>
    <CardContent className="space-y-3">
      {leaders.length > 0 ? leaders.map(leader => (
        <Link key={leader.id} to={`/leader/${leader.id}`} className="flex items-center space-x-3 p-3 hover:bg-secondary/50 rounded-md transition-colors border border-transparent hover:border-border">
          <Avatar className="h-10 w-10">
            <AvatarImage src={leader.avatarUrl} alt={leader.name} />
            <AvatarFallback>{leader.name.substring(0,1)}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-semibold text-foreground">{leader.name}</p>
            <p className="text-xs text-muted-foreground">{leader.position}</p>
          </div>
        </Link>
      )) : <p className="text-muted-foreground p-4 text-center">You are not following any leaders yet. <Link to="/" className="text-primary hover:underline">Discover leaders now!</Link></p>}
    </CardContent>
  </Card>
);

export default DashboardLeadersTab;
