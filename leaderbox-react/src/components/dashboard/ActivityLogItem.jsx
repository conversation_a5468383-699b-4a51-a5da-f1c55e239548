
import React from 'react';
import { Activity, Star, ThumbsUp, FileText, MessageSquare } from 'lucide-react';

const ActivityLogItem = ({ activity }) => {
  let icon = <Activity className="w-4 h-4 text-gray-500" />;
  if (activity.type === 'rated_leader') icon = <Star className="w-4 h-4 text-yellow-500" />;
  if (activity.type === 'voted_poll') icon = <ThumbsUp className="w-4 h-4 text-green-500" />;
  if (activity.type === 'signed_petition') icon = <FileText className="w-4 h-4 text-orange-500" />;
  if (activity.type === 'created_banter') icon = <MessageSquare className="w-4 h-4 text-blue-500" />;

  return (
    <div className="flex items-center space-x-3 p-2 hover:bg-secondary/50 rounded-md">
      {icon}
      <p className="text-sm text-muted-foreground flex-grow">{activity.text}</p>
      <span className="text-xs text-muted-foreground/70">{new Date(activity.timestamp).toLocaleDateString()}</span>
    </div>
  );
};

export default ActivityLogItem;
