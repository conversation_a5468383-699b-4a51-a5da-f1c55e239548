
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button';

const SuggestedActionCard = ({ suggestion }) => {
    const navigate = useNavigate();
    return (
        <Card className="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 border-border hover:shadow-md transition-shadow">
            <CardContent className="p-4 flex items-center space-x-3">
                <div className="p-2 bg-primary/10 rounded-full">
                    {suggestion.icon}
                </div>
                <div className="flex-grow">
                    <p className="text-sm font-medium text-foreground">{suggestion.title}</p>
                    {suggestion.description && <p className="text-xs text-muted-foreground">{suggestion.description}</p>}
                </div>
                <Button size="sm" variant="outline" className="button-outline-override" onClick={() => navigate(suggestion.link)}>
                    {suggestion.actionText || "View"}
                </Button>
            </CardContent>
        </Card>
    );
};

export default SuggestedActionCard;
