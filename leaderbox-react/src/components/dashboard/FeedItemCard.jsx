
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button';
import { MessageSquare, BarChart2, FileText, UserCheck } from 'lucide-react';

const FeedItemCard = ({ item }) => {
  const navigate = useNavigate();
  let content = null;
  let icon = <MessageSquare className="w-5 h-5 text-primary" />;
  let actionText = "View";
  let linkTo = "/";

  switch (item.type) {
    case 'banter_mention':
      icon = <MessageSquare className="w-5 h-5 text-blue-500" />;
      content = (
        <>
          <p className="text-sm text-muted-foreground">You were mentioned in a banter:</p>
          <p className="font-semibold text-foreground">{item.data.title}</p>
        </>
      );
      actionText = "View Banter";
      linkTo = `/banter/${item.data.id}`;
      break;
    case 'poll_voted':
      icon = <BarChart2 className="w-5 h-5 text-green-500" />;
      content = (
        <>
          <p className="text-sm text-muted-foreground">You voted on the poll:</p>
          <p className="font-semibold text-foreground">{item.data.topic}</p>
        </>
      );
      actionText = "View Poll";
      linkTo = `/poll/${item.data.id}`;
      break;
    case 'leader_followed_update':
      icon = <UserCheck className="w-5 h-5 text-purple-500" />;
      content = (
        <>
          <p className="text-sm text-muted-foreground">Update from a leader you follow:</p>
          <p className="font-semibold text-foreground">{item.data.name} <span className="text-xs text-muted-foreground">({item.data.update || "shared an update"})</span></p>
        </>
      );
      actionText = "View Profile";
      linkTo = `/leader/${item.data.id}`;
      break;
    case 'new_petition_location':
        icon = <FileText className="w-5 h-5 text-orange-500" />;
        content = (
            <>
                <p className="text-sm text-muted-foreground">New petition relevant to your location ({item.data.location || 'Nigeria'}):</p>
                <p className="font-semibold text-foreground">{item.data.title}</p>
            </>
        );
        actionText = "View Petition";
        linkTo = `/petition/${item.data.id}`;
        break;
    default:
      content = <p className="text-sm text-foreground">Generic feed item: {item.data.title || item.data.text}</p>;
  }

  return (
    <Card className="modern-card hover:shadow-lg transition-shadow duration-200">
      <CardContent className="p-4 flex items-start space-x-3">
        <div className="flex-shrink-0 pt-1">{icon}</div>
        <div className="flex-grow">
          {content}
          <p className="text-xs text-muted-foreground mt-1">{new Date(item.timestamp).toLocaleDateString()}</p>
        </div>
        <Button variant="ghost" size="sm" className="self-center text-primary hover:bg-primary/10" onClick={() => navigate(linkTo)}>
          {actionText}
        </Button>
      </CardContent>
    </Card>
  );
};

export default FeedItemCard;
