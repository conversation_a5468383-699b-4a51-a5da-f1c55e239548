import React from 'react';
import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import HomePage from '@/pages/HomePage.jsx';
import LeaderProfilePage from '@/pages/LeaderProfilePage.jsx';
import PollsPage from '@/pages/PollsPage.jsx';
import SinglePollPage from '@/pages/SinglePollPage.jsx';
import UserDashboardPage from '@/pages/UserDashboardPage.jsx';
import LoginPage from '@/pages/LoginPage.jsx';
import RegisterPage from '@/pages/RegisterPage.jsx';
import SettingsPage from '@/pages/SettingsPage.jsx'; 
import AdminDashboardPage from '@/pages/AdminDashboardPage.jsx';
import AdminLeaderManagementPage from '@/pages/admin/AdminLeaderManagementPage.jsx';
import AdminBanterManagementPage from '@/pages/admin/AdminBanterManagementPage.jsx';
import AdminPollManagementPage from '@/pages/admin/AdminPollManagementPage.jsx';
import AdminPetitionManagementPage from '@/pages/admin/AdminPetitionManagementPage.jsx';
import AdminContentModerationPage from '@/pages/admin/AdminContentModerationPage.jsx';
import AdminEngagementMetricsPage from '@/pages/admin/AdminEngagementMetricsPage.jsx';
import AdminKeywordManagementPage from '@/pages/admin/AdminKeywordManagementPage.jsx';
import AdminUserManagementPage from '@/pages/admin/AdminUserManagementPage.jsx';
import AdminEmailSettingsPage from '@/pages/admin/AdminEmailSettingsPage.jsx';
import AdminRegisteredUsersPage from '@/pages/admin/AdminRegisteredUsersPage.jsx';
import AdminSuggestedEditsPage from '@/pages/admin/AdminSuggestedEditsPage.jsx'; 
import AdminSuggestedLeadersPage from '@/pages/admin/AdminSuggestedLeadersPage.jsx';
import AdminProfileCategoriesPage from '@/pages/admin/AdminProfileCategoriesPage.jsx';
import AdminGroupsManagementPage from '@/pages/admin/AdminGroupsManagementPage.jsx';
import MainLayout from '@/components/layouts/MainLayout.jsx';
import AdminLayout from '@/components/layouts/AdminLayout.jsx';
import { Toaster } from "@/components/ui/toaster";
import BanterRoomPage from '@/pages/BanterRoomPage.jsx'; 
import SingleBanterPage from '@/pages/SingleBanterPage.jsx';
import PetitionsPage from '@/pages/PetitionsPage.jsx'; 
import SinglePetitionPage from '@/pages/SinglePetitionPage.jsx';
import UserProfilePage from '@/pages/UserProfilePage.jsx';
import MessagesPage from '@/pages/MessagesPage.jsx';
import GroupsPage from '@/pages/GroupsPage.jsx';
import SingleGroupPage from '@/pages/SingleGroupPage.jsx';
import SingleDiscussionPage from '@/pages/SingleDiscussionPage.jsx';
import FollowSuggestionsPage from '@/pages/FollowSuggestionsPage.jsx';
import { AuthProvider, useAuth } from '@/contexts/AuthContext.jsx';
import { DataProvider } from '@/contexts/DataContext.jsx';
import { LeaderProvider } from '@/contexts/LeaderContext.jsx';
import { PollProvider } from '@/contexts/PollContext.jsx';
import { AppStateProvider } from '@/contexts/AppStateContext.jsx';
import { BanterProvider } from '@/contexts/BanterContext.jsx';
import { PetitionProvider } from '@/contexts/PetitionContext.jsx';
import { GroupProvider } from '@/contexts/GroupContext.jsx';

const ProtectedRoute = ({ children, adminOnly = false }) => {
  const { user } = useAuth();
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  if (adminOnly && !user.isAdmin) {
    return <Navigate to="/dashboard" replace />; 
  }
  return children;
};

const AppContent = () => {
  return (
    <Routes>
      <Route element={<MainLayout />}>
        <Route path="/" element={<HomePage />} />
        <Route path="/leader/:leaderId" element={<LeaderProfilePage />} />
        <Route path="/user/:userId" element={<UserProfilePage />} /> 
        <Route path="/polls" element={<PollsPage />} />
        <Route path="/poll/:pollId" element={<SinglePollPage />} />
        <Route path="/petitions" element={<PetitionsPage />} /> 
        <Route path="/petition/:petitionId" element={<SinglePetitionPage />} />
        <Route path="/banter-room" element={<BanterRoomPage />} />
        <Route path="/banter/:banterId" element={<SingleBanterPage />} />
        <Route path="/groups" element={<GroupsPage />} />
        <Route path="/group/:groupId" element={<SingleGroupPage />} />
        <Route path="/group/:groupId/discussion/:discussionId" element={<SingleDiscussionPage />} />
        
        <Route 
          path="/dashboard" 
          element={
            <ProtectedRoute>
              <UserDashboardPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/settings" 
          element={
            <ProtectedRoute>
              <SettingsPage />
            </ProtectedRoute>
          } 
        />
         <Route 
          path="/messages" 
          element={
            <ProtectedRoute>
              <MessagesPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/welcome/follow-suggestions"
          element={
            <ProtectedRoute>
              <FollowSuggestionsPage />
            </ProtectedRoute>
          }
        />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
      </Route>

      <Route 
        path="/admin"
        element={
          <ProtectedRoute adminOnly={true}>
            <AdminLayout />
          </ProtectedRoute>
        }
      >
        <Route index element={<AdminDashboardPage />} />
        <Route path="leaders" element={<AdminLeaderManagementPage />} />
        <Route path="suggested-leaders" element={<AdminSuggestedLeadersPage />} />
        <Route path="banters" element={<AdminBanterManagementPage />} />
        <Route path="polls" element={<AdminPollManagementPage />} />
        <Route path="petitions" element={<AdminPetitionManagementPage />} />
        <Route path="moderation" element={<AdminContentModerationPage />} />
        <Route path="metrics" element={<AdminEngagementMetricsPage />} />
        <Route path="keywords" element={<AdminKeywordManagementPage />} />
        <Route path="users" element={<AdminUserManagementPage />} />
        <Route path="registered-users" element={<AdminRegisteredUsersPage />} />
        <Route path="email-settings" element={<AdminEmailSettingsPage />} />
        <Route path="suggested-edits" element={<AdminSuggestedEditsPage />} />
        <Route path="profile-categories" element={<AdminProfileCategoriesPage />} />
        <Route path="groups" element={<AdminGroupsManagementPage />} />
      </Route>
    </Routes>
  );
}

const App = () => {
  return (
    <Router>
      <AuthProvider>
        <AppStateProvider>
          <DataProvider> 
            <LeaderProvider> 
              <PollProvider>
                <BanterProvider>
                  <PetitionProvider>
                    <GroupProvider>
                      <AppContent /> 
                      <Toaster />
                    </GroupProvider>
                  </PetitionProvider>
                </BanterProvider>
              </PollProvider>
            </LeaderProvider>
          </DataProvider>
        </AppStateProvider>
      </AuthProvider>
    </Router>
  );
};

export default App;