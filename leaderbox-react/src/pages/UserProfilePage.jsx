import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useLeaderData } from '@/contexts/LeaderContext.jsx'; 
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast';
import UserProfileHeaderCard from '@/components/user-profile/UserProfileHeaderCard.jsx';
import UserProfileStatsCard from '@/components/user-profile/UserProfileStatsCard.jsx';
import UserActivityTabsContent from '@/components/user-profile/UserActivityTabsContent.jsx';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { Progress } from '@/components/ui/progress.jsx';
import { Award } from 'lucide-react';

const UserProfilePage = () => {
  const { userId } = useParams();
  const { user: currentUser, users: allRegisteredUsers = [], toggleProfileLike, userProfileLikes } = useAuth(); 
  const { leaders } = useLeaderData();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [profileUser, setProfileUser] = useState(null);
  const [isCurrentUserProfile, setIsCurrentUserProfile] = useState(false);

  useEffect(() => {
    let foundUser = null;
    if (Array.isArray(allRegisteredUsers) && allRegisteredUsers.length > 0) {
        foundUser = allRegisteredUsers.find(u => u.id === userId);
    }
    
    if (!foundUser && Array.isArray(leaders)) {
      const leaderAsUser = leaders.find(l => l.id === userId);
      if (leaderAsUser) {
        foundUser = {
          id: leaderAsUser.id,
          name: leaderAsUser.name,
          email: `${leaderAsUser.name.toLowerCase().replace(/\s+/g, '.')}@leader.example.com`, 
          avatarUrl: leaderAsUser.avatarUrl,
          state: leaderAsUser.state,
          party: leaderAsUser.party,
          position: leaderAsUser.position, 
          isLeader: true,
          bio: leaderAsUser.bioSummary || 'Esteemed political figure.',
          createdAt: leaderAsUser.createdAt || new Date(Date.now() - Math.random() * 1000 * 60 * 60 * 24 * 365 * 2).toISOString(),
          followers: leaderAsUser.followers || 0, 
          profileLikesCount: leaderAsUser.followers * 2,
          petitionsCreated: 0,
          groupMembersCount: 0,
        };
      }
    }

    if (foundUser) {
      setProfileUser(foundUser);
      setIsCurrentUserProfile(currentUser?.id === foundUser.id);
    } else {
      const dataIsLoaded = (Array.isArray(allRegisteredUsers) && allRegisteredUsers.length > 0) || (Array.isArray(leaders) && leaders.length > 0);
      if (dataIsLoaded && !foundUser) {
          toast({ title: "User Not Found", description: "This user profile does not exist.", variant: "destructive" });
          navigate('/'); 
      }
    }
  }, [userId, currentUser, allRegisteredUsers, leaders, toast, navigate]);

  const handleSendMessage = () => {
    if (!currentUser) {
        toast({ title: "Login Required", description: "Please log in to send messages.", variant: "destructive" });
        navigate('/login');
        return;
    }
    navigate(`/messages?to=${profileUser?.id}`);
  };

  const handleLikeToggle = () => {
    if (!currentUser) {
      toast({ title: "Login Required", description: "Please log in to like profiles.", variant: "destructive" });
      return;
    }
    toggleProfileLike(userId);
  };

  if (!profileUser) {
    return <div className="flex justify-center items-center min-h-screen"><p>Loading profile...</p></div>;
  }
  
  const isLikedByCurrentUser = userProfileLikes[userId]?.includes(currentUser?.id);
  const likesCount = profileUser.profileLikesCount || 0;

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-6 px-2 md:px-4 space-y-8"
    >
      <UserProfileHeaderCard 
        profileUser={profileUser}
        isCurrentUserProfile={isCurrentUserProfile}
        onSendMessage={handleSendMessage}
        onEditProfile={() => navigate('/settings')}
        onLikeToggle={handleLikeToggle}
        isLikedByCurrentUser={isLikedByCurrentUser}
      />

      {isCurrentUserProfile && likesCount < 1000 && (
        <Card className="modern-card bg-accent/10 border-accent/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Award size={32} className="text-accent flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-foreground">Become a Political Influencer</h3>
                <p className="text-xs text-muted-foreground">Get 1,000 profile likes to earn the Influencer badge!</p>
                <Progress value={(likesCount / 1000) * 100} className="mt-2 h-2" />
                <p className="text-xs text-right text-muted-foreground mt-1">{likesCount} / 1,000 Likes</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1 space-y-6">
          <UserProfileStatsCard profileUser={profileUser} />
        </div>

        <div className="lg:col-span-2">
          <UserActivityTabsContent profileUser={profileUser} leaders={leaders} />
        </div>
      </div>
    </motion.div>
  );
};

export default UserProfilePage;