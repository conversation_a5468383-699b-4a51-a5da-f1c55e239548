
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, X, UserPlus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const initialSuggestions = [
  { id: 1, name: '<PERSON><PERSON><PERSON><PERSON>', position: 'Minister of Power', party: 'APC', notes: 'Former CBN deputy governor. From Oyo state.' },
  { id: 2, name: '<PERSON><PERSON><PERSON> Wike', position: 'FCT Minister', party: 'PDP', notes: 'Former Governor of Rivers State.' },
  { id: 3, name: '<PERSON><PERSON>', position: '', party: 'PDP', notes: 'Ran for Lagos governor multiple times.' },
];

const AdminSuggestedLeadersPage = () => {
  const [suggestions, setSuggestions] = useState(initialSuggestions);
  const { toast } = useToast();

  const handleApprove = (suggestion) => {
    toast({
      title: "Leader Approved (Demo)",
      description: `Opening create form for ${suggestion.name}. In a real app, this would pre-fill the leader creation form.`,
    });
    // In a real app, you might navigate to the create leader page with state
    // For now, just remove it from the list
    setSuggestions(suggestions.filter(s => s.id !== suggestion.id));
  };

  const handleReject = (suggestionId) => {
    setSuggestions(suggestions.filter(s => s.id !== suggestionId));
    toast({
      title: "Suggestion Rejected",
      description: "The suggestion has been removed from the list.",
      variant: "destructive"
    });
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground flex items-center">
          <UserPlus className="mr-3 text-primary" /> Suggested Leaders
        </h1>
        <Badge variant="secondary">{suggestions.length} Pending</Badge>
      </div>
      <p className="text-muted-foreground">Review leader suggestions submitted by users. Approve to add them to the database or reject to dismiss.</p>

      {suggestions.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {suggestions.map((suggestion) => (
            <Card key={suggestion.id} className="modern-card flex flex-col">
              <CardHeader>
                <CardTitle className="text-primary">{suggestion.name}</CardTitle>
              </CardHeader>
              <CardContent className="flex-grow space-y-2 text-sm">
                <p><strong className="text-muted-foreground">Position:</strong> {suggestion.position || 'N/A'}</p>
                <p><strong className="text-muted-foreground">Party:</strong> {suggestion.party || 'N/A'}</p>
                <div>
                  <strong className="text-muted-foreground">Notes:</strong>
                  <p className="pl-2 border-l-2 border-primary/20 mt-1 italic text-foreground/80">{suggestion.notes || 'No notes provided.'}</p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                <Button variant="outline" size="sm" onClick={() => handleReject(suggestion.id)} className="border-destructive text-destructive hover:bg-destructive/10 hover:text-destructive">
                  <X size={16} className="mr-1" /> Reject
                </Button>
                <Button size="sm" onClick={() => handleApprove(suggestion)} className="btn-primary">
                  <Check size={16} className="mr-1" /> Approve
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-16 modern-card">
          <Check size={48} className="mx-auto text-green-500 mb-4" />
          <p className="text-xl font-semibold text-foreground">All Caught Up!</p>
          <p className="text-muted-foreground mt-2">There are no new leader suggestions to review.</p>
        </div>
      )}
    </div>
  );
};

export default AdminSuggestedLeadersPage;
