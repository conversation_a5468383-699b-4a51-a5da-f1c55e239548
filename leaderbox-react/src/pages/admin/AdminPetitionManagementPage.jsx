
import React, { useState, useMemo } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { PlusCircle, Edit, Trash2, Users, MapPin, Target, BookOpen } from 'lucide-react';
import { usePetitions } from '@/contexts/PetitionContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { Link } from 'react-router-dom';
import { Combobox } from '@/components/ui/combobox';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { useToast } from '@/components/ui/use-toast';

const AdminPetitionManagementPage = () => {
  const { petitions, createPetition, updatePetition, deletePetition } = usePetitions();
  const { leaders } = useLeaderData();
  const { user } = useAuth();
  const { toast } = useToast();
  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentPetition, setCurrentPetition] = useState(null);
  const [formData, setFormData] = useState({ title: '', description: '', targetSignatures: 100, taggedLeader: '', location: '' });

  const leaderOptions = useMemo(() => leaders.map(l => ({ value: l.name, label: l.name })), [leaders]);
  const stateOptions = useMemo(() => {
    const states = new Set(leaders.map(l => l.state).filter(Boolean));
    return Array.from(states).map(s => ({ value: s, label: s }));
  }, [leaders]);

  const canEditDeleteAll = user?.isAdmin;
  const petitionsToDisplay = canEditDeleteAll ? petitions : petitions.filter(p => p.createdBy === user?.name);

  const handleOpenCreateModal = () => {
    setIsEditing(false);
    setFormData({ title: '', description: '', targetSignatures: 100, taggedLeader: '', location: '' });
    setCurrentPetition(null);
    setShowModal(true);
  };

  const handleOpenEditModal = (petition) => {
    setIsEditing(true);
    setCurrentPetition(petition);
    setFormData({
      title: petition.title,
      description: petition.description || '',
      targetSignatures: petition.targetSignatures || 100,
      taggedLeader: petition.taggedLeader || '',
      location: petition.location || '',
    });
    setShowModal(true);
  };

  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({ ...prev, [name]: type === 'number' ? parseInt(value, 10) : value }));
  };

  const handleComboboxChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    if (!formData.title || !formData.description || formData.targetSignatures < 100) {
      toast({ title: "Error", description: "Title, description, and a target of at least 100 signatures are required.", variant: "destructive" });
      return;
    }
    if (isEditing && currentPetition) {
      updatePetition(currentPetition.id, formData);
      toast({ title: "Petition Updated", description: `Petition "${formData.title}" has been updated.` });
    } else {
      createPetition({ ...formData, createdBy: user.name });
      toast({ title: "Petition Created", description: `Petition "${formData.title}" has been created.` });
    }
    setShowModal(false);
  };

  const handleDelete = (petitionId, petitionTitle) => {
    if (window.confirm(`Are you sure you want to delete the petition "${petitionTitle}"?`)) {
      deletePetition(petitionId);
      toast({ title: "Petition Deleted", description: `Petition "${petitionTitle}" has been deleted.` });
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground">Manage Petitions</h1>
        <Button onClick={handleOpenCreateModal} className="btn-primary">
          <PlusCircle size={18} className="mr-2" /> Create New Petition
        </Button>
      </div>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Petition List ({petitionsToDisplay.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {petitionsToDisplay.length > 0 ? (
            <ul className="space-y-3">
              {petitionsToDisplay.map((petition) => (
                <li key={petition.id} className="p-3 bg-secondary/30 rounded-md hover:bg-secondary/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <Link to={`/petition/${petition.id}`} className="font-semibold text-foreground hover:underline">{petition.title}</Link>
                      <p className="text-xs text-muted-foreground">
                        By: {petition.createdBy} | {petition.signatures} / {petition.targetSignatures} signatures
                      </p>
                    </div>
                    <div className="space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleOpenEditModal(petition)} className="button-outline-override">
                        <Edit size={14} className="mr-1" /> Edit
                      </Button>
                      <Button variant="destructive" size="sm" onClick={() => handleDelete(petition.id, petition.title)}>
                        <Trash2 size={14} className="mr-1" /> Delete
                      </Button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground text-center py-4">No petitions to display.</p>
          )}
        </CardContent>
      </Card>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="sm:max-w-lg bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-2xl flex items-center">
              <BookOpen size={22} className="mr-2" /> {isEditing ? 'Edit Petition' : 'Create New Petition'}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-2 text-sm">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input id="title" name="title" value={formData.title} onChange={handleChange} className="modern-input mt-1" />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" name="description" value={formData.description} onChange={handleChange} className="modern-input mt-1 min-h-[120px]" />
            </div>
            <div>
              <Label htmlFor="targetSignatures" className="flex items-center"><Target size={14} className="mr-1.5" /> Target Signatures</Label>
              <Input id="targetSignatures" name="targetSignatures" type="number" min="100" value={formData.targetSignatures} onChange={handleChange} className="modern-input mt-1" />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="taggedLeader" className="flex items-center"><Users size={14} className="mr-1.5" /> Tag Leader</Label>
                <Combobox options={leaderOptions} value={formData.taggedLeader} onChange={(val) => handleComboboxChange('taggedLeader', val)} placeholder="Select Leader" inputClassName="modern-input mt-1" />
              </div>
              <div>
                <Label htmlFor="location" className="flex items-center"><MapPin size={14} className="mr-1.5" /> Tag Location</Label>
                <Combobox options={stateOptions} value={formData.location} onChange={(val) => handleComboboxChange('location', val)} placeholder="Select Location" inputClassName="modern-input mt-1" />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild><Button type="button" variant="outline">Cancel</Button></DialogClose>
            <Button type="button" onClick={handleSubmit} className="btn-primary">{isEditing ? 'Save Changes' : 'Create Petition'}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPetitionManagementPage;
