import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail, Save, ShieldAlert, Eye, EyeOff } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";

const AdminEmailSettingsPage = () => {
  const [zeptomailKey, setZeptomailKey] = useState('');
  const [fromEmail, setFromEmail] = useState('<EMAIL>');
  const [showKey, setShowKey] = useState(false);
  const { toast } = useToast();

  const handleSaveSettings = () => {
    if (!zeptomailKey.trim() || !fromEmail.trim()) {
      toast({ title: "Error", description: "Both ZeptoMail API Key and From Email are required.", variant: "destructive" });
      return;
    }
    // In a real app, these would be saved securely, likely via a backend.
    console.log("ZeptoMail Key (Demo):", zeptomailKey);
    console.log("From Email (Demo):", fromEmail);
    toast({ title: "Settings Saved (Demo)", description: "Email settings have been updated. This is a frontend demonstration." });
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-6 max-w-2xl">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground flex items-center"><Mail className="mr-2" />Email Delivery Settings</h1>
      </div>
      <p className="text-sm text-muted-foreground">Configure settings for sending transactional emails via ZeptoMail.</p>

       <Card className="modern-card bg-yellow-50 border-yellow-300">
        <CardHeader className="flex flex-row items-start space-x-3">
          <ShieldAlert className="h-8 w-8 text-yellow-600 flex-shrink-0 mt-1" />
          <div>
            <CardTitle className="text-yellow-700">Important Security Note</CardTitle>
            <CardDescription className="text-yellow-600 text-xs">
              API keys are sensitive credentials. In a production environment, these keys should NEVER be stored or managed directly in frontend code. They must be handled securely on a backend server and accessed via environment variables or a secure secrets management service. This page is for demonstration purposes only.
            </CardDescription>
          </div>
        </CardHeader>
      </Card>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">ZeptoMail Configuration</CardTitle>
          <CardDescription>Enter your ZeptoMail API key and default "From" email address.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="zeptomail-key">ZeptoMail Send Mail Token (API Key)</Label>
            <div className="relative">
                <Input 
                id="zeptomail-key" 
                type={showKey ? "text" : "password"}
                value={zeptomailKey} 
                onChange={(e) => setZeptomailKey(e.target.value)} 
                placeholder="Enter your ZeptoMail API Key"
                className="modern-input mt-1 pr-10"
                />
                <Button type="button" variant="ghost" size="icon" className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 text-muted-foreground" onClick={() => setShowKey(!showKey)}>
                    {showKey ? <EyeOff size={16} /> : <Eye size={16} />}
                </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-1">This key is used to authenticate with the ZeptoMail API.</p>
          </div>
          <div>
            <Label htmlFor="from-email">Default "From" Email Address</Label>
            <Input 
              id="from-email" 
              type="email"
              value={fromEmail} 
              onChange={(e) => setFromEmail(e.target.value)} 
              placeholder="e.g., <EMAIL>"
              className="modern-input mt-1"
            />
            <p className="text-xs text-muted-foreground mt-1">This email will appear as the sender for platform notifications.</p>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveSettings} className="btn-primary">
            <Save size={18} className="mr-2" /> Save Settings
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AdminEmailSettingsPage;