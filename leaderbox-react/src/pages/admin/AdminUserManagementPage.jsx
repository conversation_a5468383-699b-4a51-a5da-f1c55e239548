import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { PlusCircle, Edit, Trash2, UserCog } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";

// Placeholder - In a real app, this would come from AuthContext or a backend
const initialAdminUsers = [
  { id: 'adminDemo456', email: "<EMAIL>", name: "Admin User", role: "Overall Admin"},
  { id: 'editorDemo789', email: "<EMAIL>", name: "Editor User", role: "Editor"},
  { id: 'creatorDemo101', email: "<EMAIL>", name: "Creator User", role: "Content Creator"}
];


const AdminUserManagementPage = () => {
  const [adminUsers, setAdminUsers] = useState(initialAdminUsers);
  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [formData, setFormData] = useState({ email: '', name: '', role: 'Content Creator' });
  const { toast } = useToast();

  const roles = ["Editor", "Content Creator"]; // Overall Admin cannot be assigned via this form for safety

  const handleOpenCreateModal = () => {
    setIsEditing(false);
    setFormData({ email: '', name: '', role: 'Content Creator' });
    setCurrentUser(null);
    setShowModal(true);
  };

  const handleOpenEditModal = (user) => {
    if(user.role === "Overall Admin") {
      toast({title: "Action Denied", description: "Overall Admin role cannot be edited here.", variant: "destructive"});
      return;
    }
    setIsEditing(true);
    setCurrentUser(user);
    setFormData({ email: user.email, name: user.name, role: user.role });
    setShowModal(true);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleChange = (value) => {
    setFormData(prev => ({ ...prev, role: value }));
  };

  const handleSubmit = () => {
     if (!formData.email || !formData.name || !formData.role) {
        toast({ title: "Error", description: "All fields are required.", variant: "destructive" });
        return;
    }
    if (isEditing && currentUser) {
      setAdminUsers(prev => prev.map(u => u.id === currentUser.id ? { ...currentUser, ...formData } : u));
      toast({ title: "Admin User Updated", description: `User ${formData.name} updated.` });
    } else {
      const newUser = { id: `adminUser${Date.now()}`, ...formData };
      setAdminUsers(prev => [...prev, newUser]);
      toast({ title: "Admin User Created", description: `User ${formData.name} created. An email simulation would be sent to set password.` });
    }
    setShowModal(false);
  };
  
  const handleDeleteUser = (userId, userName, userRole) => {
     if(userRole === "Overall Admin") {
      toast({title: "Action Denied", description: "Overall Admin cannot be deleted.", variant: "destructive"});
      return;
    }
    if(window.confirm(`Are you sure you want to delete admin user ${userName}? This action is irreversible.`)) {
      setAdminUsers(prev => prev.filter(u => u.id !== userId));
      toast({ title: "Admin User Deleted", description: `User ${userName} has been deleted.` });
    }
  };


  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground flex items-center"><UserCog className="mr-2" />Manage Admin Users</h1>
        <Button onClick={handleOpenCreateModal} className="btn-primary">
          <PlusCircle size={18} className="mr-2" /> Create New Admin User
        </Button>
      </div>
       <p className="text-sm text-muted-foreground">Only "Overall Admin" can manage other admin users (Editors, Content Creators).</p>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Admin User List ({adminUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {adminUsers.length > 0 ? (
            <ul className="space-y-3">
              {adminUsers.map((adminUser) => (
                <li key={adminUser.id} className="flex items-center justify-between p-3 bg-secondary/30 rounded-md">
                  <div>
                    <p className="font-semibold text-foreground">{adminUser.name} <span className="text-xs text-muted-foreground">({adminUser.email})</span></p>
                    <p className="text-sm text-accent">{adminUser.role}</p>
                  </div>
                  <div className="space-x-2">
                    {adminUser.role !== "Overall Admin" && (
                        <>
                        <Button variant="outline" size="sm" onClick={() => handleOpenEditModal(adminUser)} className="button-outline-override">
                            <Edit size={14} className="mr-1" /> Edit
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteUser(adminUser.id, adminUser.name, adminUser.role)}>
                            <Trash2 size={14} className="mr-1" /> Delete
                        </Button>
                        </>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground text-center py-4">No admin users found.</p>
          )}
        </CardContent>
      </Card>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="sm:max-w-lg bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-2xl">{isEditing ? 'Edit Admin User' : 'Create New Admin User'}</DialogTitle>
            <DialogDescription>
              {isEditing ? `Update details for ${currentUser?.name}.` : 'Enter details for the new admin user. Password will be set via email.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 text-sm">
            <div><Label htmlFor="name">Full Name</Label><Input id="name" name="name" value={formData.name} onChange={handleChange} className="modern-input mt-1"/></div>
            <div><Label htmlFor="email">Email Address</Label><Input id="email" name="email" type="email" value={formData.email} onChange={handleChange} className="modern-input mt-1" disabled={isEditing}/></div>
            <div>
              <Label htmlFor="role">Role</Label>
              <Select value={formData.role} onValueChange={handleRoleChange}>
                <SelectTrigger className="w-full modern-input mt-1">
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map(role => (
                    <SelectItem key={role} value={role}>{role}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild><Button type="button" variant="outline">Cancel</Button></DialogClose>
            <Button type="button" onClick={handleSubmit} className="btn-primary">{isEditing ? 'Save Changes' : 'Create User'}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminUserManagementPage;