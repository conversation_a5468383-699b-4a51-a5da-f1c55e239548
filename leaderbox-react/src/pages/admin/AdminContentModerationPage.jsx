import React, { useState } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, CheckCircle, XCircle, Eye } from 'lucide-react';

const AdminContentModerationPage = () => {
  // Placeholder data - replace with actual data fetching
  const [flaggedComments, setFlaggedComments] = useState([
    { id: 'fc1', banterId: 'b1', pollId: null, text: "This is an inappropriate comment on a banter.", author: "UserX", timestamp: new Date().toISOString(), flaggedBy: "UserY" },
    { id: 'fc2', banterId: null, pollId: 'p2', text: "Spam link in poll comment: http://spam.com", author: "UserZ", timestamp: new Date(Date.now() - 3600000).toISOString(), flaggedBy: "UserA" },
  ]);
  const [flaggedBanter, setFlaggedBanter] = useState([
    { id: 'fb1', title: "Highly offensive banter topic", author: "UserB", timestamp: new Date().toISOString(), reason: "Hate speech" },
  ]);

  const handleApproveComment = (commentId) => alert(`Approve comment ${commentId}`);
  const handleDeleteComment = (commentId) => alert(`Delete comment ${commentId}`);
  const handleViewContent = (contentId, type) => alert(`View ${type} ${contentId}`);
  const handleApproveBanter = (banterId) => alert(`Approve banter ${banterId}`);
  const handleDeleteBanter = (banterId) => alert(`Delete banter ${banterId}`);


  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <h1 className="text-2xl font-bold text-foreground">Content Moderation</h1>
      <p className="text-muted-foreground">Review and manage flagged comments, banters, and other user-generated content.</p>
      
      <Tabs defaultValue="comments" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-auto md:inline-flex bg-secondary/50 rounded-lg p-1 mb-6">
          <TabsTrigger value="comments">Flagged Comments ({flaggedComments.length})</TabsTrigger>
          <TabsTrigger value="banters">Flagged Banters ({flaggedBanter.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="comments">
            <Card className="modern-card">
                <CardHeader><CardTitle className="text-primary">Flagged Comments for Review</CardTitle></CardHeader>
                <CardContent>
                {flaggedComments.length > 0 ? (
                    <ul className="space-y-3">
                    {flaggedComments.map(comment => (
                        <li key={comment.id} className="p-3 bg-secondary/30 rounded-md">
                        <p className="text-sm text-foreground italic">"{comment.text}"</p>
                        <p className="text-xs text-muted-foreground mt-1">
                            Author: {comment.author} | Flagged by: {comment.flaggedBy} | On: {comment.banterId ? `Banter ${comment.banterId}` : `Poll ${comment.pollId}`}
                        </p>
                        <div className="mt-2 space-x-2">
                            <Button size="sm" variant="outline" className="border-green-500 text-green-600 hover:bg-green-500/10" onClick={() => handleApproveComment(comment.id)}><CheckCircle size={14} className="mr-1"/> Approve</Button>
                            <Button size="sm" variant="destructive" onClick={() => handleDeleteComment(comment.id)}><XCircle size={14} className="mr-1"/> Delete</Button>
                            <Button size="sm" variant="ghost" onClick={() => handleViewContent(comment.banterId || comment.pollId, comment.banterId ? 'Banter' : 'Poll')}><Eye size={14} className="mr-1"/> View Original</Button>
                        </div>
                        </li>
                    ))}
                    </ul>
                ) : (
                    <p className="text-muted-foreground text-center py-4">No flagged comments to review.</p>
                )}
                </CardContent>
            </Card>
        </TabsContent>
        <TabsContent value="banters">
             <Card className="modern-card">
                <CardHeader><CardTitle className="text-primary">Flagged Banters for Review</CardTitle></CardHeader>
                <CardContent>
                {flaggedBanter.length > 0 ? (
                    <ul className="space-y-3">
                    {flaggedBanter.map(banter => (
                        <li key={banter.id} className="p-3 bg-secondary/30 rounded-md">
                        <p className="text-sm font-semibold text-foreground">{banter.title}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                            Author: {banter.author} | Reason: {banter.reason}
                        </p>
                        <div className="mt-2 space-x-2">
                            <Button size="sm" variant="outline" className="border-green-500 text-green-600 hover:bg-green-500/10" onClick={() => handleApproveBanter(banter.id)}><CheckCircle size={14} className="mr-1"/> Approve</Button>
                            <Button size="sm" variant="destructive" onClick={() => handleDeleteBanter(banter.id)}><XCircle size={14} className="mr-1"/> Delete</Button>
                            <Button size="sm" variant="ghost" onClick={() => handleViewContent(banter.id, 'Banter')}><Eye size={14} className="mr-1"/> View Banter</Button>
                        </div>
                        </li>
                    ))}
                    </ul>
                ) : (
                    <p className="text-muted-foreground text-center py-4">No flagged banters to review.</p>
                )}
                </CardContent>
            </Card>
        </TabsContent>
      </Tabs>
      <p className="text-xs text-muted-foreground">Feature Guide: Overall Admins and Editors can review content flagged by users or the system. Approve to keep content, delete to remove it permanently.</p>
    </div>
  );
};

export default AdminContentModerationPage;