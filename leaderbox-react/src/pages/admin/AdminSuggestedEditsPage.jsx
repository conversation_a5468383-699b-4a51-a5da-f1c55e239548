import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Eye, MessageSquare, Edit3 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const mockSuggestions = [
  {
    id: 'sugg1',
    leaderName: '<PERSON><PERSON><PERSON><PERSON>',
    leaderId: '1',
    userName: 'CivicCitizen12',
    userId: 'userDemo123',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    status: 'Pending',
    reason: "Leader's position changed as per official government website: [link to source]",
    changes: {
      position: { old: 'Minister of Power', new: 'Minister of Power & Steel Development' },
      bio: { old: '<PERSON><PERSON><PERSON><PERSON> is a Nigerian politician...', new: '<PERSON><PERSON><PERSON><PERSON>, FCA, is a prominent Nigerian politician and financial expert...' }
    }
  },
  {
    id: 'sugg2',
    leaderName: '<PERSON><PERSON><PERSON> Wike',
    leaderId: '2',
    userName: 'PoliticsWatcher22',
    userId: 'user456',
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
    status: 'Approved',
    reason: "Updated bio with recent achievements mentioned in a Channels TV interview.",
    changes: {
      detailedBio: { old: 'Former Governor of Rivers State...', new: 'Former Governor of Rivers State, now serving as the FCT Minister. Known for extensive infrastructure projects...' }
    }
  },
  {
    id: 'sugg3',
    leaderName: 'Peter Obi',
    leaderId: '3',
    userName: 'TruthSeeker007',
    userId: 'user789',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    status: 'Rejected',
    reason: "User suggested changing party affiliation without providing a credible source. Maintained current party.",
    moderatorNotes: "No evidence for party change. Closed.",
    changes: {
      party: { old: 'Labour Party', new: 'PDP' }
    }
  }
];


const AdminSuggestedEditsPage = () => {
  const [suggestions, setSuggestions] = useState(mockSuggestions);
  const { toast } = useToast();

  const handleApprove = (suggestionId) => {
    setSuggestions(prev => prev.map(s => s.id === suggestionId ? { ...s, status: 'Approved' } : s));
    toast({ title: "Suggestion Approved", description: "The leader's profile will be updated (conceptually)." });
  };

  const handleReject = (suggestionId) => {
    const reason = prompt("Enter reason for rejection (optional):");
    setSuggestions(prev => prev.map(s => s.id === suggestionId ? { ...s, status: 'Rejected', moderatorNotes: reason || s.moderatorNotes } : s));
    toast({ title: "Suggestion Rejected", description: "The suggestion has been marked as rejected." });
  };

  const renderChange = (key, change) => {
    return (
      <div key={key} className="text-xs">
        <strong className="capitalize text-primary/80">{key.replace(/([A-Z])/g, ' $1')}:</strong>
        <div className="ml-2 p-1.5 bg-red-100/50 dark:bg-red-900/20 border border-red-300/50 dark:border-red-700/30 rounded-md my-0.5">
          <span className="text-red-600 dark:text-red-400 line-through">Old: {String(change.old)}</span>
        </div>
        <div className="ml-2 p-1.5 bg-green-100/50 dark:bg-green-900/20 border border-green-300/50 dark:border-green-700/30 rounded-md my-0.5">
          <span className="text-green-600 dark:text-green-400">New: {String(change.new)}</span>
        </div>
      </div>
    );
  };


  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground flex items-center">
          <Edit3 size={24} className="mr-3 text-primary"/> Manage Suggested Edits
        </h1>
      </div>

      {suggestions.length === 0 ? (
        <Card className="modern-card">
          <CardContent className="py-10 text-center text-muted-foreground">
            <MessageSquare size={48} className="mx-auto mb-4 opacity-50" />
            No pending suggestions at the moment.
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {suggestions.map((suggestion) => (
            <Card key={suggestion.id} className="modern-card">
              <CardHeader className="flex flex-row justify-between items-start pb-3">
                <div>
                  <CardTitle className="text-lg text-primary">
                    Edit Suggestion for: {suggestion.leaderName}
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">
                    Suggested by: {suggestion.userName} (ID: {suggestion.userId}) on {new Date(suggestion.timestamp).toLocaleString()}
                  </p>
                </div>
                <Badge 
                  variant={suggestion.status === 'Approved' ? 'default' : suggestion.status === 'Rejected' ? 'destructive' : 'secondary'}
                  className={`capitalize text-xs ${suggestion.status === 'Approved' ? 'bg-green-500 hover:bg-green-600' : suggestion.status === 'Rejected' ? 'bg-red-500 hover:bg-red-600' : 'bg-amber-500 hover:bg-amber-600'} text-white`}
                >
                  {suggestion.status}
                </Badge>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div>
                  <strong className="text-foreground/90">Reason/Source Provided:</strong>
                  <p className="text-muted-foreground bg-secondary/30 p-2 rounded-md mt-1 text-xs">{suggestion.reason}</p>
                </div>
                <div className="border-t border-border pt-3">
                  <strong className="text-foreground/90">Suggested Changes:</strong>
                  <div className="mt-1 space-y-1.5 max-h-40 overflow-y-auto p-2 rounded-md bg-background border border-dashed">
                    {Object.entries(suggestion.changes).map(([key, value]) => renderChange(key, value))}
                  </div>
                </div>
                {suggestion.moderatorNotes && (
                  <div>
                    <strong className="text-foreground/90">Moderator Notes:</strong>
                    <p className="text-muted-foreground bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-md mt-1 text-xs">{suggestion.moderatorNotes}</p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                <Button variant="outline" size="sm" onClick={() => toast({ title: "View Profile Action", description: "🚧 This feature isn't implemented yet—but don't worry! You can request it in your next prompt! 🚀" })}>
                  <Eye size={16} className="mr-1.5" /> View Leader Profile
                </Button>
                {suggestion.status === 'Pending' && (
                  <>
                    <Button variant="outline" size="sm" className="text-green-600 border-green-500 hover:bg-green-50 hover:text-green-700 dark:hover:bg-green-900/30" onClick={() => handleApprove(suggestion.id)}>
                      <CheckCircle size={16} className="mr-1.5" /> Approve
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-600 border-red-500 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-900/30" onClick={() => handleReject(suggestion.id)}>
                      <XCircle size={16} className="mr-1.5" /> Reject
                    </Button>
                  </>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
      <p className="text-xs text-muted-foreground mt-4">
        Feature Guide: Review user-submitted suggestions for leader profile updates. Approve to apply changes (conceptually), or reject with a reason.
      </p>
    </div>
  );
};

export default AdminSuggestedEditsPage;