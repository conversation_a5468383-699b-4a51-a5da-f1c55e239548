
import React, { useState, useMemo } from 'react';
import { Card, CardHeader, Card<PERSON>itle, CardContent } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { PlusCircle, Edit, Trash2, Globe, Lock, ShieldCheck, UserSquare } from 'lucide-react';
import { useGroups } from '@/contexts/GroupContext.jsx';
import { useToast } from '@/components/ui/use-toast';
import { Link } from 'react-router-dom';
import { Switch } from '@/components/ui/switch';

const AdminGroupsManagementPage = () => {
  const { groups, createGroup } = useGroups();
  const { toast } = useToast();
  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentGroup, setCurrentGroup] = useState(null);
  const [formData, setFormData] = useState({ name: '', description: '', isPublic: true, tags: { leader: '', location: '', party: '', keywords: [] } });

  const handleOpenCreateModal = () => {
    setIsEditing(false);
    setFormData({ name: '', description: '', isPublic: true, tags: { leader: '', location: '', party: '', keywords: [] } });
    setCurrentGroup(null);
    setShowModal(true);
  };
  
  const handleOpenEditModal = (group) => {
    setIsEditing(true);
    setCurrentGroup(group);
    setFormData({
      name: group.name,
      description: group.description,
      isPublic: group.isPublic,
      tags: {
        leader: group.tags.leader || '',
        location: group.tags.location || '',
        party: group.tags.party || '',
        keywords: group.tags.keywords || [],
      }
    });
    setShowModal(true);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith("tag-")) {
      const tagName = name.split("-")[1];
      if (tagName === 'keywords') {
        setFormData(prev => ({...prev, tags: {...prev.tags, keywords: value.split(',').map(kw => kw.trim()).filter(Boolean)}}));
      } else {
        setFormData(prev => ({...prev, tags: {...prev.tags, [tagName]: value}}));
      }
    } else {
      setFormData(prev => ({...prev, [name]: value}));
    }
  };

  const handleSubmit = () => {
    if (!formData.name || !formData.description) {
      toast({ title: "Error", description: "Group name and description are required.", variant: "destructive" });
      return;
    }
    if (isEditing) {
      // updateGroup(currentGroup.id, formData);
      toast({ title: "Group Updated (Demo)", description: `Group "${formData.name}" has been updated.` });
    } else {
      createGroup(formData);
      toast({ title: "Group Created (Demo)", description: `Group "${formData.name}" has been created.` });
    }
    setShowModal(false);
  };

  const handleDeleteGroup = (groupId, groupName) => {
    if (window.confirm(`Are you sure you want to delete the group "${groupName}"? This is irreversible.`)) {
      // deleteGroup(groupId);
      toast({ title: "Group Deleted (Demo)", description: `Group "${groupName}" has been deleted.` });
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground flex items-center"><UserSquare className="mr-2"/> Manage Groups</h1>
        <Button onClick={handleOpenCreateModal} className="btn-primary">
          <PlusCircle size={18} className="mr-2" /> Create New Group
        </Button>
      </div>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Group List ({groups.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3">
            {groups.map(group => (
              <li key={group.id} className="p-3 bg-secondary/30 rounded-md hover:bg-secondary/50 transition-colors">
                <div className="flex items-center justify-between">
                  <div>
                    <Link to={`/group/${group.id}`} className="font-semibold text-foreground hover:underline">{group.name}</Link>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>By: {group.creatorName}</span>
                      <span>&bull;</span>
                      <span>{group.members.length} Members</span>
                      <span className="flex items-center gap-1">
                        &bull;
                        {group.isPublic ? <Globe size={12}/> : <Lock size={12}/>}
                        {group.isPublic ? 'Public' : 'Private'}
                      </span>
                    </div>
                  </div>
                  <div className="space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleOpenEditModal(group)} className="button-outline-override"><Edit size={14} className="mr-1"/> Edit</Button>
                    <Button variant="destructive" size="sm" onClick={() => handleDeleteGroup(group.id, group.name)}><Trash2 size={14} className="mr-1"/> Delete</Button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="sm:max-w-lg bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-2xl">{isEditing ? 'Edit Group' : 'Create New Group'}</DialogTitle>
            <DialogDescription>{isEditing ? `Update details for "${formData.name}"` : 'Set up a new group for users to join and discuss.'}</DialogDescription>
          </DialogHeader>
          <div className="grid gap-3 py-4 max-h-[70vh] overflow-y-auto pr-2 text-sm">
            <Label htmlFor="name">Group Name</Label>
            <Input id="name" name="name" value={formData.name} onChange={handleChange} className="modern-input"/>
            <Label htmlFor="description">Description</Label>
            <Textarea id="description" name="description" value={formData.description} onChange={handleChange} className="modern-input min-h-[80px]"/>
            <div className="flex items-center space-x-2 mt-2">
              <Switch id="isPublic" checked={formData.isPublic} onCheckedChange={(checked) => setFormData(p => ({...p, isPublic: checked}))}/>
              <Label htmlFor="isPublic">{formData.isPublic ? 'Public Group' : 'Private Group'}</Label>
            </div>
            
            <h4 className="font-semibold text-base mt-2">Tags (Optional)</h4>
            <Label htmlFor="tag-leader">Tag Leader</Label><Input id="tag-leader" name="tag-leader" value={formData.tags.leader} onChange={handleChange} className="modern-input" placeholder="e.g., Peter Obi"/>
            <Label htmlFor="tag-location">Tag Location</Label><Input id="tag-location" name="tag-location" value={formData.tags.location} onChange={handleChange} className="modern-input" placeholder="e.g., Lagos"/>
            <Label htmlFor="tag-party">Tag Party</Label><Input id="tag-party" name="tag-party" value={formData.tags.party} onChange={handleChange} className="modern-input" placeholder="e.g., LP"/>
            <Label htmlFor="tag-keywords">Keywords (comma-separated)</Label><Input id="tag-keywords" name="tag-keywords" value={formData.tags.keywords.join(', ')} onChange={handleChange} className="modern-input" placeholder="e.g., economy, security, youths"/>
          </div>
          <DialogFooter>
            <DialogClose asChild><Button type="button" variant="outline">Cancel</Button></DialogClose>
            <Button type="button" onClick={handleSubmit} className="btn-primary">{isEditing ? 'Save Changes' : 'Create Group'}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminGroupsManagementPage;
