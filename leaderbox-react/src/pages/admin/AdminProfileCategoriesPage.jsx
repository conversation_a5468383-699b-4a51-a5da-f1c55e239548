import React, { useState } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.jsx';
import { PlusCircle, Edit, Trash2, ListFilter } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const initialCategories = {
  parties: ["APC", "PDP", "LP", "NNPP", "APGA"],
  positions: ["Governor", "Senator", "Minister", "President", "Presidential Candidate", "Former Governor", "House of Reps", "State Assembly"],
  locations: ["Abia", "Adamawa", "Akwa Ibom", "Anambra", "Bauchi", "Bayelsa", "Benue", "Borno", "Cross River", "Delta", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ug<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kadu<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ko<PERSON>", "Kwara", "Lagos", "Nasarawa", "Niger", "Ogun", "Ondo", "Osun", "Oyo", "Plateau", "Rivers", "Sokoto", "Taraba", "Yobe", "Zamfara", "Federal Capital Territory"]
};

const CategoryManager = ({ title, items, setItems, categoryKey }) => {
  const [newItem, setNewItem] = useState('');
  const [editingItem, setEditingItem] = useState({ index: -1, value: '' });
  const { toast } = useToast();

  const handleAddItem = () => {
    if (newItem.trim() === '') {
      toast({ title: "Cannot add empty item", variant: "destructive" });
      return;
    }
    if (items.includes(newItem.trim())) {
      toast({ title: "Item already exists", variant: "destructive" });
      return;
    }
    setItems(prev => [...prev, newItem.trim()]);
    setNewItem('');
    toast({ title: `${title.slice(0,-1)} Added`, description: `"${newItem.trim()}" added to ${title}.` });
  };

  const handleEditItem = (index) => {
    setEditingItem({ index, value: items[index] });
  };

  const handleSaveEdit = () => {
    if (editingItem.value.trim() === '') {
      toast({ title: "Cannot save empty item", variant: "destructive" });
      return;
    }
    const updatedItems = [...items];
    updatedItems[editingItem.index] = editingItem.value.trim();
    setItems(updatedItems);
    setEditingItem({ index: -1, value: '' });
    toast({ title: `${title.slice(0,-1)} Updated`, description: `Item updated to "${editingItem.value.trim()}".` });
  };

  const handleDeleteItem = (indexToDelete) => {
    setItems(prev => prev.filter((_, index) => index !== indexToDelete));
    toast({ title: `${title.slice(0,-1)} Deleted`, description: `Item removed from ${title}.` });
  };

  return (
    <Card className="modern-card">
      <CardHeader>
        <CardTitle className="text-lg text-primary">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 mb-4">
          <Input 
            value={newItem} 
            onChange={(e) => setNewItem(e.target.value)} 
            placeholder={`New ${title.slice(0,-1)}...`} 
            className="modern-input"
          />
          <Button onClick={handleAddItem} className="btn-primary shrink-0"><PlusCircle size={16} className="mr-1.5"/> Add</Button>
        </div>
        {editingItem.index !== -1 && (
          <div className="flex gap-2 mb-4 p-3 bg-secondary/50 rounded-md border border-dashed">
            <Input 
              value={editingItem.value} 
              onChange={(e) => setEditingItem(prev => ({ ...prev, value: e.target.value }))} 
              placeholder="Edit item..." 
              className="modern-input"
            />
            <Button onClick={handleSaveEdit} size="sm" className="btn-primary shrink-0">Save Edit</Button>
            <Button onClick={() => setEditingItem({ index: -1, value: '' })} size="sm" variant="outline">Cancel</Button>
          </div>
        )}
        <div className="max-h-60 overflow-y-auto pr-1">
          {items.length > 0 ? (
            <Table>
              <TableHeader className="sticky top-0 bg-card z-10">
                <TableRow>
                  <TableHead className="w-[70%]">{title.slice(0,-1)} Name</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={`${categoryKey}-${index}`}>
                    <TableCell>{item}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon" onClick={() => handleEditItem(index)} className="h-8 w-8 text-muted-foreground hover:text-primary"><Edit size={14}/></Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteItem(index)} className="h-8 w-8 text-destructive hover:bg-destructive/10"><Trash2 size={14}/></Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-3">No {title.toLowerCase()} added yet.</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const AdminProfileCategoriesPage = () => {
  const [parties, setParties] = useState(initialCategories.parties);
  const [positions, setPositions] = useState(initialCategories.positions);
  const [locations, setLocations] = useState(initialCategories.locations);
  
  const { toast } = useToast();

  const handleSaveChanges = () => {
    localStorage.setItem('leaderboxAdminCategories', JSON.stringify({ parties, positions, locations }));
    toast({ title: "Categories Saved", description: "All category changes have been saved to localStorage (conceptually)." });
  };

  React.useEffect(() => {
    const savedCategories = localStorage.getItem('leaderboxAdminCategories');
    if (savedCategories) {
      try {
        const parsed = JSON.parse(savedCategories);
        if (parsed.parties) setParties(parsed.parties);
        if (parsed.positions) setPositions(parsed.positions);
        if (parsed.locations) setLocations(parsed.locations);
      } catch (error) {
        console.error("Failed to parse categories from localStorage", error);
        toast({ title: "Error Loading Categories", description: "Could not load saved categories. Using defaults.", variant: "destructive" });
      }
    }
  }, [toast]);


  return (
    <div className="container mx-auto py-8 px-4 space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl font-bold text-foreground flex items-center">
          <ListFilter size={24} className="mr-3 text-primary"/> Manage Profile Filter Categories
        </h1>
        <Button onClick={handleSaveChanges} className="btn-accent self-start sm:self-auto">Save All Changes</Button>
      </div>
      <p className="text-sm text-muted-foreground">
        Manage the categories used for filtering leader profiles (e.g., on the homepage). Add, edit, or remove items as needed. These changes impact dropdowns and filter logic across the site.
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <CategoryManager title="Political Parties" items={parties} setItems={setParties} categoryKey="parties" />
        <CategoryManager title="Positions" items={positions} setItems={setPositions} categoryKey="positions" />
        <CategoryManager title="Locations (States)" items={locations} setItems={setLocations} categoryKey="locations" />
      </div>
    </div>
  );
};

export default AdminProfileCategoriesPage;