import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { PlusCircle, Edit, Trash2, ShieldCheck } from 'lucide-react';
import { useBanter } from '@/contexts/BanterContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { Link } from 'react-router-dom';

const AdminBanterManagementPage = () => {
  const { banters, addBanter, deleteBanter: contextDeleteBanter, updateBanter } = useBanter();
  const { user } = useAuth();
  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentBanter, setCurrentBanter] = useState(null);
  const [formData, setFormData] = useState({ title: '', details: '', tags: { leader: '', location: '', party: '', topic: ''}, sourceLink: '' });


  const canEditDeleteAll = user?.role === 'Overall Admin' || user?.role === 'Editor';
  const bantersToDisplay = canEditDeleteAll ? banters : banters.filter(b => b.authorId === user?.id);

  const handleOpenCreateModal = () => {
    setIsEditing(false);
    setFormData({ title: '', details: '', tags: { leader: '', location: '', party: '', topic: ''}, sourceLink: '' });
    setCurrentBanter(null);
    setShowModal(true);
  };

  const handleOpenEditModal = (banter) => {
    setIsEditing(true);
    setCurrentBanter(banter);
    setFormData({ ...banter });
    setShowModal(true);
  };
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith("tag-")) {
      const tagName = name.split("-")[1];
      setFormData(prev => ({ ...prev, tags: { ...prev.tags, [tagName]: value } }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = () => {
    if (!formData.title || !formData.details) {
        alert("Title and Details are required.");
        return;
    }
    if (isEditing && currentBanter) {
      // updateBanter(currentBanter.id, formData); // This function needs to be implemented in BanterContext
      alert(`Updated banter: ${formData.title}. (This is a demo, data persistence not fully implemented for admin edits yet)`);
    } else {
      addBanter(formData);
      alert(`Created banter: ${formData.title}.`);
    }
    setShowModal(false);
  };

  const handleDeleteBanter = (banterId, banterTitle) => {
    if(window.confirm(`Are you sure you want to delete the banter "${banterTitle}"?`)) {
        contextDeleteBanter(banterId);
        alert(`Banter "${banterTitle}" deleted by admin.`);
    }
  };
  const handleModerateComments = (banterId) => alert(`Moderate comments for banter ${banterId}`);

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground">Manage Banters</h1>
        { (user?.role === 'Overall Admin' || user?.role === 'Editor' || user?.role === 'Content Creator') &&
            <Button onClick={handleOpenCreateModal} className="btn-primary">
            <PlusCircle size={18} className="mr-2" /> Create New Banter
            </Button>
        }
      </div>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Banter List ({bantersToDisplay.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {bantersToDisplay.length > 0 ? (
            <ul className="space-y-3">
              {bantersToDisplay.map((banter) => (
                <li key={banter.id} className="p-3 bg-secondary/30 rounded-md hover:bg-secondary/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <Link to={`/banter/${banter.id}`} className="font-semibold text-foreground hover:underline">{banter.title}</Link>
                      <p className="text-xs text-muted-foreground">By: {banter.authorName} | Comments: {banter.comments?.length || 0}</p>
                    </div>
                    <div className="space-x-2">
                      {canEditDeleteAll || banter.authorId === user?.id ? (
                        <>
                          <Button variant="outline" size="sm" onClick={() => handleOpenEditModal(banter)} className="button-outline-override">
                            <Edit size={14} className="mr-1" /> Edit
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteBanter(banter.id, banter.title)}>
                            <Trash2 size={14} className="mr-1" /> Delete
                          </Button>
                        </>
                      ) : null}
                       {(user?.role === 'Overall Admin' || user?.role === 'Editor') && (
                        <Button variant="outline" size="sm" onClick={() => handleModerateComments(banter.id)} className="border-yellow-500 text-yellow-600 hover:bg-yellow-500/10">
                            <ShieldCheck size={14} className="mr-1" /> Moderate
                        </Button>
                       )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground text-center py-4">No banters to display.</p>
          )}
        </CardContent>
      </Card>
      <p className="text-xs text-muted-foreground">Feature Guide: Manage all banters. Overall Admins and Editors can edit/delete any banter and moderate comments. Content Creators can manage their own banters.</p>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="sm:max-w-lg bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-2xl">{isEditing ? 'Edit Banter' : 'Create New Banter'}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-3 py-4 max-h-[70vh] overflow-y-auto pr-2 text-sm">
            <Label htmlFor="title">Title</Label><Input id="title" name="title" value={formData.title} onChange={handleChange} className="modern-input"/>
            <Label htmlFor="details">Details</Label><Textarea id="details" name="details" value={formData.details} onChange={handleChange} className="modern-input min-h-[100px]"/>
            <Label htmlFor="tag-leader">Tag Leader</Label><Input id="tag-leader" name="tag-leader" value={formData.tags.leader} onChange={handleChange} className="modern-input"/>
            <Label htmlFor="tag-location">Tag Location</Label><Input id="tag-location" name="tag-location" value={formData.tags.location} onChange={handleChange} className="modern-input"/>
            <Label htmlFor="tag-party">Tag Party</Label><Input id="tag-party" name="tag-party" value={formData.tags.party} onChange={handleChange} className="modern-input"/>
            <Label htmlFor="tag-topic">Tag Topic</Label><Input id="tag-topic" name="tag-topic" value={formData.tags.topic} onChange={handleChange} className="modern-input"/>
            <Label htmlFor="sourceLink">Source Link (Optional)</Label><Input id="sourceLink" name="sourceLink" type="url" value={formData.sourceLink} onChange={handleChange} className="modern-input"/>
          </div>
          <DialogFooter>
            <DialogClose asChild><Button type="button" variant="outline">Cancel</Button></DialogClose>
            <Button type="button" onClick={handleSubmit} className="btn-primary">{isEditing ? 'Save Changes' : 'Create Banter'}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminBanterManagementPage;