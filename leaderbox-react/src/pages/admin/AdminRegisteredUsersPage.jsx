import React, { useState, useMemo } from 'react';
import { Card, CardHeader, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users2 } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from '@/contexts/AuthContext.jsx';
import { motion } from 'framer-motion';
import UserTableFilters from '@/components/admin/registered-users/UserTableFilters.jsx';
import UserTable from '@/components/admin/registered-users/UserTable.jsx';
import EditUserDialog from '@/components/admin/registered-users/EditUserDialog.jsx';

const AdminRegisteredUsersPage = () => {
  const { users: allUsers = [], updateUserStatus, deleteRegisteredUser, updateRegisteredUserDetails } = useAuth();
  const { toast } = useToast();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'ascending' });
  
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [editFormData, setEditFormData] = useState({ name: '', email: '', role: '', status: '' });

  const filteredAndSortedUsers = useMemo(() => {
    let filtered = allUsers.filter(user => {
      const nameMatch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) || false;
      const emailMatch = user.email?.toLowerCase().includes(searchTerm.toLowerCase()) || false;
      const roleMatch = filterRole === 'all' || user.role === filterRole;
      const statusMatch = filterStatus === 'all' || user.status === filterStatus;
      return (nameMatch || emailMatch) && roleMatch && statusMatch;
    });

    if (sortConfig.key) {
      filtered.sort((a, b) => {
        let valA = a[sortConfig.key] || '';
        let valB = b[sortConfig.key] || '';

        if (typeof valA === 'string') valA = valA.toLowerCase();
        if (typeof valB === 'string') valB = valB.toLowerCase();
        
        if (valA < valB) return sortConfig.direction === 'ascending' ? -1 : 1;
        if (valA > valB) return sortConfig.direction === 'ascending' ? 1 : -1;
        return 0;
      });
    }
    return filtered;
  }, [allUsers, searchTerm, filterRole, filterStatus, sortConfig]);

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const handleOpenEditModal = (user) => {
    setEditingUser(user);
    setEditFormData({ name: user.name || '', email: user.email, role: user.role || 'User', status: user.status || 'active' });
    setShowEditModal(true);
  };

  const handleEditFormChange = (e) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleEditFormSelectChange = (name, value) => {
    setEditFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveChanges = () => {
    if (!editingUser) return;
    updateRegisteredUserDetails(editingUser.id, { name: editFormData.name, role: editFormData.role });
    updateUserStatus(editingUser.id, editFormData.status);
    toast({ title: "User Updated", description: `${editFormData.name}'s details have been updated.` });
    setShowEditModal(false);
    setEditingUser(null);
  };

  const handleDelete = (userId, userName) => {
    if (window.confirm(`Are you sure you want to delete user ${userName}? This action cannot be undone.`)) {
      deleteRegisteredUser(userId);
      toast({ title: "User Deleted", description: `User ${userName} has been removed.` });
    }
  };

  const handleFlag = (userId, userName) => {
    updateUserStatus(userId, 'flagged');
    toast({ title: "User Flagged", description: `${userName} has been flagged for review.` });
  };

  const handleBan = (userId, userName) => {
     if (window.confirm(`Are you sure you want to ban user ${userName}? This will restrict their access.`)) {
        updateUserStatus(userId, 'banned');
        toast({ title: "User Banned", description: `${userName} has been banned.` });
     }
  };
  
  const handleUnban = (userId, userName) => {
    updateUserStatus(userId, 'active');
    toast({ title: "User Unbanned", description: `${userName} is now active.` });
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl md:text-3xl font-bold text-foreground flex items-center"><Users2 className="mr-3 text-primary" />Registered Users</h1>
      </div>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-lg md:text-xl text-primary">Filters & Search</CardTitle>
          <UserTableFilters
            searchTerm={searchTerm}
            onSearchTermChange={setSearchTerm}
            filterRole={filterRole}
            onFilterRoleChange={setFilterRole}
            filterStatus={filterStatus}
            onFilterStatusChange={setFilterStatus}
          />
        </CardHeader>
        <CardContent className="overflow-x-auto">
          <UserTable
            users={filteredAndSortedUsers}
            sortConfig={sortConfig}
            requestSort={requestSort}
            onEdit={handleOpenEditModal}
            onFlag={handleFlag}
            onBan={handleBan}
            onUnban={handleUnban}
            onDelete={handleDelete}
          />
        </CardContent>
        <CardFooter className="py-3">
            <p className="text-xs text-muted-foreground">Showing {filteredAndSortedUsers.length} of {allUsers.length} users.</p>
        </CardFooter>
      </Card>

      {editingUser && (
        <EditUserDialog
          isOpen={showEditModal}
          onOpenChange={setShowEditModal}
          editingUser={editingUser}
          editFormData={editFormData}
          onFormChange={handleEditFormChange}
          onSelectChange={handleEditFormSelectChange}
          onSaveChanges={handleSaveChanges}
        />
      )}
    </motion.div>
  );
};

export default AdminRegisteredUsersPage;