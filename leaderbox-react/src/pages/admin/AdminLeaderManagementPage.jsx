import React, { useState, useRef } from 'react';
import { Card, CardHeader, Card<PERSON><PERSON>le, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { PlusCircle, Edit, Trash2, Upload, Youtube as YoutubeIcon } from 'lucide-react';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { useToast } from '@/components/ui/use-toast';

const AdminLeaderManagementPage = () => {
  const { leaders, addLeader, updateLeader, deleteLeader } = useLeaderData(); 
  const { toast } = useToast();
  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentLeader, setCurrentLeader] = useState(null);
  const avatarFileRef = useRef(null);
  const [formData, setFormData] = useState({
    name: '', position: '', party: '', state: '', avatarUrl: '', bio: '', education: '', background: '', detailedBio: '', profileVideoUrl: ''
  });

  const handleOpenCreateModal = () => {
    setIsEditing(false);
    setFormData({ name: '', position: '', party: '', state: '', avatarUrl: '', bio: '', education: '', background: '', detailedBio: '', profileVideoUrl: '' });
    setCurrentLeader(null);
    if(avatarFileRef.current) avatarFileRef.current.value = "";
    setShowModal(true);
  };

  const handleOpenEditModal = (leader) => {
    setIsEditing(true);
    setCurrentLeader(leader);
    setFormData({ ...leader, profileVideoUrl: leader.profileVideoUrl || '' });
    if(avatarFileRef.current) avatarFileRef.current.value = "";
    setShowModal(true);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // In a real app, you'd upload this file and get a URL.
      // For demo, we'll use a placeholder or just log it.
      console.log("Selected avatar file:", file.name);
      setFormData(prev => ({ ...prev, avatarUrl: `https://via.placeholder.com/150?text=${file.name.substring(0,3)}` })); // Placeholder
      toast({ title: "Avatar Selected (Demo)", description: `${file.name} chosen. Actual upload not implemented.`});
    }
  };

  const handleSubmit = () => {
    if (!formData.name || !formData.position || !formData.party || !formData.state) {
        toast({ title: "Error", description: "Name, Position, Party, and State are required.", variant: "destructive" });
        return;
    }
    if (isEditing && currentLeader) {
      // updateLeader(currentLeader.id, formData); // Actual update function
      toast({ title: "Leader Updated (Demo)", description: `Profile for ${formData.name} updated.`});
    } else {
      // addLeader({ ...formData, id: `leader${Date.now()}` }); // Actual add function
      toast({ title: "Leader Created (Demo)", description: `Profile for ${formData.name} created.`});
    }
    setShowModal(false);
  };
  
  const handleDeleteLeader = (leaderId, leaderName) => {
    if(window.confirm(`Are you sure you want to delete ${leaderName}?`)) {
      // deleteLeader(leaderId); // Actual delete function
      toast({ title: "Leader Deleted (Demo)", description: `Profile for ${leaderName} deleted.`});
    }
  };


  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground">Manage Leader Profiles</h1>
        <Button onClick={handleOpenCreateModal} className="btn-primary">
          <PlusCircle size={18} className="mr-2" /> Create New Leader
        </Button>
      </div>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Leader List ({leaders.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {leaders.length > 0 ? (
            <ul className="space-y-3">
              {leaders.map((leader) => (
                <li key={leader.id} className="flex items-center justify-between p-3 bg-secondary/30 rounded-md hover:bg-secondary/50 transition-colors">
                  <div>
                    <p className="font-semibold text-foreground">{leader.name}</p>
                    <p className="text-xs text-muted-foreground">{leader.position} - {leader.party}</p>
                  </div>
                  <div className="space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleOpenEditModal(leader)} className="button-outline-override">
                      <Edit size={14} className="mr-1" /> Edit
                    </Button>
                    <Button variant="destructive" size="sm" onClick={() => handleDeleteLeader(leader.id, leader.name)}>
                      <Trash2 size={14} className="mr-1" /> Delete
                    </Button>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground text-center py-4">No leaders found.</p>
          )}
        </CardContent>
      </Card>
      <p className="text-xs text-muted-foreground">Feature Guide: Admins and Editors can create, edit, and delete leader profiles here. Changes will reflect site-wide.</p>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="sm:max-w-2xl bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-2xl">{isEditing ? 'Edit Leader Profile' : 'Create New Leader'}</DialogTitle>
            <DialogDescription>
              {isEditing ? `Update details for ${currentLeader?.name}.` : 'Enter the details for the new leader.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-[75vh] overflow-y-auto pr-3 text-sm">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div><Label htmlFor="name">Name</Label><Input id="name" name="name" value={formData.name} onChange={handleChange} className="modern-input"/></div>
              <div><Label htmlFor="position">Position</Label><Input id="position" name="position" value={formData.position} onChange={handleChange} className="modern-input"/></div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div><Label htmlFor="party">Party</Label><Input id="party" name="party" value={formData.party} onChange={handleChange} className="modern-input"/></div>
              <div><Label htmlFor="state">State</Label><Input id="state" name="state" value={formData.state} onChange={handleChange} className="modern-input"/></div>
            </div>
            
            <div>
                <Label htmlFor="avatarFile">Avatar Image</Label>
                <div className="flex items-center gap-2 mt-1">
                    <Input id="avatarFile" name="avatarFile" type="file" accept="image/*" ref={avatarFileRef} onChange={handleAvatarChange} className="modern-input flex-grow" />
                    <Button type="button" variant="outline" size="icon" onClick={() => avatarFileRef.current?.click()}><Upload size={16} /></Button>
                </div>
                {formData.avatarUrl && <img-replace src={formData.avatarUrl} alt="Avatar Preview" className="mt-2 h-20 w-20 rounded-full object-cover" />}
                <Input type="hidden" name="avatarUrl" value={formData.avatarUrl} />
            </div>
            
            <div><Label htmlFor="profileVideoUrl" className="flex items-center"><YoutubeIcon className="mr-1.5 h-4 w-4 text-red-500" /> YouTube Profile Video URL (Optional)</Label><Input id="profileVideoUrl" name="profileVideoUrl" value={formData.profileVideoUrl} onChange={handleChange} placeholder="e.g., https://www.youtube.com/watch?v=videoID" className="modern-input"/></div>
            <div><Label htmlFor="bio">Short Bio</Label><Textarea id="bio" name="bio" value={formData.bio} onChange={handleChange} className="modern-input min-h-[80px]"/></div>
            <div><Label htmlFor="detailedBio">Detailed Bio (Supports basic formatting with new lines)</Label><Textarea id="detailedBio" name="detailedBio" value={formData.detailedBio} onChange={handleChange} className="modern-input min-h-[150px]" placeholder="Enter full biography. Use new lines for paragraphs."/>
            <p className="text-xs text-muted-foreground mt-1">A full WYSIWYG editor can be integrated here for richer formatting.</p></div>
            <div><Label htmlFor="education">Education (Optional)</Label><Input id="education" name="education" value={formData.education} onChange={handleChange} className="modern-input"/></div>
            <div><Label htmlFor="background">Background (Optional)</Label><Input id="background" name="background" value={formData.background} onChange={handleChange} className="modern-input"/></div>
          </div>
          <DialogFooter>
            <DialogClose asChild><Button type="button" variant="outline">Cancel</Button></DialogClose>
            <Button type="button" onClick={handleSubmit} className="btn-primary">{isEditing ? 'Save Changes' : 'Create Leader'}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
};

export default AdminLeaderManagementPage;