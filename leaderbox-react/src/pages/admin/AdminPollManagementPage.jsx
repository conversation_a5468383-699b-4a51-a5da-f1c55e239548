import React, { useState, useMemo } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { PlusCircle, Edit, Trash2, ShieldCheck, X, Users, MapPin, Tag as TagIcon } from 'lucide-react';
import { usePollData } from '@/contexts/PollContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { Link } from 'react-router-dom';
import { Combobox } from '@/components/ui/combobox';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';


const AdminPollManagementPage = () => {
  const { polls, createPoll, deletePoll: contextDeletePoll } = usePollData(); // Removed updatePoll as it's not fully implemented
  const { leaders } = useLeaderData();
  const { user } = useAuth();
  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentPoll, setCurrentPoll] = useState(null);
  const [formData, setFormData] = useState({ topic: '', description: '', options: ['', ''], taggedLeader: '', taggedLocation: '', generalTags: [] });

  const leaderOptions = useMemo(() => leaders.map(l => ({ value: l.name.toLowerCase(), label: l.name })), [leaders]);
  const stateOptions = useMemo(() => {
    const states = new Set(leaders.map(l => l.state).filter(Boolean));
    return Array.from(states).map(s => ({ value: s.toLowerCase(), label: s }));
  }, [leaders]);

  const canEditDeleteAll = user?.role === 'Overall Admin' || user?.role === 'Editor';
  const pollsToDisplay = canEditDeleteAll ? polls : polls.filter(p => p.authorId === user?.id);

  const handleOpenCreateModal = () => {
    setIsEditing(false);
    setFormData({ topic: '', description: '', options: ['', ''], taggedLeader: '', taggedLocation: '', generalTags: [] });
    setCurrentPoll(null);
    setShowModal(true);
  };

  const handleOpenEditModal = (poll) => {
    setIsEditing(true);
    setCurrentPoll(poll);
    setFormData({
        topic: poll.topic,
        description: poll.description || '',
        options: poll.options.map(opt => opt.text), 
        taggedLeader: poll.taggedLeader || '',
        taggedLocation: poll.taggedLocation || '',
        generalTags: poll.generalTags || []
    });
    setShowModal(true);
  };
  
  const handleChange = (e) => {
    const { name, value } = e.target;
     if (name === "generalTags") {
        setFormData(prev => ({...prev, generalTags: value.split(',').map(t => t.trim()).filter(t => t)}));
    } else {
        setFormData(prev => ({ ...prev, [name]: value }));
    }
  };
  
  const handleComboboxChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };


  const handleOptionChange = (index, value) => {
    const newOptions = [...formData.options];
    newOptions[index] = value;
    setFormData(prev => ({ ...prev, options: newOptions }));
  };

  const handleAddOption = () => {
    if (formData.options.length < 5) {
      setFormData(prev => ({ ...prev, options: [...prev.options, ''] }));
    }
  };
  
  const handleRemoveOption = (index) => {
    if (formData.options.length > 2) {
      const newOptions = formData.options.filter((_, i) => i !== index);
      setFormData(prev => ({...prev, options: newOptions}));
    }
  };

  const handleSubmit = () => {
    if (!formData.topic || formData.options.some(opt => !opt.trim()) || formData.options.length < 2) {
        alert("Topic and at least two non-empty options are required.");
        return;
    }
    if (isEditing && currentPoll) {
      // updatePoll(currentPoll.id, formData); // updatePoll function needed in context
      alert(`Updated poll: ${formData.topic}. (This is a demo, data persistence not fully implemented for admin edits yet)`);
    } else {
      createPoll(formData);
      alert(`Created poll: ${formData.topic}.`);
    }
    setShowModal(false);
  };


  const handleDeletePoll = (pollId, pollTopic) => {
    if(window.confirm(`Are you sure you want to delete the poll "${pollTopic}"?`)){
        contextDeletePoll(pollId); 
        alert(`Poll "${pollTopic}" deleted by admin.`);
    }
  };
  const handleModerateComments = (pollId) => alert(`Moderate comments for poll ${pollId}`);


  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground">Manage Polls</h1>
         { (user?.role === 'Overall Admin' || user?.role === 'Editor' || user?.role === 'Content Creator') &&
            <Button onClick={handleOpenCreateModal} className="btn-primary">
            <PlusCircle size={18} className="mr-2" /> Create New Poll
            </Button>
        }
      </div>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Poll List ({pollsToDisplay.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {pollsToDisplay.length > 0 ? (
            <ul className="space-y-3">
              {pollsToDisplay.map((poll) => (
                <li key={poll.id} className="p-3 bg-secondary/30 rounded-md hover:bg-secondary/50 transition-colors">
                    <div className="flex items-center justify-between">
                        <div>
                            <Link to={`/poll/${poll.id}`} className="font-semibold text-foreground hover:underline">{poll.topic}</Link>
                            <p className="text-xs text-muted-foreground">By: {poll.source} | Votes: {poll.totalVotes || 0} | Comments: {poll.commentsCount || 0}</p>
                             <div className="mt-1 flex flex-wrap gap-1">
                                {poll.taggedLeader && <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full">{poll.taggedLeader}</span>}
                                {poll.taggedLocation && <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">{poll.taggedLocation}</span>}
                                {(poll.generalTags || []).map(tag => <span key={tag} className="text-xs bg-gray-100 text-gray-700 px-1.5 py-0.5 rounded-full">{tag}</span>)}
                            </div>
                        </div>
                        <div className="space-x-2">
                            {canEditDeleteAll || poll.authorId === user?.id ? ( 
                            <>
                                <Button variant="outline" size="sm" onClick={() => handleOpenEditModal(poll)} className="button-outline-override">
                                <Edit size={14} className="mr-1" /> Edit
                                </Button>
                                <Button variant="destructive" size="sm" onClick={() => handleDeletePoll(poll.id, poll.topic)}>
                                <Trash2 size={14} className="mr-1" /> Delete
                                </Button>
                            </>
                            ) : null}
                            {(user?.role === 'Overall Admin' || user?.role === 'Editor') && (
                                <Button variant="outline" size="sm" onClick={() => handleModerateComments(poll.id)} className="border-yellow-500 text-yellow-600 hover:bg-yellow-500/10">
                                    <ShieldCheck size={14} className="mr-1" /> Moderate
                                </Button>
                            )}
                        </div>
                    </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground text-center py-4">No polls to display.</p>
          )}
        </CardContent>
      </Card>
      <p className="text-xs text-muted-foreground">Feature Guide: Manage all polls. Overall Admins and Editors can edit/delete any poll and moderate comments. Content Creators can manage their own polls.</p>
    
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="sm:max-w-lg bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-2xl">{isEditing ? 'Edit Poll' : 'Create New Poll'}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-3 py-4 max-h-[70vh] overflow-y-auto pr-2 text-sm">
            <Label htmlFor="topic">Topic</Label><Input id="topic" name="topic" value={formData.topic} onChange={handleChange} className="modern-input"/>
            <Label htmlFor="description">Description (Optional)</Label><Textarea id="description" name="description" value={formData.description} onChange={handleChange} className="modern-input min-h-[80px]"/>
            <Label>Options (Min 2, Max 5)</Label>
            {formData.options.map((opt, index) => (
              <div key={index} className="flex items-center gap-2">
                <Input value={opt} onChange={(e) => handleOptionChange(index, e.target.value)} placeholder={`Option ${index + 1}`} className="modern-input"/>
                {formData.options.length > 2 && <Button variant="ghost" size="icon" onClick={() => handleRemoveOption(index)} className="text-destructive h-8 w-8"><X size={16}/></Button>}
              </div>
            ))}
            {formData.options.length < 5 && <Button variant="outline" size="sm" onClick={handleAddOption} className="btn-outline-primary mt-1">Add Option</Button>}
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                    <Label htmlFor="taggedLeader" className="flex items-center mb-1"><Users size={14} className="mr-1.5 text-muted-foreground"/>Tag Leader</Label>
                    <Combobox options={leaderOptions} value={formData.taggedLeader} onChange={(val) => handleComboboxChange('taggedLeader', val)} placeholder="Select Leader" inputClassName="modern-input"/>
                </div>
                <div>
                    <Label htmlFor="taggedLocation" className="flex items-center mb-1"><MapPin size={14} className="mr-1.5 text-muted-foreground"/>Tag Location</Label>
                    <Combobox options={stateOptions} value={formData.taggedLocation} onChange={(val) => handleComboboxChange('taggedLocation', val)} placeholder="Select Location" inputClassName="modern-input"/>
                </div>
            </div>
             <Label htmlFor="generalTags" className="flex items-center mb-1"><TagIcon size={14} className="mr-1.5 text-muted-foreground"/>General Tags (comma-separated)</Label>
             <Input id="generalTags" name="generalTags" value={(formData.generalTags || []).join(', ')} onChange={handleChange} className="modern-input" placeholder="e.g. economy, youth"/>
          </div>
          <DialogFooter>
            <DialogClose asChild><Button type="button" variant="outline">Cancel</Button></DialogClose>
            <Button type="button" onClick={handleSubmit} className="btn-primary">{isEditing ? 'Save Changes' : 'Create Poll'}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPollManagementPage;