import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'; // Added CardDescription
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PlusCircle, Trash2, ShieldAlert } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";

const AdminKeywordManagementPage = () => {
  const [keywords, setKeywords] = useState(['badword1', 'offensivephrase2', 'spamkeyword3']);
  const [newKeyword, setNewKeyword] = useState('');
  const { toast } = useToast();

  const handleAddKeyword = () => {
    if (newKeyword.trim() && !keywords.includes(newKeyword.trim().toLowerCase())) {
      setKeywords([...keywords, newKeyword.trim().toLowerCase()]);
      setNewKeyword('');
      toast({ title: "Keyword Added", description: `"${newKeyword.trim()}" added to blacklist.` });
    } else if (keywords.includes(newKeyword.trim().toLowerCase())) {
      toast({ title: "Duplicate Keyword", description: "This keyword is already in the list.", variant: "destructive" });
    } else {
      toast({ title: "Invalid Keyword", description: "Keyword cannot be empty.", variant: "destructive" });
    }
  };

  const handleDeleteKeyword = (keywordToDelete) => {
    setKeywords(keywords.filter(kw => kw !== keywordToDelete));
    toast({ title: "Keyword Removed", description: `"${keywordToDelete}" removed from blacklist.` });
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground">Manage Blacklisted Keywords</h1>
      </div>
       <Card className="modern-card bg-yellow-50 border-yellow-300">
        <CardHeader className="flex flex-row items-center space-x-3">
          <ShieldAlert className="h-6 w-6 text-yellow-600" />
          <div>
            <CardTitle className="text-yellow-700">System Note</CardTitle>
            <CardDescription className="text-yellow-600 text-xs">
              Keywords added here will be used to automatically flag or hide content across the platform. Use with caution. This is a simplified frontend demonstration; robust implementation requires backend processing.
            </CardDescription>
          </div>
        </CardHeader>
      </Card>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Add New Blacklisted Keyword/Phrase</CardTitle>
        </CardHeader>
        <CardContent className="flex items-end space-x-3">
          <div className="flex-grow">
            <Label htmlFor="new-keyword">Keyword or Phrase</Label>
            <Input 
              id="new-keyword" 
              value={newKeyword} 
              onChange={(e) => setNewKeyword(e.target.value)} 
              placeholder="e.g., offensive_term"
              className="modern-input mt-1"
            />
          </div>
          <Button onClick={handleAddKeyword} className="btn-primary">
            <PlusCircle size={18} className="mr-2" /> Add to Blacklist
          </Button>
        </CardContent>
      </Card>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-primary">Current Blacklisted Keywords ({keywords.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {keywords.length > 0 ? (
            <ul className="space-y-2">
              {keywords.map((keyword) => (
                <li key={keyword} className="flex items-center justify-between p-3 bg-secondary/30 rounded-md hover:bg-secondary/50 transition-colors">
                  <span className="text-foreground font-mono text-sm">{keyword}</span>
                  <Button variant="destructive" size="sm" onClick={() => handleDeleteKeyword(keyword)}>
                    <Trash2 size={14} className="mr-1" /> Remove
                  </Button>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground text-center py-4">No keywords currently blacklisted.</p>
          )}
        </CardContent>
      </Card>
       <p className="text-xs text-muted-foreground">Feature Guide: Overall Admins and Editors can add or remove keywords/phrases. Content containing these terms may be automatically flagged or hidden, depending on system configuration.</p>
    </div>
  );
};

export default AdminKeywordManagementPage;