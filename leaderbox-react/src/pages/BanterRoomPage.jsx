import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { MessageSquare, Search, PlusCircle, TrendingUp, Sparkles, UserCircle, Edit3, Trash2 } from 'lucide-react';
import { useBanter } from '@/contexts/BanterContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useToast } from "@/components/ui/use-toast";
import BanterCard from '@/components/banter/BanterCard.jsx';
import StartBanterDialog from '@/components/banter/StartBanterDialog.jsx';

const BANTERS_PER_PAGE = 20;

const BanterRoomPage = () => {
  const { banters, addBanter, deleteBanter } = useBanter();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('hot');
  const [showStartBanterDialog, setShowStartBanterDialog] = useState(false);
  const [visibleBantersCount, setVisibleBantersCount] = useState(BANTERS_PER_PAGE);

  const handleStartBanter = (banterData) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to start a banter.", variant: "destructive" });
      navigate('/login');
      return;
    }
    const newBanter = addBanter(banterData);
    if (newBanter) {
      toast({ title: "Banter Started!", description: "Your banter is now live." });
      navigate(`/banter/${newBanter.id}`);
    } else {
      toast({ title: "Error", description: "Could not start banter.", variant: "destructive" });
    }
  };

  const handleDeleteBanter = (banterId) => {
    deleteBanter(banterId);
    toast({ title: "Banter Deleted", description: "The banter has been removed." });
  };

  const filteredBanters = useMemo(() => {
    let filtered = banters || [];
    if (searchTerm) {
      filtered = filtered.filter(banter =>
        banter.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        banter.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
        banter.authorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (banter.tags?.topic && banter.tags.topic.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (activeTab === 'hot') {
      return filtered.sort((a, b) => (b.upvotes + (b.comments?.length || 0)) - (a.upvotes + (a.comments?.length || 0)));
    } else if (activeTab === 'new') {
      return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    } else if (activeTab === 'my-banters' && user) {
      return filtered.filter(banter => banter.authorId === user.id || (banter.comments || []).some(c => c.userId === user.id));
    }
    return filtered;
  }, [banters, searchTerm, activeTab, user]);

  const bantersToShow = filteredBanters.slice(0, visibleBantersCount);

  const onboardingMessages = [
    "Welcome to the Banter Room – Nigeria’s political talkspace. Start a banter. Drop your take. Keep it respectful.",
    "Use this space to debate, discuss, and laugh through politics – without hate."
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-5xl mx-auto space-y-6"
    >
      <header className="text-center py-6 space-y-2">
        <h1 className="text-4xl font-extrabold gradient-text from-primary to-accent">Banter Room</h1>
        {onboardingMessages.map((msg, idx) => (
          <p key={idx} className="text-muted-foreground text-sm md:text-base">{msg}</p>
        ))}
      </header>

      <div className="flex flex-col sm:flex-row gap-4 items-center sticky top-[70px] md:top-[80px] z-30 bg-background/80 backdrop-blur-sm py-3 px-2 -mx-2 rounded-b-lg shadow-sm">
        <div className="relative flex-grow w-full sm:w-auto">
          <Input
            type="search"
            placeholder="Search banters by title, topic, or author..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="modern-input pl-4 pr-10 text-base h-11"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        </div>
        <Button onClick={() => user ? setShowStartBanterDialog(true) : navigate('/login')} className="btn-primary rounded-full px-6 py-2.5 text-base w-full sm:w-auto h-11">
          <PlusCircle size={20} className="mr-2" /> Start a Banter
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 md:w-auto md:inline-flex bg-secondary/50 rounded-lg p-1 mb-6">
          <TabsTrigger value="hot" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
            <TrendingUp size={18} /> Hot
          </TabsTrigger>
          <TabsTrigger value="new" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
            <Sparkles size={18} /> New
          </TabsTrigger>
          <TabsTrigger value="my-banters" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
            <UserCircle size={18} /> My Banters
          </TabsTrigger>
        </TabsList>

        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {bantersToShow.length > 0 ? (
              <div className="space-y-4">
                {bantersToShow.map((banter) => (
                  <BanterCard 
                    key={banter.id} 
                    banter={banter} 
                    currentUser={user}
                    onDelete={() => handleDeleteBanter(banter.id)}
                    onEdit={() => navigate(`/banter/${banter.id}/edit`)} 
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-10 modern-card">
                <MessageSquare size={48} className="mx-auto text-muted-foreground/50 mb-4" />
                <p className="text-xl text-muted-foreground">
                  {activeTab === 'my-banters' && !user ? "Log in to see your banters." : 
                   activeTab === 'my-banters' && user ? "You haven't started or participated in any banters yet." :
                   searchTerm ? "No banters found matching your search." :
                   "No banters yet. Be the first to stir the pot."}
                </p>
                {(!searchTerm && activeTab !== 'my-banters') && <p className="text-sm text-muted-foreground">Why not start one?</p>}
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </Tabs>
      
      {filteredBanters.length > visibleBantersCount && (
        <div className="text-center mt-8">
          <Button onClick={() => setVisibleBantersCount(prev => prev + BANTERS_PER_PAGE)} className="btn-outline-primary">
            Load More Banters
          </Button>
        </div>
      )}

      <StartBanterDialog
        isOpen={showStartBanterDialog}
        onClose={() => setShowStartBanterDialog(false)}
        onSubmit={handleStartBanter}
      />
    </motion.div>
  );
};

export default BanterRoomPage;