
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { usePollData } from '@/contexts/PollContext.jsx'; 
import { useAuth } from '@/contexts/AuthContext.jsx'; 
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';
import { MessageSquare, ThumbsUp, ThumbsDown, Share2, ArrowLeft, CheckSquare, User, CalendarDays, Tag as TagIcon, Info, MessageCircle, AtSign } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import CommentAfterVoteDialog from '@/components/polls/CommentAfterVoteDialog.jsx';
import MediaUpload from '@/components/ui/MediaUpload.jsx';
import MediaDisplayItem from '@/components/shared/MediaDisplayItem.jsx';
import { cn } from '@/lib/utils';

const SinglePollPage = () => {
  const { pollId } = useParams();
  const navigate = useNavigate();
  const { polls, voteOnPoll, addCommentToPoll, addReplyToPollComment, voteOnPollItem, userPollItemVotes } = usePollData();
  const { user } = useAuth();
  const { toast } = useToast();

  const [poll, setPoll] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null);
  const [showResults, setShowResults] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [attachedMediaFiles, setAttachedMediaFiles] = useState([]);
  const [replyingTo, setReplyingTo] = useState(null); 
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [votedOptionId, setVotedOptionId] = useState(null);
  const commentInputRef = React.useRef(null);

  useEffect(() => {
    const currentPoll = polls.find(p => p.id === pollId);
    if (currentPoll) {
      setPoll(currentPoll);
      if (user && currentPoll.userVotes && currentPoll.userVotes[user.id]) {
        setShowResults(true);
        setSelectedOption(currentPoll.userVotes[user.id]);
      } else {
        setShowResults(false);
        setSelectedOption(null);
      }
    } else {
      toast({ title: "Error", description: "Poll not found.", variant: "destructive" });
      navigate("/polls");
    }
  }, [pollId, polls, user, toast, navigate]);

  const handleVote = (optionId) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to vote.", variant: "destructive" });
      navigate('/login');
      return;
    }
    if (poll.userVotes && poll.userVotes[user.id]) {
      toast({ title: "Already Voted", description: "You have already voted on this poll.", variant: "info" });
      return;
    }
    setSelectedOption(optionId);
    setVotedOptionId(optionId); 
    setShowCommentDialog(true);
  };

  const handleCommentAfterVote = (commentTextAfterVote) => {
    if (votedOptionId) {
      voteOnPoll(pollId, votedOptionId, commentTextAfterVote);
      toast({ title: "Vote Submitted!", description: "Your vote has been counted." });
      setShowResults(true);
    }
    setShowCommentDialog(false);
    setVotedOptionId(null); 
  };

  const handleSkipComment = () => {
    if (votedOptionId) {
      voteOnPoll(pollId, votedOptionId, ''); 
      toast({ title: "Vote Submitted!", description: "Your vote has been counted." });
      setShowResults(true);
    }
    setShowCommentDialog(false);
    setVotedOptionId(null);
  };

  const handlePostCommentOrReply = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to comment.", variant: "destructive" });
      navigate('/login');
      return;
    }
    if (!commentText.trim() && attachedMediaFiles.length === 0) {
      toast({ title: "Empty Content", description: "Cannot post an empty comment or reply without text or media.", variant: "destructive" });
      return;
    }
    
    const mediaDataForStorage = attachedMediaFiles.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
      url: URL.createObjectURL(file) 
    }));

    if (replyingTo) {
      addReplyToPollComment(pollId, replyingTo.commentId, commentText, mediaDataForStorage);
      toast({ title: "Reply Posted!", description: "Your reply has been added." });
    } else {
      addCommentToPoll(pollId, commentText, mediaDataForStorage);
      toast({ title: "Comment Posted!", description: "Your comment has been added." });
    }
    setCommentText('');
    setAttachedMediaFiles([]);
    setReplyingTo(null);
  };

  const handleReplyClick = (commentId, userName) => {
    setReplyingTo({ commentId, userName });
    setCommentText(`@${userName} `);
    setAttachedMediaFiles([]); 
    commentInputRef.current?.focus();
  };

  const handleMediaChange = (files) => {
    setAttachedMediaFiles(files);
  };

  const handlePollItemVote = (itemId, voteType, itemType) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to vote.", variant: "destructive" });
      return;
    }
    voteOnPollItem(pollId, itemId, voteType, itemType);
  };

  if (!poll) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-background">
        <div className="text-center">
          <Info size={48} className="mx-auto text-primary mb-4" />
          <p className="text-xl text-muted-foreground">Loading poll details...</p>
        </div>
      </div>
    );
  }

  const totalVotes = poll.options.reduce((sum, option) => sum + (option.votes || 0), 0);
  const pollCreator = poll.creator;
  const pollVoteStatus = userPollItemVotes[`poll-${poll.id}-${user?.id}`];

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }} 
      animate={{ opacity: 1, y: 0 }} 
      transition={{ duration: 0.5 }}
      className="container mx-auto py-6 sm:py-10 px-2 sm:px-4 max-w-3xl"
    >
      <Button variant="outline" onClick={() => navigate(-1)} className="mb-6 btn-outline-primary">
        <ArrowLeft size={18} className="mr-2" /> Back to Polls
      </Button>

      <Card className="modern-card overflow-hidden">
        <CardHeader className="modern-card-header">
          <div className="flex justify-between items-start">
            <CardTitle className="text-2xl sm:text-3xl text-primary flex-1">{poll.topic}</CardTitle>
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="sm" onClick={() => handlePollItemVote(poll.id, 'upvote', 'poll')} className={cn('text-xs hover:text-green-500', pollVoteStatus === 'upvote' ? 'text-green-500 bg-green-500/10' : 'text-muted-foreground')}>
                <ThumbsUp size={14} className="mr-1.5" /> {poll.upvotes || 0}
              </Button>
              <Button variant="ghost" size="sm" onClick={() => handlePollItemVote(poll.id, 'downvote', 'poll')} className={cn('text-xs hover:text-red-500', pollVoteStatus === 'downvote' ? 'text-red-500 bg-red-500/10' : 'text-muted-foreground')}>
                <ThumbsDown size={14} className="mr-1.5" /> {poll.downvotes || 0}
              </Button>
            </div>
          </div>
          <div className="flex items-center text-xs sm:text-sm text-muted-foreground mt-2 space-x-3 flex-wrap">
            {poll.source && (
              <span className="flex items-center">
                <User size={14} className="mr-1.5"/>By: {poll.source}
              </span>
            )}
            <span className="flex items-center"><CalendarDays size={14} className="mr-1.5"/>Created: {new Date(poll.createdAt).toLocaleDateString()}</span>
            <span className="flex items-center"><CheckSquare size={14} className="mr-1.5"/>{totalVotes} Votes</span>
          </div>
          {poll.description && <p className="text-sm text-foreground/80 mt-3 whitespace-pre-wrap">{poll.description}</p>}
          {poll.media && poll.media.length > 0 && (
            <div className="mt-3 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
              {poll.media.map((item, index) => (
                <MediaDisplayItem key={index} mediaItem={item} />
              ))}
            </div>
          )}
          {(poll.taggedLeader || poll.taggedLocation) && (
            <div className="mt-3 flex flex-wrap gap-2 text-xs">
              {poll.taggedLeader && (
                <span className="flex items-center bg-primary/10 text-primary px-2 py-1 rounded-full">
                  <AtSign size={12} className="mr-1" /> {poll.taggedLeader}
                </span>
              )}
              {poll.taggedLocation && (
                <span className="flex items-center bg-accent/10 text-accent-foreground px-2 py-1 rounded-full">
                  <TagIcon size={12} className="mr-1" /> {poll.taggedLocation}
                </span>
              )}
            </div>
          )}
        </CardHeader>

        <CardContent className="py-6 px-4 sm:px-6 space-y-3">
          {poll.options.map(option => (
            <motion.div
              key={option.id}
              whileHover={{ scale: showResults ? 1 : 1.02 }}
              className="relative"
            >
              <Button
                variant={selectedOption === option.id ? "default" : "outline"}
                className={`w-full justify-start text-left h-auto py-3 px-4 rounded-lg transition-all duration-200 ease-in-out modern-input focus:ring-primary ${
                  showResults ? 'cursor-default' : 'hover:bg-primary/10'
                } ${selectedOption === option.id && !showResults ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : ''}`}
                onClick={() => !showResults && handleVote(option.id)}
                disabled={showResults}
              >
                <span className="flex-grow text-sm sm:text-base">{option.text}</span>
                {showResults && (
                  <span className="ml-auto text-xs sm:text-sm font-semibold text-primary">
                    {((option.votes / totalVotes) * 100 || 0).toFixed(1)}% ({option.votes} votes)
                  </span>
                )}
              </Button>
              {showResults && (
                <Progress 
                  value={(option.votes / totalVotes) * 100 || 0} 
                  className="absolute bottom-0 left-0 right-0 h-1 rounded-b-lg opacity-70"
                  indicatorClassName={selectedOption === option.id ? "bg-primary" : "bg-primary/50"}
                />
              )}
            </motion.div>
          ))}
        </CardContent>

        <CardFooter className="modern-card-footer flex-col items-start sm:flex-row sm:items-center sm:justify-between gap-3">
          {!showResults && user && !(poll.userVotes && poll.userVotes[user.id]) && (
            <p className="text-xs text-muted-foreground">Select an option to cast your vote.</p>
          )}
          {showResults && (
            <p className="text-xs text-muted-foreground">
              {user && poll.userVotes && poll.userVotes[user.id] ? "You have voted." : "Results are shown."}
            </p>
          )}
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-primary text-xs" onClick={() => toast({ title: "🚧 Feature Not Implemented", description: "Sharing polls isn't available yet, but you can request it! 🚀" })}>
            <Share2 size={14} className="mr-1.5" /> Share Poll
          </Button>
        </CardFooter>
      </Card>

      <section className="mt-8 sm:mt-12">
        <h3 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6 text-foreground flex items-center">
          <MessageSquare size={22} className="mr-2.5 text-primary" /> Comments ({poll.comments?.length || 0})
        </h3>
        {user && (
          <Card className="mb-6 modern-card">
            <CardHeader className="pb-2">
              <p className="text-sm font-medium text-primary">
                {replyingTo ? `Replying to ${replyingTo.userName}` : "Leave a comment"}
              </p>
            </CardHeader>
            <CardContent className="space-y-3">
              <Textarea 
                ref={commentInputRef}
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                placeholder={replyingTo ? `Type your reply to @${replyingTo.userName}...` : "Share your thoughts on this poll..."}
                className="modern-input min-h-[80px]"
              />
              <MediaUpload onFilesChange={handleMediaChange} existingFiles={attachedMediaFiles} />
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              {replyingTo && (
                <Button variant="ghost" size="sm" onClick={() => { setReplyingTo(null); setCommentText(''); setAttachedMediaFiles([]); }}>Cancel Reply</Button>
              )}
              <Button onClick={handlePostCommentOrReply} className="btn-primary">
                Post {replyingTo ? "Reply" : "Comment"}
              </Button>
            </CardFooter>
          </Card>
        )}

        {poll.comments && poll.comments.length > 0 ? (
          <div className="space-y-4">
            {poll.comments.sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp)).map(comment => {
              const commentVoteStatus = userPollItemVotes[`comment-${comment.commentId}-${user?.id}`];
              return (
              <Card key={comment.commentId} className="bg-background/50 border-border shadow-sm">
                <CardHeader className="p-3 pb-1 flex flex-row items-start space-x-3">
                  <Link to={`/user/${comment.userId}`}>
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={comment.userAvatarUrl || `https://avatar.vercel.sh/${comment.userName}.png?size=36`} alt={comment.userName} />
                      <AvatarFallback>{comment.userName ? comment.userName.substring(0,1).toUpperCase() : 'U'}</AvatarFallback>
                    </Avatar>
                  </Link>
                  <div>
                    <Link to={`/user/${comment.userId}`} className="font-semibold text-sm text-primary hover:underline">
                      {comment.userName}
                    </Link>
                    <p className="text-xs text-muted-foreground">{new Date(comment.timestamp).toLocaleString()}</p>
                  </div>
                </CardHeader>
                <CardContent className="p-3 pt-1">
                  <p className="text-sm text-foreground/90 whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: comment.text.replace(/@(\w+)/g, '<span class="text-accent font-semibold">@$1</span>') }}></p>
                  {comment.media && comment.media.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {comment.media.map((item, index) => (
                        <MediaDisplayItem key={index} mediaItem={item} />
                      ))}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="p-3 pt-1 flex justify-between items-center border-t border-border/50">
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" onClick={() => handlePollItemVote(comment.commentId, 'upvote', 'comment')} className={cn("text-xs hover:text-green-500 px-2", commentVoteStatus === 'upvote' ? 'text-green-500 bg-green-500/10' : 'text-muted-foreground')}>
                      <ThumbsUp size={14} className="mr-1" /> ({comment.upvotes || 0})
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handlePollItemVote(comment.commentId, 'downvote', 'comment')} className={cn("text-xs hover:text-red-500 px-2", commentVoteStatus === 'downvote' ? 'text-red-500 bg-red-500/10' : 'text-muted-foreground')}>
                      <ThumbsDown size={14} className="mr-1" /> ({comment.downvotes || 0})
                    </Button>
                    {user && (
                      <Button variant="ghost" size="sm" onClick={() => handleReplyClick(comment.commentId, comment.userName)} className="text-xs text-muted-foreground hover:text-primary px-2">
                        <MessageCircle size={14} className="mr-1" /> Reply
                      </Button>
                    )}
                  </div>
                </CardFooter>
                {comment.replies && comment.replies.length > 0 && (
                  <div className="pl-8 pr-3 pb-3 space-y-2">
                    {comment.replies.sort((a,b) => new Date(a.timestamp) - new Date(b.timestamp)).map(reply => (
                      <div key={reply.replyId} className="p-2 bg-secondary/30 rounded-md border-l-2 border-primary/50">
                        <div className="flex items-center space-x-2 mb-1">
                          <Link to={`/user/${reply.userId}`}>
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={reply.userAvatarUrl || `https://avatar.vercel.sh/${reply.userName}.png?size=24`} alt={reply.userName} />
                              <AvatarFallback className="text-xs">{reply.userName ? reply.userName.substring(0,1).toUpperCase() : 'U'}</AvatarFallback>
                            </Avatar>
                          </Link>
                          <Link to={`/user/${reply.userId}`} className="font-semibold text-xs text-primary hover:underline">
                            {reply.userName}
                          </Link>
                          <p className="text-xs text-muted-foreground">{new Date(reply.timestamp).toLocaleDateString()}</p>
                        </div>
                        <p className="text-xs text-foreground/80 whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: reply.text.replace(/@(\w+)/g, '<span class="text-accent font-semibold">@$1</span>') }}></p>
                        {reply.media && reply.media.length > 0 && (
                          <div className="mt-1.5 flex flex-wrap gap-1.5">
                            {reply.media.map((item, index) => (
                              <MediaDisplayItem key={index} mediaItem={item} />
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </Card>
            )})}
          </div>
        ) : (
          <p className="text-muted-foreground text-center py-6">No comments yet. Be the first to share your thoughts!</p>
        )}
      </section>

      <CommentAfterVoteDialog
        isOpen={showCommentDialog}
        onClose={() => { setShowCommentDialog(false); setVotedOptionId(null); }}
        pollTopic={poll?.topic}
        onSubmitComment={handleCommentAfterVote}
      />
    </motion.div>
  );
};

export default SinglePollPage;
