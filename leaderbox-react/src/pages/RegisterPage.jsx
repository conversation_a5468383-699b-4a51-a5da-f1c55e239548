
import React, { useState } from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from "@/components/ui/use-toast";
import { motion } from 'framer-motion';
import { ChevronLeft, Eye, EyeOff, Camera } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext.jsx';

const nigerianStates = [
  "Abia", "Adamawa", "Akwa Ibom", "Anambra", "Bauchi", "Bayelsa", "Benue", 
  "Borno", "Cross River", "Delta", "Ebonyi", "Edo", "<PERSON><PERSON><PERSON>", "Enug<PERSON>", 
  "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kaduna", "Kano", "Katsina", "Ke<PERSON><PERSON>", 
  "Kogi", "Kwara", "Lagos", "Nasarawa", "Niger", "Ogun", "Ondo", 
  "Osun", "Oyo", "Plateau", "Rivers", "Sokoto", "Taraba", "Yobe", "Zamfara", 
  "Federal Capital Territory"
];

const politicalParties = ["APC", "PDP", "LP", "NNPP", "Neutral"];

const RegisterPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { register, completeOnboarding } = useAuth();
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: '',
    state: '',
    lga: '', 
    gender: '',
    party: '',
    avatarFile: null,
    avatarPreview: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const inputClasses = "bg-gray-200 text-black placeholder:text-gray-600 focus:bg-white";

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleAvatarChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({
        ...prev,
        avatarFile: file,
        avatarPreview: URL.createObjectURL(file)
      }));
    }
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleNextStep = async () => {
    if (step === 1) { // Email & Password
      if (!formData.email || !formData.password || !formData.confirmPassword) {
        toast({ title: "Hold Up!", description: "Gotta fill in your email and password deets!", variant: "destructive" });
        return;
      }
      if (formData.password !== formData.confirmPassword) {
        toast({ title: "Oops!", description: "Your passwords aren't matching. Try again!", variant: "destructive" });
        return;
      }
      if (!/\S+@\S+\.\S+/.test(formData.email)) {
        toast({ title: "Hmm...", description: "That email looks a bit off. Check it?", variant: "destructive" });
        return;
      }
    }
    if (step === 2) { // Full Name & Phone
        if (!formData.fullName.trim()) {
            toast({ title: "Just a sec!", description: "We need your full name to know who you are!", variant: "destructive" });
            return;
        }
    }
    setStep(prev => prev + 1);
  };

  const handlePrevStep = () => {
    setStep(prev => prev - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (step === 3 && (!formData.state || !formData.lga.trim())) {
      toast({ title: "Almost there!", description: "Where you dey represent? (State & LGA)", variant: "destructive" });
      return;
    }
    if (step === 4 && (!formData.gender || !formData.party)) {
       toast({ title: "Last few things!", description: "Please select your gender and party affiliation.", variant: "destructive" });
       return;
    }

    try {
      const registeredUser = await register({ email: formData.email, password: formData.password });
      if (registeredUser) {
        completeOnboarding({
          name: formData.fullName,
          phone: formData.phone,
          state: formData.state,
          lga: formData.lga,
          gender: formData.gender,
          party: formData.party,
          avatarUrl: formData.avatarPreview || '', 
        });
        toast({ title: "You're In!", description: "Welcome to the LeaderBox fam! Get ready to connect.", variant: "success", duration: 5000 });
        if(formData.avatarPreview){
          navigate('/dashboard');
        } else {
          navigate('/welcome/follow-suggestions');
        }
      }
    } catch (error) {
      toast({ title: "Registration Error", description: error.message || "Something went wrong. Please try again.", variant: "destructive" });
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1: // Email & Password
        return (
          <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5 }} className="space-y-6">
            <p className="text-center text-gray-300 text-sm">First up, let's get your login sorted. Secure and simple!</p>
            <div>
              <Label htmlFor="email">Your Email Address</Label>
              <Input id="email" name="email" type="email" placeholder="<EMAIL>" value={formData.email} onChange={handleChange} className={`mt-1 ${inputClasses}`} />
            </div>
            <div className="relative">
              <Label htmlFor="password">Choose a Password</Label>
              <Input id="password" name="password" type={showPassword ? "text" : "password"} placeholder="••••••••" value={formData.password} onChange={handleChange} className={`mt-1 ${inputClasses}`} />
              <Button variant="ghost" size="icon" onClick={() => setShowPassword(!showPassword)} className="absolute bottom-1 right-1 h-7 w-7 text-gray-500 hover:text-black">
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </Button>
            </div>
            <div className="relative">
              <Label htmlFor="confirmPassword">Confirm Your Password</Label>
              <Input id="confirmPassword" name="confirmPassword" type={showConfirmPassword ? "text" : "password"} placeholder="••••••••" value={formData.confirmPassword} onChange={handleChange} className={`mt-1 ${inputClasses}`} />
              <Button variant="ghost" size="icon" onClick={() => setShowConfirmPassword(!showConfirmPassword)} className="absolute bottom-1 right-1 h-7 w-7 text-gray-500 hover:text-black">
                {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </Button>
            </div>
          </motion.div>
        );
      case 2: // Full Name & Phone
        return (
          <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5 }} className="space-y-6">
            <p className="text-center text-gray-300 text-sm">Great! Now, let's get to know you a bit better.</p>
            <div>
              <Label htmlFor="fullName">Full Name</Label>
              <Input id="fullName" name="fullName" type="text" placeholder="e.g., Bisi Adekunle" value={formData.fullName} onChange={handleChange} className={`mt-1 ${inputClasses}`} />
            </div>
            <div>
              <Label htmlFor="phone">Phone Number (Optional)</Label>
              <Input id="phone" name="phone" type="tel" placeholder="08012345678" value={formData.phone} onChange={handleChange} className={`mt-1 ${inputClasses}`} />
            </div>
          </motion.div>
        );
      case 3: // Location
        return (
          <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5 }} className="space-y-6">
            <p className="text-center text-gray-300 text-sm">Your location helps us tailor content for you.</p>
            <div>
              <Label htmlFor="state">State of Residence</Label>
              <Select name="state" onValueChange={(value) => handleSelectChange('state', value)} value={formData.state}>
                <SelectTrigger id="state" className={`mt-1 ${inputClasses}`}><SelectValue placeholder="Select your state" /></SelectTrigger>
                <SelectContent>
                  {nigerianStates.map(state => <SelectItem key={state} value={state}>{state}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="lga">LGA (Local Government Area)</Label>
              <Input id="lga" name="lga" type="text" placeholder="e.g., Ikeja" value={formData.lga} onChange={handleChange} className={`mt-1 ${inputClasses}`} />
            </div>
          </motion.div>
        );
      case 4: // Gender & Party
        return (
          <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5 }} className="space-y-6">
            <p className="text-center text-gray-300 text-sm">Just a couple more details to complete your profile.</p>
            <div>
              <Label htmlFor="gender">Gender</Label>
              <Select name="gender" onValueChange={(value) => handleSelectChange('gender', value)} value={formData.gender}>
                <SelectTrigger id="gender" className={`mt-1 ${inputClasses}`}><SelectValue placeholder="Select your gender" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="Male">Male</SelectItem>
                  <SelectItem value="Female">Female</SelectItem>
                  <SelectItem value="Prefer not to say">Prefer not to say</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="party">Political Affiliation</Label>
              <Select name="party" onValueChange={(value) => handleSelectChange('party', value)} value={formData.party}>
                <SelectTrigger id="party" className={`mt-1 ${inputClasses}`}><SelectValue placeholder="Select your party" /></SelectTrigger>
                <SelectContent>
                  {politicalParties.map(party => <SelectItem key={party} value={party}>{party}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
          </motion.div>
        );
      case 5: // Avatar
        return (
          <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5 }} className="space-y-6 flex flex-col items-center">
            <p className="text-center text-gray-300 text-sm">Last step! Add a profile picture to stand out.</p>
            <div className="relative">
              <Avatar className="w-32 h-32 border-4 border-primary/30">
                <AvatarImage src={formData.avatarPreview} alt="Avatar Preview" />
                <AvatarFallback className="text-4xl">{formData.fullName.split(' ').map(n => n[0]).join('') || 'U'}</AvatarFallback>
              </Avatar>
              <Label htmlFor="avatar-upload" className="absolute -bottom-1 -right-1 bg-primary text-primary-foreground rounded-full p-2 cursor-pointer hover:bg-primary/80 transition-colors">
                <Camera size={20} />
                <Input id="avatar-upload" type="file" accept="image/*" className="hidden" onChange={handleAvatarChange} />
              </Label>
            </div>
            <Button variant="link" onClick={handleSubmit} className="text-sm text-gray-400 hover:text-white">Skip for now</Button>
          </motion.div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4 bg-grid-pattern">
      <motion.div 
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="w-full max-w-md bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl shadow-2xl p-8 text-white"
      >
        <div className="text-center mb-4">
          <h1 className="text-4xl font-bold text-primary">Join LeaderBox</h1>
          <p className="text-gray-400 mt-2">Create your account to start connecting.</p>
        </div>
        
        <div className="flex items-center my-6">
          <div className="flex-1 border-t border-gray-700"></div>
          <span className="px-4 text-xs text-gray-500 uppercase">Step {step} of 5</span>
          <div className="flex-1 border-t border-gray-700"></div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {renderStepContent()}
          
          <div className="flex items-center justify-between mt-8">
            {step > 1 ? (
              <Button type="button" variant="outline" onClick={handlePrevStep} className="bg-transparent border-gray-600 hover:bg-gray-700">
                <ChevronLeft size={16} className="mr-1" /> Back
              </Button>
            ) : <div />}
            
            {step < 5 ? (
              <Button type="button" onClick={handleNextStep} className="btn-primary">
                Next
              </Button>
            ) : (
              <Button type="submit" className="btn-primary">
                Finish & Create Account
              </Button>
            )}
          </div>
        </form>

        <p className="text-center text-sm text-gray-400 mt-8">
          Already have an account?{' '}
          <Link to="/login" className="font-medium text-primary hover:underline">
            Log in here
          </Link>
        </p>
      </motion.div>
    </div>
  );
};

export default RegisterPage;
