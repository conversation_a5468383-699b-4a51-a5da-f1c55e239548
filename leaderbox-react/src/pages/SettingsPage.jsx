
import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch.jsx';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useToast } from '@/components/ui/use-toast';
import { User, Bell, Shield, LogOut, Eye, EyeOff, Camera } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const SettingsPage = () => {
  const { user, logout: authLogout, updateRegisteredUserDetails } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate(); 
  const avatarInputRef = useRef(null);

  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    state: user?.state || '',
    lga: user?.lga || '',
    party: user?.party || '',
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: '',
  });

  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);

  const [notifications, setNotifications] = useState({
    emailMentions: true,
    emailPollUpdates: false,
    appNewFollowers: true,
  });
  
  const [avatarPreview, setAvatarPreview] = useState(user?.avatarUrl || null);
  const [avatarFile, setAvatarFile] = useState(null);

  const handleProfileChange = (e) => {
    setProfileData({ ...profileData, [e.target.name]: e.target.value });
  };

  const handlePasswordChange = (e) => {
    setPasswordData({ ...passwordData, [e.target.name]: e.target.value });
  };

  const handleNotificationChange = (key) => {
    setNotifications({ ...notifications, [key]: !notifications[key] });
     toast({ title: "Preference Updated", description: "Notification preference saved (demo)." });
  };

  const handleProfileSave = () => {
    updateRegisteredUserDetails(user.id, profileData);
    toast({ title: "Profile Updated", description: "Your profile information has been saved." });
  };

  const handleChangePassword = () => {
    if (passwordData.newPassword !== passwordData.confirmNewPassword) {
      toast({ title: "Error", description: "New passwords do not match.", variant: "destructive" });
      return;
    }
    if (passwordData.newPassword.length < 6) {
       toast({ title: "Error", description: "New password must be at least 6 characters.", variant: "destructive" });
      return;
    }
    toast({ title: "Password Changed", description: "Your password has been updated (demo)." });
    setPasswordData({ currentPassword: '', newPassword: '', confirmNewPassword: '' });
  };
  
  const handleLogout = () => {
    authLogout();
    navigate('/login'); 
    toast({ title: "Logged Out", description: "You have been successfully logged out." });
  };

  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) { // 2MB limit
        toast({ title: "File too large", description: "Avatar image must be less than 2MB.", variant: "destructive" });
        return;
      }
      setAvatarFile(file);
      setAvatarPreview(URL.createObjectURL(file));
    }
  };

  const handleSaveAvatar = () => {
    if (avatarFile) {
      updateRegisteredUserDetails(user.id, { avatarUrl: avatarPreview });
      toast({ title: "Avatar Updated!", description: "Your new avatar has been saved." });
      setAvatarFile(null);
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto py-8 px-4 text-center">
        <p>Please log in to access settings.</p>
        <Button onClick={() => navigate('/login')} className="mt-4">Login</Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-8 px-4 space-y-8 max-w-3xl"
    >
      <header className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold text-foreground">Settings</h1>
        <p className="text-lg text-muted-foreground mt-1">Manage your account preferences and details.</p>
      </header>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-xl text-primary flex items-center"><User className="mr-2" /> Profile Information</CardTitle>
          <CardDescription>Update your personal details and avatar.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center gap-6">
            <div className="relative">
              <Avatar className="h-24 w-24 border-4 border-secondary">
                <AvatarImage src={avatarPreview} alt={user.name} />
                <AvatarFallback className="text-3xl">{user.name ? user.name.substring(0,1).toUpperCase() : user.email.substring(0,1).toUpperCase()}</AvatarFallback>
              </Avatar>
              <Button size="icon" className="absolute -bottom-1 -right-1 h-8 w-8 rounded-full btn-primary" onClick={() => avatarInputRef.current?.click()}>
                <Camera size={16} />
              </Button>
              <Input type="file" accept="image/*" ref={avatarInputRef} onChange={handleAvatarChange} className="hidden" />
            </div>
            <div className="flex-grow">
              <Label htmlFor="name">Full Name</Label>
              <Input id="name" name="name" value={profileData.name} onChange={handleProfileChange} className="modern-input mt-1" />
            </div>
          </div>
          {avatarFile && (
            <div className="flex justify-end">
              <Button onClick={handleSaveAvatar} size="sm" className="btn-accent">Save Avatar</Button>
            </div>
          )}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input id="email" name="email" type="email" value={profileData.email} onChange={handleProfileChange} className="modern-input mt-1" disabled />
            </div>
             <div>
              <Label htmlFor="party">Political Party Affiliation</Label>
              <Input id="party" name="party" value={profileData.party} onChange={handleProfileChange} className="modern-input mt-1" />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="state">State</Label>
              <Input id="state" name="state" value={profileData.state} onChange={handleProfileChange} className="modern-input mt-1" />
            </div>
            <div>
              <Label htmlFor="lga">LGA</Label>
              <Input id="lga" name="lga" value={profileData.lga} onChange={handleProfileChange} className="modern-input mt-1" />
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="btn-primary" onClick={handleProfileSave}>Save Profile Changes</Button>
        </CardFooter>
      </Card>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-xl text-primary flex items-center"><Shield className="mr-2" /> Change Password</CardTitle>
          <CardDescription>Update your account password.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="relative">
            <Label htmlFor="currentPassword">Current Password</Label>
            <Input id="currentPassword" name="currentPassword" type={showCurrentPassword ? "text" : "password"} value={passwordData.currentPassword} onChange={handlePasswordChange} className="modern-input mt-1 pr-10" />
             <Button type="button" variant="ghost" size="icon" className="absolute right-2 top-8 h-7 w-7 text-muted-foreground" onClick={() => setShowCurrentPassword(!showCurrentPassword)}>
                {showCurrentPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </Button>
          </div>
          <div className="relative">
            <Label htmlFor="newPassword">New Password</Label>
            <Input id="newPassword" name="newPassword" type={showNewPassword ? "text" : "password"} value={passwordData.newPassword} onChange={handlePasswordChange} className="modern-input mt-1 pr-10" />
            <Button type="button" variant="ghost" size="icon" className="absolute right-2 top-8 h-7 w-7 text-muted-foreground" onClick={() => setShowNewPassword(!showNewPassword)}>
                {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </Button>
          </div>
          <div className="relative">
            <Label htmlFor="confirmNewPassword">Confirm New Password</Label>
            <Input id="confirmNewPassword" name="confirmNewPassword" type={showConfirmNewPassword ? "text" : "password"} value={passwordData.confirmNewPassword} onChange={handlePasswordChange} className="modern-input mt-1 pr-10" />
            <Button type="button" variant="ghost" size="icon" className="absolute right-2 top-8 h-7 w-7 text-muted-foreground" onClick={() => setShowConfirmNewPassword(!showConfirmNewPassword)}>
                {showConfirmNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="btn-primary" onClick={handleChangePassword}>Change Password</Button>
        </CardFooter>
      </Card>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-xl text-primary flex items-center"><Bell className="mr-2" /> Notification Preferences</CardTitle>
          <CardDescription>Manage how you receive notifications from LeaderBox.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-secondary/30 rounded-md">
            <Label htmlFor="emailMentions" className="text-sm font-medium">Email for @mentions</Label>
            <Switch id="emailMentions" checked={notifications.emailMentions} onCheckedChange={() => handleNotificationChange('emailMentions')} />
          </div>
          <div className="flex items-center justify-between p-3 bg-secondary/30 rounded-md">
            <Label htmlFor="emailPollUpdates" className="text-sm font-medium">Email for poll updates & results</Label>
            <Switch id="emailPollUpdates" checked={notifications.emailPollUpdates} onCheckedChange={() => handleNotificationChange('emailPollUpdates')} />
          </div>
          <div className="flex items-center justify-between p-3 bg-secondary/30 rounded-md">
            <Label htmlFor="appNewFollowers" className="text-sm font-medium">In-app for new followers</Label>
            <Switch id="appNewFollowers" checked={notifications.appNewFollowers} onCheckedChange={() => handleNotificationChange('appNewFollowers')} />
          </div>
        </CardContent>
      </Card>
      
       <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-xl text-destructive flex items-center"><LogOut className="mr-2"/> Account Actions</CardTitle>
          <CardDescription>Manage your account session.</CardDescription>
        </CardHeader>
        <CardContent>
            <Button variant="destructive" className="w-full sm:w-auto" onClick={handleLogout}>
                Log Out
            </Button>
            <p className="text-xs text-muted-foreground mt-2">This will end your current session on this device.</p>
        </CardContent>
      </Card>

    </motion.div>
  );
};

export default SettingsPage;
