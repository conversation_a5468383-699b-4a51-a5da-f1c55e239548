import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card.jsx';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Star, Users, Briefcase, MapPin, UserPlus, SlidersHorizontal, UserCheck } from 'lucide-react';
import SuggestLeaderDialog from '@/components/home/<USER>';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils.js';
import { useToast } from '@/components/ui/use-toast.js';

const HomePage = () => {
  const { leaders, followLeader, unfollowLeader } = useLeaderData();
  const { user } = useAuth();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({ party: 'all', state: 'all', position: 'all' });
  const [showSuggestLeaderDialog, setShowSuggestLeaderDialog] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  const parties = useMemo(() => ['all', ...new Set(leaders.map(l => l.party).filter(Boolean))], [leaders]);
  const states = useMemo(() => ['all', ...new Set(leaders.map(l => l.state).filter(Boolean))], [leaders]);
  const positions = useMemo(() => ['all', ...new Set(leaders.map(l => l.position).filter(Boolean))], [leaders]);

  const recommendedLeaders = useMemo(() => {
    let sortedLeaders;
    if (user) {
      // Logged-in user logic
      sortedLeaders = leaders
        .map(leader => {
          let score = 0;
          if (leader.party === user.party) score += 10;
          if (leader.state === user.state) score += 5;
          score += (leader.followers || 0) / 1000; // Add popularity
          return { ...leader, recommendationScore: score };
        })
        .sort((a, b) => b.recommendationScore - a.recommendationScore);
    } else {
      // Logged-out user logic
      const shuffled = [...leaders].sort(() => 0.5 - Math.random());
      sortedLeaders = shuffled.sort((a, b) => (b.popularityScore || 0) - (a.popularityScore || 0));
    }
    return sortedLeaders;
  }, [leaders, user]);

  const filteredLeaders = useMemo(() => {
    return recommendedLeaders
      .filter(leader => leader.name.toLowerCase().includes(searchTerm.toLowerCase()))
      .filter(leader => filters.party === 'all' || leader.party === filters.party)
      .filter(leader => filters.state === 'all' || leader.state === filters.state)
      .filter(leader => filters.position === 'all' || leader.position === filters.position);
  }, [recommendedLeaders, searchTerm, filters]);

  const handleFilterChange = (type, value) => {
    setFilters(prev => ({ ...prev, [type]: value }));
  };
  
  const getPartyColor = (party) => {
    const colors = {
        'APC': 'bg-blue-500 text-white',
        'PDP': 'bg-green-600 text-white',
        'LP': 'bg-red-500 text-white',
        'NNPP': 'bg-orange-500 text-white',
        'APGA': 'bg-yellow-500 text-black',
    };
    return colors[party] || 'bg-gray-500 text-white';
  };

  const handleFollowToggle = (e, leader) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to follow leaders.", variant: "destructive" });
      return;
    }
    if (leader.isFollowed) {
      unfollowLeader(leader.id);
      toast({ title: `Unfollowed ${leader.name}` });
    } else {
      followLeader(leader.id);
      toast({ title: `Followed ${leader.name}` });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-8 px-4"
    >
      <header className="text-center mb-10 civic-icons-bg py-10 rounded-xl">
        <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight gradient-text from-primary to-accent">
          Connect With 11,000+ Political Leaders in Nigeria
        </h1>
        <p className="mt-4 text-lg text-muted-foreground max-w-3xl mx-auto">
          LeaderBox is a political social media platform where political leaders and citizens connect, engage and drive accountability.
        </p>
      </header>

      <div className="sticky top-[70px] md:top-[80px] z-30 bg-background/80 backdrop-blur-sm py-4 px-2 -mx-2 rounded-b-lg shadow-sm mb-4">
        <div className="max-w-5xl mx-auto flex flex-col md:flex-row gap-3 items-center">
            <div className="relative w-full md:flex-grow">
                 <Input
                  type="search"
                  placeholder="Search for a political leader..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="modern-input pl-4 pr-10 text-base h-11"
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            </div>
            <div className="hidden md:flex gap-3 items-center">
                <Select onValueChange={(value) => handleFilterChange('position', value)} defaultValue="all">
                  <SelectTrigger className="w-full lg:w-36 modern-input h-11"><Briefcase size={14} className="mr-2 text-muted-foreground"/> Position</SelectTrigger>
                  <SelectContent>{positions.map(pos => <SelectItem key={pos} value={pos}>{pos === 'all' ? 'All' : pos}</SelectItem>)}</SelectContent>
                </Select>
                <Select onValueChange={(value) => handleFilterChange('party', value)} defaultValue="all">
                  <SelectTrigger className="w-full lg:w-32 modern-input h-11"><Users size={14} className="mr-2 text-muted-foreground"/> Party</SelectTrigger>
                  <SelectContent>{parties.map(party => <SelectItem key={party} value={party}>{party === 'all' ? 'All' : party}</SelectItem>)}</SelectContent>
                </Select>
                <Select onValueChange={(value) => handleFilterChange('state', value)} defaultValue="all">
                  <SelectTrigger className="w-full lg:w-32 modern-input h-11"><MapPin size={14} className="mr-2 text-muted-foreground"/> State</SelectTrigger>
                  <SelectContent>{states.map(state => <SelectItem key={state} value={state}>{state === 'all' ? 'All' : state}</SelectItem>)}</SelectContent>
                </Select>
            </div>
            <div className="md:hidden w-full">
                <Button variant="outline" className="w-full flex items-center justify-center h-11" onClick={() => setShowMobileFilters(!showMobileFilters)}>
                    <SlidersHorizontal size={16} className="mr-2" />
                    <span>{showMobileFilters ? 'Hide Filters' : 'Show Filters'}</span>
                </Button>
            </div>
        </div>
        <AnimatePresence>
        {showMobileFilters && (
            <motion.div
                initial={{ opacity: 0, y: -10, height: 0 }}
                animate={{ opacity: 1, y: 0, height: 'auto' }}
                exit={{ opacity: 0, y: -10, height: 0 }}
                transition={{ duration: 0.2 }}
                className="md:hidden mt-3 grid grid-cols-1 sm:grid-cols-3 gap-3"
            >
                <Select onValueChange={(value) => handleFilterChange('position', value)} defaultValue="all">
                  <SelectTrigger className="w-full modern-input h-11"><Briefcase size={14} className="mr-2 text-muted-foreground"/> Position</SelectTrigger>
                  <SelectContent>{positions.map(pos => <SelectItem key={pos} value={pos}>{pos === 'all' ? 'All Positions' : pos}</SelectItem>)}</SelectContent>
                </Select>
                <Select onValueChange={(value) => handleFilterChange('party', value)} defaultValue="all">
                  <SelectTrigger className="w-full modern-input h-11"><Users size={14} className="mr-2 text-muted-foreground"/> Party</SelectTrigger>
                  <SelectContent>{parties.map(party => <SelectItem key={party} value={party}>{party === 'all' ? 'All Parties' : party}</SelectItem>)}</SelectContent>
                </Select>
                <Select onValueChange={(value) => handleFilterChange('state', value)} defaultValue="all">
                  <SelectTrigger className="w-full modern-input h-11"><MapPin size={14} className="mr-2 text-muted-foreground"/> State</SelectTrigger>
                  <SelectContent>{states.map(state => <SelectItem key={state} value={state}>{state === 'all' ? 'All States' : state}</SelectItem>)}</SelectContent>
                </Select>
            </motion.div>
        )}
        </AnimatePresence>
      </div>

      <h2 className="text-2xl font-bold text-foreground mb-6">Suggested For You</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
        {filteredLeaders.map(leader => (
          <motion.div key={leader.id} layout initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0.9 }} whileHover={{ y: -5, boxShadow: "0 10px 20px rgba(0,0,0,0.1)" }} className="h-full">
            <Card className="modern-card h-full flex flex-col text-center overflow-hidden">
              <CardHeader className="items-center p-4">
                <Link to={`/leader/${leader.id}`} className="block">
                  <Avatar className="w-24 h-24 mb-2 border-4 border-primary/20">
                    <AvatarImage src={leader.avatarUrl} alt={leader.name} />
                    <AvatarFallback>{leader.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <h3 className="text-lg font-semibold text-foreground hover:text-primary transition-colors truncate w-full" title={leader.name}>{leader.name}</h3>
                </Link>
              </CardHeader>
              <CardContent className="flex-grow p-4 pt-0">
                <p className="text-sm text-muted-foreground truncate" title={leader.position}>{leader.position}</p>
                 <div className="flex justify-center items-center gap-2 mt-2">
                    <Badge className={cn('text-xs', getPartyColor(leader.party))}>{leader.party}</Badge>
                    <Badge variant="secondary" className="text-xs">{leader.state}</Badge>
                 </div>
                 <Button 
                    size="sm" 
                    className="w-full mt-3" 
                    variant={user && leader.isFollowed ? "secondary" : "default"}
                    onClick={(e) => handleFollowToggle(e, leader)}
                  >
                    {user && leader.isFollowed ? <UserCheck size={16} className="mr-2" /> : <UserPlus size={16} className="mr-2" />}
                    {user && leader.isFollowed ? 'Following' : 'Follow'}
                  </Button>
              </CardContent>
              <CardFooter className="p-3 bg-secondary/30 flex justify-between items-center text-sm">
                 <div className="flex items-center text-amber-600 font-bold">
                  <Star size={16} className="mr-1 fill-current text-amber-500" /> {leader.currentRating.toFixed(1)}
                </div>
                <div className="flex items-center text-muted-foreground">
                  <Users size={14} className="mr-1" /> {leader.followers.toLocaleString()}
                </div>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredLeaders.length === 0 && (
        <div className="text-center py-16 modern-card col-span-full">
          <Search size={48} className="mx-auto text-muted-foreground/50 mb-4" />
          <p className="text-xl font-semibold text-foreground">No Leaders Found</p>
          <p className="text-muted-foreground mt-2">
            {searchTerm ? `No leaders match "${searchTerm}". Try a different search or suggest this leader.` : "Try adjusting your filters."}
          </p>
          {searchTerm && (
            <Button onClick={() => setShowSuggestLeaderDialog(true)} className="mt-4 btn-accent">
              <UserPlus size={18} className="mr-2" /> Suggest '{searchTerm}' to be added?
            </Button>
          )}
        </div>
      )}

      <SuggestLeaderDialog 
        isOpen={showSuggestLeaderDialog} 
        onClose={() => setShowSuggestLeaderDialog(false)} 
        leaderName={searchTerm} 
      />
    </motion.div>
  );
};

export default HomePage;