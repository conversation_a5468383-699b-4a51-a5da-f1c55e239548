
import React, { useState, useMemo } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { useGroups } from '@/contexts/GroupContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card.jsx';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, MessageSquare, Send } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import MediaGrid from '@/components/shared/MediaGrid.jsx';

const SingleDiscussionPage = () => {
  const { groupId, discussionId } = useParams();
  const { getGroupById, addReplyToDiscussion } = useGroups();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [replyText, setReplyText] = useState('');

  const { group, discussion } = useMemo(() => {
    const group = getGroupById(groupId);
    if (!group) return { group: null, discussion: null };
    const discussion = group.discussions.find(d => d.id === discussionId);
    return { group, discussion };
  }, [groupId, discussionId, getGroupById]);
  
  if (!group || !discussion) {
    return <div className="text-center p-10">Discussion not found.</div>;
  }
  
  const isMember = user && group.members.includes(user.id);

  const handleReplySubmit = () => {
    if (!replyText.trim()) {
      toast({ title: 'Cannot post empty reply.', variant: 'destructive'});
      return;
    }
    addReplyToDiscussion(groupId, discussionId, replyText);
    setReplyText('');
    toast({ title: 'Reply posted!'});
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-6 px-2 md:px-4 space-y-6 max-w-4xl"
    >
        <div>
            <Button variant="outline" onClick={() => navigate(`/group/${groupId}`)} className="btn-outline-primary mb-4">
                <ArrowLeft size={18} className="mr-2" /> Back to Group
            </Button>
            <p className="text-sm text-muted-foreground">
                Part of the <Link to={`/group/${groupId}`} className="text-primary hover:underline">{group.name}</Link> group.
            </p>
        </div>

        <Card className="modern-card">
            <CardHeader>
                <CardTitle className="text-2xl md:text-3xl text-primary">{discussion.title}</CardTitle>
                <div className="flex items-center space-x-2 pt-2">
                    <Link to={`/user/${discussion.authorId}`}>
                        <Avatar className="h-9 w-9">
                            <AvatarImage src={discussion.authorAvatarUrl} alt={discussion.authorName} />
                            <AvatarFallback>{discussion.authorName?.[0]}</AvatarFallback>
                        </Avatar>
                    </Link>
                    <div>
                        <Link to={`/user/${discussion.authorId}`} className="text-sm font-semibold text-foreground hover:underline">{discussion.authorName}</Link>
                        <p className="text-xs text-muted-foreground">{new Date(discussion.timestamp).toLocaleString()}</p>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <p className="text-foreground/90 whitespace-pre-wrap leading-relaxed">{discussion.content}</p>
                {discussion.media && discussion.media.length > 0 && (
                  <div className="mt-4 rounded-lg overflow-hidden border border-border">
                    <MediaGrid mediaItems={discussion.media} />
                  </div>
                )}
            </CardContent>
        </Card>

        <div className="space-y-4">
            <h3 className="text-xl font-semibold text-foreground flex items-center">
                <MessageSquare size={20} className="mr-2 text-primary" /> Replies ({discussion.replies.length})
            </h3>
            {discussion.replies.length > 0 ? (
                discussion.replies.map(reply => (
                    <Card key={reply.id} className="modern-card bg-secondary/30">
                        <CardHeader className="flex flex-row items-start space-x-3 p-4 pb-2">
                             <Link to={`/user/${reply.authorId}`}>
                                <Avatar className="h-8 w-8">
                                    <AvatarImage src={reply.authorAvatarUrl} alt={reply.authorName} />
                                    <AvatarFallback>{reply.authorName?.[0]}</AvatarFallback>
                                </Avatar>
                            </Link>
                            <div>
                                <Link to={`/user/${reply.authorId}`} className="text-sm font-semibold text-primary hover:underline">{reply.authorName}</Link>
                                <p className="text-xs text-muted-foreground">{new Date(reply.timestamp).toLocaleString()}</p>
                            </div>
                        </CardHeader>
                        <CardContent className="px-4 pb-4 pl-16">
                             <p className="text-sm text-foreground/90">{reply.text}</p>
                        </CardContent>
                    </Card>
                ))
            ) : (
                <p className="text-center text-muted-foreground py-4">No replies yet. Be the first one!</p>
            )}
        </div>
        
        {isMember && (
            <Card className="modern-card sticky bottom-20 md:bottom-4 z-10">
                <CardContent className="p-3">
                    <div className="flex items-start space-x-3">
                        <Avatar className="h-9 w-9 mt-1">
                            <AvatarImage src={user.avatarUrl} alt={user.name} />
                            <AvatarFallback>{user.name?.[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                            <Textarea 
                                value={replyText}
                                onChange={(e) => setReplyText(e.target.value)}
                                placeholder="Add your reply..."
                                className="modern-input"
                            />
                            <div className="flex justify-end mt-2">
                                <Button size="sm" onClick={handleReplySubmit} disabled={!replyText.trim()}>
                                    <Send size={14} className="mr-2"/> Post Reply
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        )}
    </motion.div>
  );
};

export default SingleDiscussionPage;
