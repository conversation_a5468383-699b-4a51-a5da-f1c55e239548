import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Send, Search, MessageCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useSearchParams } from 'react-router-dom';

const mockConversations = [
  { id: 'convo1', userId: 'userDemo123', userName: 'Demo User', lastMessage: 'Hey, how are you?', timestamp: new Date(Date.now() - 1000 * 60 * 5), unread: 2, avatarUrl: 'https://avatar.vercel.sh/demouser.png?size=40' },
  { id: 'convo2', userId: 'adminDemo456', userName: 'Admin User', lastMessage: 'Sure, I can help with that.', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), unread: 0, avatarUrl: 'https://avatar.vercel.sh/adminuser.png?size=40' },
  { id: 'convo3', userId: 'editorDemo789', userName: 'Editor User', lastMessage: 'Meeting at 3 PM.', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), unread: 1, avatarUrl: 'https://avatar.vercel.sh/editoruser.png?size=40' },
];

const mockMessages = {
  convo1: [
    { id: 'msg1', senderId: 'userDemo123', text: 'Hey, how are you?', timestamp: new Date(Date.now() - 1000 * 60 * 6) },
    { id: 'msg2', senderId: 'currentUserPlaceholder', text: 'I am good, thanks! You?', timestamp: new Date(Date.now() - 1000 * 60 * 5) },
  ],
  convo2: [
    { id: 'msg3', senderId: 'currentUserPlaceholder', text: 'Need help with my account.', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2 - 10000) },
    { id: 'msg4', senderId: 'adminDemo456', text: 'Sure, I can help with that.', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2) },
  ],
   convo3: [
    { id: 'msg5', senderId: 'editorDemo789', text: 'Meeting at 3 PM.', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24) },
  ],
};


const MessagesPage = () => {
  const { user: currentUser } = useAuth();
  const [searchParams] = useSearchParams();
  const targetUserId = searchParams.get('to');
  
  const [selectedConversationId, setSelectedConversationId] = React.useState(null);
  const [messageInput, setMessageInput] = React.useState('');

  React.useEffect(() => {
    if (targetUserId) {
      const existingConvo = mockConversations.find(c => c.userId === targetUserId);
      if (existingConvo) {
        setSelectedConversationId(existingConvo.id);
      } else {
        if (mockConversations.length > 0) setSelectedConversationId(mockConversations[0].id);
      }
    } else if (mockConversations.length > 0) {
      setSelectedConversationId(mockConversations[0].id);
    }
  }, [targetUserId]);

  const selectedConversation = mockConversations.find(c => c.id === selectedConversationId);
  const currentMessages = selectedConversationId ? (mockMessages[selectedConversationId] || []) : [];

  const handleSendMessage = () => {
    if (!messageInput.trim() || !selectedConversationId || !currentUser) return;
    
    const newMessage = { 
      id: `msg${Date.now()}`, 
      senderId: currentUser.id, 
      text: messageInput, 
      timestamp: new Date() 
    };

    mockMessages[selectedConversationId] = [
        ...(mockMessages[selectedConversationId] || []),
        newMessage
    ];
    
    const convoIndex = mockConversations.findIndex(c => c.id === selectedConversationId);
    if (convoIndex !== -1) {
        mockConversations[convoIndex].lastMessage = messageInput;
        mockConversations[convoIndex].timestamp = new Date();
    }

    setMessageInput('');
    setSelectedConversationId(prev => prev ? prev + '' : null); 
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="container mx-auto py-6 px-2 md:px-4 h-[calc(100vh-150px)] md:h-[calc(100vh-120px)]"
    >
      <Card className="modern-card h-full flex flex-col md:flex-row overflow-hidden">
        <div className="w-full md:w-1/3 lg:w-1/4 border-b md:border-b-0 md:border-r border-border flex flex-col">
          <CardHeader className="p-3 md:p-4 border-b border-border">
            <CardTitle className="text-lg md:text-xl text-primary flex items-center">
              <MessageCircle size={20} className="mr-2"/> Messages
            </CardTitle>
            <div className="relative mt-2">
              <Input placeholder="Search conversations..." className="modern-input pr-10 text-xs" />
              <Search size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent className="p-0 flex-grow overflow-y-auto">
            {mockConversations.sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp)).map(convo => (
              <div
                key={convo.id}
                className={`flex items-center space-x-3 p-3 cursor-pointer hover:bg-secondary/50 transition-colors ${selectedConversationId === convo.id ? 'bg-secondary/70' : ''}`}
                onClick={() => setSelectedConversationId(convo.id)}
              >
                <Avatar className="h-10 w-10">
                  <AvatarImage src={convo.avatarUrl} alt={convo.userName} />
                  <AvatarFallback>{convo.userName.substring(0,1)}</AvatarFallback>
                </Avatar>
                <div className="flex-grow overflow-hidden">
                  <p className="font-semibold text-sm text-foreground truncate">{convo.userName}</p>
                  <p className="text-xs text-muted-foreground truncate">{convo.lastMessage}</p>
                </div>
                {convo.unread > 0 && (
                  <span className="bg-primary text-primary-foreground text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                    {convo.unread}
                  </span>
                )}
              </div>
            ))}
          </CardContent>
        </div>

        <div className="w-full md:w-2/3 lg:w-3/4 flex flex-col bg-background">
          {selectedConversation ? (
            <>
              <CardHeader className="p-3 md:p-4 border-b border-border flex flex-row items-center space-x-3">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={selectedConversation.avatarUrl} alt={selectedConversation.userName} />
                  <AvatarFallback>{selectedConversation.userName.substring(0,1)}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-base md:text-lg text-foreground">{selectedConversation.userName}</CardTitle>
                  <p className="text-xs text-muted-foreground">Online</p>
                </div>
              </CardHeader>
              <CardContent className="flex-grow p-3 md:p-4 space-y-3 overflow-y-auto">
                {currentMessages.map(msg => (
                  <div key={msg.id} className={`flex ${msg.senderId === currentUser?.id ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[70%] p-2 md:p-3 rounded-xl text-sm ${msg.senderId === currentUser?.id ? 'bg-primary text-primary-foreground rounded-br-none' : 'bg-secondary text-secondary-foreground rounded-bl-none'}`}>
                      {msg.text}
                      <p className={`text-xs mt-1 ${msg.senderId === currentUser?.id ? 'text-primary-foreground/70 text-right' : 'text-muted-foreground/70 text-left'}`}>
                        {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>
                  </div>
                ))}
                 {currentMessages.length === 0 && (
                    <p className="text-center text-muted-foreground py-10">No messages yet. Start the conversation!</p>
                )}
              </CardContent>
              <div className="p-3 md:p-4 border-t border-border bg-secondary/30">
                <div className="flex items-center space-x-2">
                  <Input 
                    placeholder="Type your message..." 
                    className="modern-input flex-grow bg-background" 
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  />
                  <Button onClick={handleSendMessage} className="btn-primary" disabled={!messageInput.trim()}>
                    <Send size={18} />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-grow flex flex-col items-center justify-center text-center p-8">
              <MessageCircle size={48} className="text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold text-foreground">Select a conversation</h2>
              <p className="text-muted-foreground">Or start a new one by visiting a user's profile.</p>
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  );
};

export default MessagesPage;