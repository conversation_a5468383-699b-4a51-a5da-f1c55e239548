
import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { usePetitions } from '@/contexts/PetitionContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ArrowLeft, Users, UserCheck, FileText, Share2, Copy, CheckCircle, Paperclip, FileImage as ImageIcon, Video, FileText as FileTextIcon, Lock } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from "@/components/ui/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const getMediaIcon = (fileName) => {
  if (/\.(jpe?g|png|gif|webp)$/i.test(fileName)) return <ImageIcon className="h-5 w-5 text-blue-500" />;
  if (/\.(mp4|mov|avi|wmv)$/i.test(fileName)) return <Video className="h-5 w-5 text-purple-500" />;
  if (/\.(pdf)$/i.test(fileName)) return <FileTextIcon className="h-5 w-5 text-red-500" />;
  if (/\.(doc|docx)$/i.test(fileName)) return <FileTextIcon className="h-5 w-5 text-sky-500" />;
  return <Paperclip className="h-5 w-5 text-gray-500" />;
};

const AttachedMediaItem = ({ mediaItem }) => (
  <a 
    href={mediaItem.url || '#'} 
    target="_blank" 
    rel="noopener noreferrer"
    className="flex items-center gap-2 text-sm p-2 bg-secondary rounded-md hover:bg-secondary/80 transition-colors max-w-xs truncate"
    title={mediaItem.name}
  >
    {mediaItem.type?.startsWith('image/') && mediaItem.url ? (
      <img-replace src={mediaItem.url} alt={mediaItem.name} className="h-8 w-8 rounded object-cover" />
    ) : (
      getMediaIcon(mediaItem.name)
    )}
    <span className="truncate">{mediaItem.name}</span>
  </a>
);


const SinglePetitionPage = () => {
  const { petitionId } = useParams();
  const navigate = useNavigate();
  const { getPetitionById, signPetition: contextSignPetition } = usePetitions();
  const { user } = useAuth();
  const { toast } = useToast();

  const [petition, setPetition] = useState(null);
  const [showAllSigners, setShowAllSigners] = useState(false);

  useEffect(() => {
    const currentPetition = getPetitionById(petitionId);
    if (currentPetition) {
      setPetition(currentPetition);
    } else {
      toast({ title: "Error", description: "Petition not found.", variant: "destructive" });
      navigate("/petitions");
    }
  }, [petitionId, getPetitionById, toast, navigate, petition?.signatures]); 

  const handleSignPetition = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to sign petitions.", variant: "destructive" });
      navigate('/login');
      return;
    }
    const updatedPetition = contextSignPetition(petitionId);
    if (updatedPetition) {
      setPetition(updatedPetition); 
      toast({ title: "Petition Signed!", description: `You have successfully signed "${updatedPetition.title}".` });
    } else {
      toast({ title: "Already Signed or Error", description: "You may have already signed this petition, or an error occurred.", variant: "default" });
    }
  };

  const handleShare = () => {
    const shareUrl = window.location.href;
    const petitionTitle = petition?.title || "Support this cause!";
    const shareText = `Sign the petition: "${petitionTitle}" on LeaderBox! ${shareUrl}`;

    if (navigator.share) {
      navigator.share({
        title: petitionTitle,
        text: `Support the petition "${petitionTitle}" on LeaderBox!`,
        url: shareUrl,
      }).catch(console.error);
    } else {
      navigator.clipboard.writeText(shareText)
        .then(() => toast({ title: "Link Copied!", description: "Petition link copied to clipboard." }))
        .catch(() => toast({ title: "Error", description: "Could not copy link.", variant: "destructive" }));
    }
  };

  if (!petition) {
    return <div className="flex justify-center items-center min-h-[calc(100vh-200px)]"><p>Loading petition...</p></div>;
  }

  const isSigned = user && petition.signers && petition.signers.includes(user.name || user.email);
  const isCreator = user && petition.createdBy === (user.name || user.email.split('@')[0]);
  const canSign = user && !isSigned && !isCreator;
  const signersToShow = showAllSigners ? (petition.signers || []) : (petition.signers || []).slice(0, 15);
  const canViewSigners = isSigned || isCreator;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-3xl mx-auto space-y-6"
    >
      <Button variant="outline" onClick={() => navigate('/petitions')} className="mb-4 btn-outline-primary">
        <ArrowLeft size={18} className="mr-2" /> Back to Petitions
      </Button>

      <Card className="modern-card overflow-hidden">
        <CardHeader className="modern-card-header bg-gradient-to-r from-primary/10 via-card to-accent/10">
          <CardTitle className="text-2xl md:text-3xl font-bold text-primary">{petition.title}</CardTitle>
          <CardDescription className="text-sm text-muted-foreground mt-1">
            Created by: {petition.createdBy} {petition.taggedLeader && `| Tagged: ${petition.taggedLeader}`}
          </CardDescription>
          {petition.status === 'pending' && petition.signatures < 20 && (
            <p className="text-sm text-amber-600 font-semibold mt-2">
              This petition needs {20 - petition.signatures} more signature{20 - petition.signatures !== 1 ? 's' : ''} to become publicly visible.
            </p>
          )}
        </CardHeader>

        <CardContent className="modern-card-content space-y-4">
          <p className="text-md text-foreground/90 whitespace-pre-wrap leading-relaxed">{petition.description}</p>
          
           {petition.media && petition.media.length > 0 && (
            <div className="mt-4 space-y-2">
              <h4 className="text-sm font-semibold text-foreground">Attached Media:</h4>
              <div className="flex flex-wrap gap-3">
                {petition.media.map((item, index) => (
                  <AttachedMediaItem key={index} mediaItem={item} />
                ))}
              </div>
            </div>
          )}

          <div className="space-y-2 pt-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium text-primary flex items-center"><Users size={16} className="mr-1.5"/> {petition.signatures.toLocaleString()} Signatures</span>
              <span className="text-muted-foreground">Target: {petition.target.toLocaleString()}</span>
            </div>
            <Progress value={(petition.signatures / petition.target) * 100} className="h-3 rounded-full" />
            <p className="text-xs text-muted-foreground text-right">
              {((petition.signatures / petition.target) * 100).toFixed(1)}% towards goal
            </p>
          </div>
        </CardContent>

        <CardFooter className="modern-card-footer flex flex-col sm:flex-row items-center justify-between gap-3">
          {canSign ? (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button size="lg" className="btn-accent w-full sm:w-auto">
                  <UserCheck size={20} className="mr-2" /> Sign This Petition
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirm Your Support</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to sign and support the petition titled "{petition.title}"? Your name ({user?.name || user?.email}) will be added to the list of signers.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleSignPetition} className="bg-accent hover:bg-accent/90">Yes, Sign Petition</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          ) : (
            <Button size="lg" className="bg-muted text-muted-foreground hover:bg-muted/90 cursor-not-allowed w-full sm:w-auto" disabled>
              <CheckCircle size={20} className="mr-2" /> {isSigned ? 'You Have Signed' : (isCreator ? 'You Created This' : 'Sign This Petition')}
            </Button>
          )}
          <Button variant="outline" size="lg" onClick={handleShare} className="w-full sm:w-auto">
            <Share2 size={20} className="mr-2" /> Share Petition
          </Button>
        </CardFooter>
      </Card>

      {petition.signers && petition.signers.length > 0 && (
        <Card className="modern-card">
          <CardHeader className="modern-card-header">
            <CardTitle className="text-xl text-primary flex items-center">
              <Users size={22} className="mr-2" /> Signers ({petition.signers.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="modern-card-content">
            {canViewSigners ? (
              <>
                <div className="max-h-80 overflow-y-auto pr-2">
                  <ul className="space-y-1.5 text-sm text-foreground/80 columns-2 sm:columns-3 md:columns-4">
                    {signersToShow.map((signer, index) => (
                      <li key={index} className="truncate text-xs p-1 bg-secondary/30 rounded-sm">
                        {signer}
                      </li>
                    ))}
                  </ul>
                </div>
                {(petition.signers.length > 15 && !showAllSigners) && (
                  <Button variant="link" onClick={() => setShowAllSigners(true)} className="mt-3 text-primary p-0 h-auto">
                    Show all {petition.signers.length} signers...
                  </Button>
                )}
                {(petition.signers.length > 15 && showAllSigners) && (
                  <Button variant="link" onClick={() => setShowAllSigners(false)} className="mt-3 text-primary p-0 h-auto">
                    Show fewer signers
                  </Button>
                )}
              </>
            ) : (
              <div className="text-center py-6 bg-secondary/50 rounded-lg flex flex-col items-center">
                <Lock size={24} className="text-muted-foreground mb-2" />
                <p className="text-sm font-medium text-foreground">Signer list is private.</p>
                <p className="text-xs text-muted-foreground">Sign the petition to see who else has supported it.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
};

export default SinglePetitionPage;
