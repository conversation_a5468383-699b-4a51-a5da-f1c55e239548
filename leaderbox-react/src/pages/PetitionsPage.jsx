import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FileText, Users, Search, PlusCircle, Link as LinkIcon, User<PERSON>heck, Co<PERSON>, Paperclip } from 'lucide-react';
import { usePetitions } from '@/contexts/PetitionContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useToast } from "@/components/ui/use-toast";
import { useNavigate } from 'react-router-dom';
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Label } from '@/components/ui/label';
import MediaUpload from '@/components/ui/MediaUpload.jsx';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { Combobox } from '@/components/ui/combobox.jsx';

const PETITIONS_PER_PAGE = 20;

const PetitionCard = ({ petition, onSign, onReadMore, user }) => {
  const isSigned = user && petition.signers && petition.signers.includes(user.name || user.email);
  const isCreator = user && petition.createdBy === (user.name || user.email.split('@')[0]);
  const canSign = user && !isSigned && !isCreator;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="modern-card h-full flex flex-col"
    >
      <div className="modern-card-header">
        <h2 className="text-xl font-semibold text-primary hover:underline cursor-pointer" onClick={() => onReadMore(petition.id)}>{petition.title}</h2>
        <p className="text-xs text-muted-foreground">Created by: {petition.createdBy || "Community Member"} {petition.taggedLeader && `(Tagged: ${petition.taggedLeader})`}</p>
        {petition.status === 'pending' && petition.signatures < 20 && (
            <p className="text-xs text-amber-600 font-medium mt-1">Needs {20 - petition.signatures} more signatures to go public.</p>
        )}
      </div>
      <div className="modern-card-content flex-grow">
        <p className="text-foreground/80 text-sm mb-3 line-clamp-3">{petition.description}</p>
        <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-primary">{petition.signatures.toLocaleString()} signatures</span>
            <span className="text-xs text-muted-foreground">Target: {petition.target.toLocaleString()}</span>
        </div>
        <Progress value={(petition.signatures / petition.target) * 100} className="h-2" />
      </div>
      <div className="modern-card-footer flex items-center justify-end gap-2">
        <Button variant="outline" size="sm" className="button-outline-override" onClick={() => onReadMore(petition.id)}>
          <FileText size={16} className="mr-1.5" /> Read More
        </Button>
        {canSign ? (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button size="sm" className="btn-accent">
                <Users size={16} className="mr-1.5" /> Sign Petition
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirm Your Support</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to sign and support the petition titled "{petition.title}"?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={() => onSign(petition.id)} className="bg-accent hover:bg-accent/90">Yes, Sign Petition</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        ) : (
          <Button size="sm" className="bg-muted text-muted-foreground hover:bg-muted/90 cursor-not-allowed" disabled>
            <UserCheck size={16} className="mr-1.5" /> {isSigned ? 'Signed' : (isCreator ? 'Creator' : 'Sign Petition')}
          </Button>
        )}
      </div>
    </motion.div>
  );
};


const PetitionsPage = () => {
  const { petitions, addPetition, signPetition: contextSignPetition } = usePetitions();
  const { user } = useAuth();
  const { leaders } = useLeaderData();
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newPetitionData, setNewPetitionData] = useState({ title: '', description: '', target: 100, taggedLeader: '', media: [] });
  const [showShareModal, setShowShareModal] = useState(false);
  const [petitionToShare, setPetitionToShare] = useState(null);
  const [visiblePetitionsCount, setVisiblePetitionsCount] = useState(PETITIONS_PER_PAGE);

  const { toast } = useToast();
  const navigate = useNavigate();

  const leaderOptions = useMemo(() => leaders.map(l => ({ value: l.name, label: l.name })), [leaders]);

  const handleMediaChange = (files) => {
    setNewPetitionData(prev => ({ ...prev, media: files.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
        url: URL.createObjectURL(file) 
      })) 
    }));
  };

  const handleCreatePetition = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to create a petition.", variant: "destructive", action: <Button onClick={() => navigate('/login')}>Login</Button> });
      return;
    }
    if (!newPetitionData.title.trim() || !newPetitionData.description.trim()) {
      toast({ title: "Missing Information", description: "Title and description are required.", variant: "destructive" });
      return;
    }
    if (newPetitionData.target < 100) {
        toast({ title: "Invalid Target", description: "Signature target must be at least 100.", variant: "destructive" });
        return;
    }
    
    const createdPetition = addPetition(newPetitionData);
    if (createdPetition) {
        setShowCreateModal(false);
        setNewPetitionData({ title: '', description: '', target: 100, taggedLeader: '', media: [] });
        
        const petitionsToActivist = 10 - (user.petitionsCreated + 1);
        if (petitionsToActivist > 0) {
            toast({
                title: "Petition Created!",
                description: `Submit ${petitionsToActivist} more petitions to become an Activist.`,
                duration: 7000,
            });
        } else if (user.petitionsCreated + 1 === 10) {
             toast({
                title: "Petition Created & Badge Unlocked!",
                description: "Congratulations! You've earned the Activist badge!",
                duration: 7000,
            });
        } else {
            toast({ title: "Petition Created!", description: "Your petition is now live." });
        }
        
        if (createdPetition.signatures < 20) {
            setPetitionToShare(createdPetition);
            setShowShareModal(true);
        }
    } else {
        toast({ title: "Error", description: "Could not create petition.", variant: "destructive" });
    }
  };

  const handleSignPetition = (petitionId) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to sign petitions.", variant: "destructive" });
      navigate('/login');
      return;
    }
    const updatedPetition = contextSignPetition(petitionId);
    if (updatedPetition) {
      toast({ title: "Petition Signed!", description: `You have successfully signed "${updatedPetition.title}".` });
    } else {
      toast({ title: "Already Signed or Error", description: "You may have already signed this petition, or an error occurred.", variant: "default" });
    }
  };

  const handleReadMore = (petitionId) => {
    navigate(`/petition/${petitionId}`);
  };

  const copyShareLink = () => {
    if (!petitionToShare) return;
    const shareLink = `${window.location.origin}/petition/${petitionToShare.id}`;
    navigator.clipboard.writeText(shareLink)
      .then(() => toast({ title: "Link Copied!", description: "Petition link copied to clipboard." }))
      .catch(() => toast({ title: "Error", description: "Could not copy link.", variant: "destructive" }));
  };

  const filteredPetitions = useMemo(() => {
    return (petitions || [])
      .filter(p => p.status === 'active' || (p.status === 'pending' && p.createdBy === (user?.name || user?.email?.split('@')[0]))) 
      .filter(p => 
        p.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
        p.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (p.taggedLeader && p.taggedLeader.toLowerCase().includes(searchTerm.toLowerCase()))
      )
      .sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp)); 
  }, [petitions, searchTerm, user]);

  const petitionsToShow = filteredPetitions.slice(0, visiblePetitionsCount);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-5xl mx-auto space-y-6"
    >
      <header className="text-center py-6">
        <h1 className="text-4xl font-extrabold gradient-text from-primary to-accent">Petitions</h1>
        <p className="text-muted-foreground mt-2 text-lg">Make your voice heard. Start or sign petitions for causes you care about.</p>
      </header>

      <div className="flex flex-col sm:flex-row gap-4 items-center sticky top-[70px] md:top-[80px] z-30 bg-background/80 backdrop-blur-sm py-3 px-2 -mx-2 rounded-b-lg shadow-sm">
        <div className="relative flex-grow w-full sm:w-auto">
          <Input
            type="search"
            placeholder="Search petitions by title, keyword, or tagged leader..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="modern-input pl-4 pr-10 text-base h-11"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        </div>
        <Button onClick={() => user ? setShowCreateModal(true) : navigate('/login')} className="btn-primary rounded-full px-6 py-2.5 text-base w-full sm:w-auto h-11">
          <PlusCircle size={20} className="mr-2" /> Create Petition
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pt-4">
        {petitionsToShow.length > 0 ? (
          petitionsToShow.map((petition) => (
            <PetitionCard 
              key={petition.id} 
              petition={petition} 
              onSign={handleSignPetition} 
              onReadMore={handleReadMore}
              user={user}
            />
          ))
        ) : (
          <div className="text-center py-10 modern-card md:col-span-2 lg:col-span-3">
            <FileText size={48} className="mx-auto text-muted-foreground/50 mb-4" />
            <p className="text-xl text-muted-foreground">No petitions found matching your search.</p>
            {searchTerm === '' && <p className="text-sm text-muted-foreground">Why not start one?</p>}
          </div>
        )}
      </div>
      
      {filteredPetitions.length > visiblePetitionsCount && (
        <div className="text-center mt-8">
          <Button onClick={() => setVisiblePetitionsCount(prev => prev + PETITIONS_PER_PAGE)} className="btn-outline-primary">
            Load More Petitions
          </Button>
        </div>
      )}


      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-lg bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-2xl">Create New Petition</DialogTitle>
            <DialogDescription>
              Clearly state the change you want to see. Be specific and concise.
              Petitions need 20 signatures to become publicly visible.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
            <div>
              <Label htmlFor="petition-title" className="text-sm font-medium">Petition Title</Label>
              <Input 
                id="petition-title" 
                value={newPetitionData.title} 
                onChange={(e) => setNewPetitionData({...newPetitionData, title: e.target.value})}
                placeholder="e.g., Improve Local Road Network in Alimosho LGA"
                className="modern-input mt-1"
              />
            </div>
            <div>
              <Label htmlFor="petition-description" className="text-sm font-medium">Description</Label>
              <Textarea 
                id="petition-description" 
                value={newPetitionData.description} 
                onChange={(e) => setNewPetitionData({...newPetitionData, description: e.target.value})}
                placeholder="Explain the issue, who it affects, and your proposed solution..."
                className="modern-input min-h-[120px] mt-1"
              />
            </div>
             <div>
              <Label htmlFor="petition-target" className="text-sm font-medium">Signature Target</Label>
              <Input 
                id="petition-target"
                type="number"
                value={newPetitionData.target} 
                onChange={(e) => setNewPetitionData({...newPetitionData, target: parseInt(e.target.value, 10) || 100})}
                min="100"
                className="modern-input mt-1"
              />
            </div>
            <div>
              <Label htmlFor="petition-tag-leader" className="text-sm font-medium">Tag a Leader (Optional)</Label>
               <Combobox
                options={leaderOptions}
                value={newPetitionData.taggedLeader}
                onChange={(value) => setNewPetitionData(d => ({...d, taggedLeader: value}))}
                placeholder="Select or type leader..."
                inputClassName="modern-input mt-1"
              />
            </div>
            <div>
              <Label htmlFor="petition-media" className="text-foreground font-semibold flex items-center">
                <Paperclip size={14} className="mr-1.5 text-muted-foreground" /> Attach Media (Optional)
              </Label>
              <MediaUpload onFilesChange={handleMediaChange} />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">Cancel</Button>
            </DialogClose>
            <Button type="button" onClick={handleCreatePetition} className="btn-primary">Create Petition</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showShareModal} onOpenChange={setShowShareModal}>
        <DialogContent className="sm:max-w-md bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-primary text-xl">Share Your Petition!</DialogTitle>
            <DialogDescription>
              Your petition "{petitionToShare?.title}" needs <strong>{Math.max(0, 20 - (petitionToShare?.signatures || 0))} more signatures</strong> to become publicly visible.
              Share this link with your friends and supporters:
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="flex items-center space-x-2 p-2 border border-dashed rounded-md bg-secondary/50">
              <LinkIcon className="h-5 w-5 text-muted-foreground" />
              <Input 
                readOnly 
                value={`${window.location.origin}/petition/${petitionToShare?.id}`} 
                className="flex-1 bg-transparent border-none focus-visible:ring-0 text-sm"
              />
              <Button variant="ghost" size="icon" onClick={copyShareLink} className="h-8 w-8">
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">Once you have 20 signatures, your petition will be visible to everyone on LeaderBox.</p>
          </div>
          <DialogFooter>
            <Button type="button" onClick={() => setShowShareModal(false)} className="btn-primary">Done</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </motion.div>
  );
};

export default PetitionsPage;