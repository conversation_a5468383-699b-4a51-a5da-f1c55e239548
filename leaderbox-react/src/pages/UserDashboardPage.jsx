import React, { useMemo, useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card.jsx";
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area.jsx'; 
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { usePollData } from '@/contexts/PollContext.jsx';
import { useBanter } from '@/contexts/BanterContext.jsx';
import { usePetitions } from '@/contexts/PetitionContext.jsx';
import { useGroups } from '@/contexts/GroupContext.jsx';
import { Link, useNavigate } from 'react-router-dom';
import { MessageSquare, BarChart2, FileText, UserCheck, Activity, Star, Zap, Lightbulb, LayoutDashboard, Users } from 'lucide-react';

import ActivityLogItem from '@/components/dashboard/ActivityLogItem.jsx';
import SuggestedActionCard from '@/components/dashboard/SuggestedActionCard.jsx';
import DashboardFeedTab from '@/components/dashboard/DashboardFeedTab.jsx';
import DashboardActivityTab from '@/components/dashboard/DashboardActivityTab.jsx';
import DashboardLeadersTab from '@/components/dashboard/DashboardLeadersTab.jsx';

const UserDashboardPage = () => {
  const { user } = useAuth();
  const { leaders, leaderUserRatings } = useLeaderData();
  const { polls } = usePollData();
  const { banters } = useBanter();
  const { petitions } = usePetitions();
  const { getGroupsForUser } = useGroups();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('feed');

  const followedLeadersList = useMemo(() => {
    return leaders.filter(leader => user?.followedLeaders?.includes(leader.id));
  }, [leaders, user]);
  
  const userGroups = useMemo(() => {
    if (!user) return [];
    return getGroupsForUser(user.id);
  }, [user, getGroupsForUser]);

  const userBanterActivity = useMemo(() => {
    if (!user) return [];
    return banters.filter(b => b.authorId === user.id || (b.comments || []).some(c => c.userId === user.id)).slice(0, 5);
  }, [banters, user]);

  const userPollActivity = useMemo(() => {
    if (!user) return [];
    return polls.filter(p => p.userVotes && p.userVotes[user.id]).slice(0, 5);
  }, [polls, user]);

  const userPetitionActivity = useMemo(() => {
    if (!user) return [];
    return petitions.filter(p => p.createdBy === (user.name || user.email.split('@')[0]) || (p.signers || []).includes(user.name || user.email)).slice(0, 5);
  }, [petitions, user]);

  const customFeedItems = useMemo(() => {
    if (!user) return [];
    let feed = [];

    banters.forEach(banter => {
      if ((banter.details || "").toLowerCase().includes(`@${(user.name || user.email.split('@')[0]).toLowerCase()}`)) {
        feed.push({ type: 'banter_mention', data: banter, timestamp: banter.createdAt });
      }
      (banter.comments || []).forEach(comment => {
        if ((comment.text || "").toLowerCase().includes(`@${(user.name || user.email.split('@')[0]).toLowerCase()}`)) {
          feed.push({ type: 'banter_mention', data: { ...banter, title: `Mention in: ${banter.title}` }, timestamp: comment.timestamp });
        }
      });
    });
    
    userPollActivity.forEach(poll => {
      feed.push({ type: 'poll_voted', data: poll, timestamp: poll.createdAt }); 
    });

    followedLeadersList.slice(0, 3).forEach(leader => {
        const recentRating = leaderUserRatings[leader.id]?.[user.id];
        const updateText = recentRating ? `You rated them ${recentRating.rating} stars recently.` : "Check their latest activities.";
        feed.push({ 
            type: 'leader_followed_update', 
            data: { ...leader, update: updateText }, 
            timestamp: recentRating?.lastRated || new Date(Date.now() - Math.random() * 1000000000).toISOString() 
        });
    });
    
    petitions.filter(p => p.status === 'active' && (p.taggedLeader && followedLeadersList.find(fl => fl.name === p.taggedLeader) || (user?.state && p.location === user.state))).slice(0,2).forEach(petition => {
        feed.push({ type: 'new_petition_location', data: {...petition, location: user.state || 'Nigeria'}, timestamp: petition.timestamp });
    });

    return feed.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 15);
  }, [user, banters, userPollActivity, followedLeadersList, petitions, leaderUserRatings]);

  const activityTimeline = useMemo(() => {
    if (!user) return [];
    let activities = [];
    
    Object.entries(leaderUserRatings || {}).forEach(([leaderId, userRatings]) => {
        if(userRatings[user.id]) {
            const leader = leaders.find(l => l.id === leaderId);
            if (leader) {
                activities.push({ 
                    type: 'rated_leader', 
                    text: `You rated ${leader.name} ${userRatings[user.id].rating} stars.`, 
                    timestamp: userRatings[user.id].lastRated 
                });
            }
        }
    });

    userPollActivity.forEach(poll => {
      activities.push({ type: 'voted_poll', text: `You voted on: "${poll.topic.substring(0,30)}..."`, timestamp: poll.createdAt }); 
    });

    userPetitionActivity.forEach(petition => {
        if (petition.createdBy === (user.name || user.email.split('@')[0])) {
            activities.push({ type: 'created_petition', text: `You created petition: "${petition.title.substring(0,30)}..."`, timestamp: petition.timestamp });
        } else if ((petition.signers || []).includes(user.name || user.email)) {
            activities.push({ type: 'signed_petition', text: `You signed petition: "${petition.title.substring(0,30)}..."`, timestamp: petition.timestamp });
        }
    });

    userBanterActivity.forEach(banter => {
         if (banter.authorId === user.id) {
            activities.push({ type: 'created_banter', text: `You started banter: "${banter.title.substring(0,30)}..."`, timestamp: banter.createdAt });
         }
    });

    return activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10);
  }, [user, leaderUserRatings, leaders, userPollActivity, userPetitionActivity, userBanterActivity]);

  const suggestedActions = useMemo(() => {
    if (!user) return [];
    let suggestions = [];
    
    const unratedLeaders = leaders.filter(l => !(leaderUserRatings[l.id] && leaderUserRatings[l.id][user.id])).slice(0,1);
    if(unratedLeaders.length > 0) {
        suggestions.push({ 
            title: `Rate ${unratedLeaders[0].name}`, 
            description: "Share your opinion on their performance.",
            link: `/leader/${unratedLeaders[0].id}`, 
            actionText: "Rate Now",
            icon: <Star className="w-5 h-5 text-yellow-500" />
        });
    } else if (leaders.length > 0) {
         suggestions.push({ 
            title: "Review a Leader's Profile", 
            description: `Check out ${leaders[Math.floor(Math.random()*leaders.length)].name}'s latest updates.`,
            link: `/leader/${leaders[Math.floor(Math.random()*leaders.length)].id}`, 
            actionText: "View Profile",
            icon: <UserCheck className="w-5 h-5 text-purple-500" />
        });
    }

    const trendingPoll = polls.sort((a,b) => (b.totalVotes + (b.comments?.length ||0)) - (a.totalVotes + (a.comments?.length||0))).find(p => !(p.userVotes && p.userVotes[user.id]));
    if(trendingPoll) {
        suggestions.push({ 
            title: `Vote on: ${trendingPoll.topic.substring(0,25)}...`,
            description: "Make your voice heard in this trending poll.",
            link: `/poll/${trendingPoll.id}`, 
            actionText: "Vote Now",
            icon: <BarChart2 className="w-5 h-5 text-green-500" />
        });
    }
    
    if (followedLeadersList.length > 0) {
        const randomFollowedLeader = followedLeadersList[Math.floor(Math.random() * followedLeadersList.length)];
        const banterAboutLeader = banters.find(b => (b.title + b.details).toLowerCase().includes(randomFollowedLeader.name.toLowerCase()));
        if (banterAboutLeader) {
            suggestions.push({ 
                title: `Read banters about ${randomFollowedLeader.name}`,
                description: `"${banterAboutLeader.title.substring(0,30)}..."`,
                link: `/banter/${banterAboutLeader.id}`, 
                actionText: "Read Banter",
                icon: <MessageSquare className="w-5 h-5 text-blue-500" />
            });
        }
    } else if (banters.length > 0) {
         suggestions.push({ 
            title: "Explore Banter Room", 
            description: `Check out "${banters[Math.floor(Math.random()*banters.length)].title.substring(0,30)}..."`,
            link: `/banter/${banters[Math.floor(Math.random()*banters.length)].id}`, 
            actionText: "Join In",
            icon: <MessageSquare className="w-5 h-5 text-blue-500" />
        });
    }
    
    return suggestions.slice(0,3);
  }, [user, leaders, polls, banters, followedLeadersList, leaderUserRatings]);

  const dashboardTabs = [
    { value: "feed", label: "Feed", icon: LayoutDashboard },
    { value: "groups", label: "Groups", icon: Users },
    { value: "banter", label: "Banter", icon: MessageSquare },
    { value: "polls", label: "Polls", icon: BarChart2 },
    { value: "petitions", label: "Petitions", icon: FileText },
    { value: "leaders", label: "Leaders", icon: UserCheck }
  ];

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center p-4">
        <Zap size={64} className="text-primary mb-4" />
        <h1 className="text-2xl font-bold mb-2">Access Your Dashboard</h1>
        <p className="text-muted-foreground mb-6">Please log in to view your personalized feed and activities.</p>
        <Button onClick={() => navigate('/login')} className="btn-primary">Login</Button>
      </div>
    );
  }

  return (
    <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="container mx-auto py-6 px-2 md:px-4 space-y-8"
    >
      <header className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold text-foreground">Welcome back, <span className="gradient-text from-primary to-accent">{user.name || user.email.split('@')[0]}!</span></h1>
        <p className="text-lg text-muted-foreground mt-1">Here's what's happening in your political sphere.</p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="overflow-x-auto no-scrollbar">
                    <TabsList className="inline-flex w-max sm:w-full sm:grid sm:grid-cols-6 bg-secondary/50 rounded-lg p-1 mb-2">
                        {dashboardTabs.map(tab => (
                            <TabsTrigger 
                                key={tab.value} 
                                value={tab.value} 
                                className="dashboard-tab-trigger"
                            >
                                <tab.icon size={16} className="mr-1.5 hidden sm:inline"/>{tab.label}
                            </TabsTrigger>
                        ))}
                    </TabsList>
                </div>

                <TabsContent value="feed">
                    <DashboardFeedTab items={customFeedItems} />
                </TabsContent>
                <TabsContent value="groups">
                     <DashboardActivityTab 
                        activity={userGroups.map(g => ({...g, title: g.name, createdAt: g.createdAt}))} 
                        type="Group" 
                        linkPrefix="/group"
                        emptyMessage="You haven't joined any groups yet."
                        ctaLink="/groups"
                        ctaText="Discover groups!"
                    />
                </TabsContent>
                <TabsContent value="banter">
                     <DashboardActivityTab 
                        activity={userBanterActivity} 
                        type="Banter" 
                        linkPrefix="/banter"
                        emptyMessage="No banter activity yet."
                        ctaLink="/banter-room"
                        ctaText="Join or start a discussion!"
                    />
                </TabsContent>
                 <TabsContent value="polls">
                    <DashboardActivityTab 
                        activity={userPollActivity} 
                        type="Poll" 
                        linkPrefix="/poll"
                        emptyMessage="You haven't voted on any polls yet."
                        ctaLink="/polls"
                        ctaText="Explore polls now!"
                    />
                </TabsContent>
                <TabsContent value="petitions">
                     <DashboardActivityTab 
                        activity={userPetitionActivity} 
                        type="Petition" 
                        linkPrefix="/petition"
                        emptyMessage="No petition activity yet."
                        ctaLink="/petitions"
                        ctaText="Start or sign a petition!"
                    />
                </TabsContent>
                <TabsContent value="leaders">
                    <DashboardLeadersTab leaders={followedLeadersList} />
                </TabsContent>
            </Tabs>
        </div>

        <div className="space-y-6">
            <Card className="modern-card">
                <CardHeader>
                    <CardTitle className="text-lg text-primary flex items-center"><Lightbulb size={20} className="mr-2"/>Suggested Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                    {suggestedActions.length > 0 ? suggestedActions.map((action, index) => (
                        <SuggestedActionCard key={index} suggestion={action} />
                    )) : <p className="text-sm text-muted-foreground p-4 text-center">No specific suggestions right now. Keep exploring!</p>}
                </CardContent>
            </Card>

            <Card className="modern-card">
                <CardHeader>
                    <CardTitle className="text-lg text-primary flex items-center"><Activity size={20} className="mr-2"/>Activity Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                    <ScrollArea className="h-[250px] pr-3">
                        {activityTimeline.length > 0 ? activityTimeline.map((activity, index) => (
                            <ActivityLogItem key={`${activity.type}-${index}`} activity={activity} />
                        )) : <p className="text-sm text-muted-foreground p-4 text-center">No recent activity to show.</p>}
                    </ScrollArea>
                </CardContent>
            </Card>
        </div>
      </div>
      <style jsx>{`
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .no-scrollbar {
            -ms-overflow-style: none; 
            scrollbar-width: none; 
        }
        .dashboard-tab-trigger {
            @apply data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-3 py-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1.5 whitespace-nowrap;
        }
      `}</style>
    </motion.div>
  );
};

export default UserDashboardPage;