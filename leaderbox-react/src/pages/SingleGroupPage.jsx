import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useGroups } from '@/contexts/GroupContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { motion } from 'framer-motion';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users, Lock, Globe, MessageSquare, Info, BarChart, Settings, UserPlus, ChevronRight, Award } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import StartDiscussionDialog from '@/components/groups/StartDiscussionDialog.jsx';
import { Progress } from '@/components/ui/progress.jsx';

const SingleGroupPage = () => {
  const { groupId } = useParams();
  const { getGroupById, startNewDiscussion } = useGroups();
  const { user, users: allUsers } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showStartDiscussionDialog, setShowStartDiscussionDialog] = useState(false);
  
  const group = getGroupById(groupId);

  if (!group) {
    return <div className="text-center p-10">Group not found.</div>;
  }

  const isMember = user && group.members.includes(user.id);
  const isCreator = user && group.creatorId === user.id;
  const membersCount = group.members.length;

  const handleJoinGroup = () => {
    toast({ title: "Joined Group! (Demo)", description: `You are now a member of ${group.name}.` });
  };

  const handleStartDiscussionSubmit = (discussionData) => {
    const newDiscussion = startNewDiscussion(groupId, discussionData);
    if(newDiscussion) {
        toast({ title: "Discussion Started!", description: `Your topic "${newDiscussion.title}" is live.`});
        navigate(`/group/${groupId}/discussion/${newDiscussion.id}`);
    }
  };

  const getMemberDetails = (memberId) => {
      return allUsers.find(u => u.id === memberId) || { id: memberId, name: 'Unknown User', avatarUrl: '' };
  }

  const canViewMembers = isMember || isCreator || user?.isAdmin;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-6 px-2 md:px-4 space-y-6"
    >
      <Card className="modern-card overflow-hidden">
        <div className="relative">
          <img alt={`${group.name} cover`} className="w-full h-48 md:h-64 object-cover" src={group.coverImage || "https://images.unsplash.com/photo-1595872018818-97555653a011"} />
          <div className="absolute inset-0 bg-black/50" />
          <div className="absolute bottom-4 left-4 text-white">
            <h1 className="text-3xl md:text-4xl font-bold">{group.name}</h1>
            <p className="flex items-center text-sm">
              {group.isPublic ? <Globe size={14} className="mr-1.5"/> : <Lock size={14} className="mr-1.5"/>}
              {group.isPublic ? 'Public' : 'Private'} Group &bull; {membersCount} Members
            </p>
          </div>
          <div className="absolute top-4 right-4">
             {user && (
                 isMember ? (
                     <Button variant="secondary" onClick={() => toast({ title: "Left Group (Demo)" })}>Leave Group</Button>
                 ) : (
                    group.isPublic && <Button onClick={handleJoinGroup} className="btn-primary">Join Group</Button>
                 )
             )}
             {!user && group.isPublic && <Button onClick={() => navigate('/login')} className="btn-primary">Join Group</Button>}
          </div>
        </div>
      </Card>
      
      {isCreator && membersCount < 1000 && (
        <Card className="modern-card bg-accent/10 border-accent/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Award size={32} className="text-accent flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-foreground">Become a Political Organizer</h3>
                <p className="text-xs text-muted-foreground">Increase your group membership to 1,000 members to earn the Organizer badge!</p>
                <Progress value={(membersCount / 1000) * 100} className="mt-2 h-2" />
                <p className="text-xs text-right text-muted-foreground mt-1">{membersCount} / 1,000 Members</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="discussions" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 bg-secondary/50 rounded-lg p-1 mb-4">
          <TabsTrigger value="discussions">Discussions</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
          {isCreator && <TabsTrigger value="analytics">Analytics</TabsTrigger>}
        </TabsList>

        <TabsContent value="discussions">
          <Card className="modern-card">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-primary flex items-center"><MessageSquare size={20} className="mr-2"/> Discussions</CardTitle>
              {isMember && <Button size="sm" className="btn-primary flex-shrink-0" onClick={() => setShowStartDiscussionDialog(true)}><UserPlus size={16} className="mr-1.5"/> <span className="truncate">Start Discussion</span></Button>}
            </CardHeader>
            <CardContent>
              {group.discussions.length > 0 ? (
                <ul className="space-y-3">
                  {group.discussions.map(d => (
                    <li key={d.id}>
                        <Link to={`/group/${groupId}/discussion/${d.id}`} className="block p-3 bg-secondary/30 hover:bg-secondary/50 rounded-md transition-colors group">
                            <div className="flex justify-between items-center">
                                <div>
                                    <p className="font-semibold text-foreground group-hover:text-primary">{d.title}</p>
                                    <p className="text-xs text-muted-foreground">by {d.authorName} &bull; {d.replies.length} replies</p>
                                </div>
                                <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-transform group-hover:translate-x-1" />
                            </div>
                        </Link>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-center text-muted-foreground py-4">No discussions yet. Be the first to start one!</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="members">
          <Card className="modern-card">
            <CardHeader><CardTitle className="text-primary flex items-center"><Users size={20} className="mr-2"/> Members ({membersCount})</CardTitle></CardHeader>
            <CardContent>
                {canViewMembers ? (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        {group.members.map(memberId => {
                            const member = getMemberDetails(memberId);
                            return (
                                <Link to={`/user/${member.id}`} key={memberId} className="flex flex-col items-center text-center p-2 rounded-lg hover:bg-secondary">
                                <Avatar>
                                    <AvatarImage src={member.avatarUrl || `https://avatar.vercel.sh/${member.id}.png?size=40`} />
                                    <AvatarFallback>{member.name.substring(0,2).toUpperCase()}</AvatarFallback>
                                </Avatar>
                                <p className="text-sm font-medium mt-1 truncate w-full">{member.name}</p>
                                </Link>
                            )
                        })}
                    </div>
                ) : (
                    <div className="text-center py-8 text-muted-foreground">
                        <Lock size={32} className="mx-auto mb-2" />
                        <p>Members list is private and only visible to group members and admins.</p>
                    </div>
                )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about">
          <Card className="modern-card">
            <CardHeader><CardTitle className="text-primary flex items-center"><Info size={20} className="mr-2"/> About This Group</CardTitle></CardHeader>
            <CardContent className="space-y-4">
              <p className="text-foreground/90">{group.description}</p>
              <div>
                <p className="font-semibold">Tags:</p>
                <div className="flex flex-wrap gap-2 mt-1">
                  {group.tags.leader && <Badge variant="secondary">Leader: {group.tags.leader}</Badge>}
                  {group.tags.location && <Badge variant="secondary">Location: {group.tags.location}</Badge>}
                  {group.tags.party && <Badge variant="secondary">Party: {group.tags.party}</Badge>}
                  {group.tags.keywords.map(kw => <Badge key={kw} variant="outline">{kw}</Badge>)}
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                <p>Created by {group.creatorName} on {new Date(group.createdAt).toLocaleDateString()}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {isCreator && (
            <TabsContent value="analytics">
                <Card className="modern-card">
                <CardHeader><CardTitle className="text-primary flex items-center"><BarChart size={20} className="mr-2"/> Group Analytics</CardTitle></CardHeader>
                <CardContent>
                    <p className="text-muted-foreground text-center py-8">Group member demographics and analytics will be displayed here.</p>
                </CardContent>
                </Card>
            </TabsContent>
        )}
      </Tabs>
      <StartDiscussionDialog 
        isOpen={showStartDiscussionDialog} 
        onClose={() => setShowStartDiscussionDialog(false)}
        onSubmit={handleStartDiscussionSubmit}
       />
    </motion.div>
  );
};

export default SingleGroupPage;