import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLeaderData } from '@/contexts/LeaderContext.jsx'; 
import { useAuth } from '@/contexts/AuthContext.jsx'; 
import { motion } from 'framer-motion';
import { useToast } from "@/components/ui/use-toast";
import { Button } from '@/components/ui/button';
import { Edit2 } from 'lucide-react';

import LeaderProfileHeader from '@/components/leader-profile/LeaderProfileHeader.jsx';
import LeaderMetricsCard from '@/components/leader-profile/LeaderMetricsCard.jsx';
import LeaderEngagementCard from '@/components/leader-profile/LeaderEngagementCard.jsx';
import LeaderBioCard from '@/components/leader-profile/LeaderBioCard.jsx';
import LeaderActivityTabs from '@/components/leader-profile/LeaderActivityTabs.jsx';
import RatingDialog from '@/components/leader-profile/RatingDialog.jsx';
import DirectMessageDialog from '@/components/leader-profile/DirectMessageDialog.jsx';
import SuggestEditDialog from '@/components/leader-profile/SuggestEditDialog.jsx';


const LeaderProfilePage = () => {
  const { leaderId } = useParams();
  const navigate = useNavigate();
  const { 
    leaders, 
    followLeader, 
    unfollowLeader, 
    rateLeader, 
    getLeaderRatingByUser, 
    getCommentsForLeader,
    leaderUserRatings, 
    leaderComments 
  } = useLeaderData(); 
  const { user } = useAuth(); 
  const { toast } = useToast();

  const [leader, setLeader] = useState(null);
  const [userRatingDetails, setUserRatingDetails] = useState(null);
  const [canRateAgain, setCanRateAgain] = useState(true);
  const [commentsList, setCommentsList] = useState([]);
  
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [ratingModalCurrentRating, setRatingModalCurrentRating] = useState(0);
  const [ratingModalCommentText, setRatingModalCommentText] = useState('');
  const [ratingModalHoverRating, setRatingModalHoverRating] = useState(0);

  const [showDirectMessageModal, setShowDirectMessageModal] = useState(false);
  const [showSuggestEditModal, setShowSuggestEditModal] = useState(false);


  useEffect(() => {
    const foundLeader = leaders.find(l => l.id === leaderId);
    if (foundLeader) {
      setLeader(foundLeader);
      
      const userSpecificRating = getLeaderRatingByUser(leaderId); 
      if (userSpecificRating) {
        setUserRatingDetails(userSpecificRating);
        const twentyFourHours = 24 * 60 * 60 * 1000;
        setCanRateAgain(Date.now() - (userSpecificRating.lastRated || 0) >= twentyFourHours);
      } else {
        setUserRatingDetails(null);
        setCanRateAgain(true);
      }
      setCommentsList(getCommentsForLeader(leaderId));
    } else {
      toast({ title: "Error", description: "Leader profile not found.", variant: "destructive"});
      navigate("/");
    }
  }, [leaderId, leaders, user, getLeaderRatingByUser, getCommentsForLeader, leaderUserRatings, leaderComments, navigate, toast]);


  const handleFollowToggle = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to follow.", variant: "destructive", action: <Button onClick={() => navigate('/login')} className="ml-2 p-1 bg-primary text-primary-foreground rounded">Login</Button> });
      return;
    }
    if (!leader) return;

    if (leader.isFollowed) {
      unfollowLeader(leaderId); 
      toast({ title: "Unfollowed", description: `You unfollowed ${leader.name}.` });
    } else {
      followLeader(leaderId); 
      toast({ title: "Followed", description: `You are now following ${leader.name}.` });
    }
  };

  const openRatingDialog = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to rate.", variant: "destructive", action: <Button onClick={() => navigate('/login')} className="ml-2 p-1 bg-primary text-primary-foreground rounded">Login</Button> });
      return;
    }
    if (!leader) return;

    if (!canRateAgain && userRatingDetails) {
       toast({ title: "Already Rated", description: "You can update your rating after 24 hours.", variant: "default" });
       return;
    }
    setRatingModalCurrentRating(userRatingDetails?.rating || 0);
    setRatingModalCommentText(userRatingDetails?.comment || '');
    setShowRatingModal(true);
  };

  const handleSubmitRating = () => {
    if (!leader) return;
    if (ratingModalCurrentRating === 0) {
      toast({ title: "Rating Required", description: "Please select a star rating.", variant: "destructive" });
      return;
    }
    rateLeader(leaderId, ratingModalCurrentRating, ratingModalCommentText);
    toast({ title: "Rating Submitted!", description: `You rated ${leader.name} ${ratingModalCurrentRating} stars. ${ratingModalCommentText ? 'Thanks for your comment!' : ''}`, duration: 4000 });
    setShowRatingModal(false);
  };
  
  const openDirectMessageDialog = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to send a message.", variant: "destructive", action: <Button onClick={() => navigate('/login')} className="ml-2 p-1 bg-primary text-primary-foreground rounded">Login</Button> });
      return;
    }
    setShowDirectMessageModal(true);
  };

  const openSuggestEditDialog = () => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to suggest an edit.", variant: "destructive", action: <Button onClick={() => navigate('/login')} className="ml-2 p-1 bg-primary text-primary-foreground rounded">Login</Button> });
      return;
    }
    setShowSuggestEditModal(true);
  };

  const handleSubmitSuggestion = (suggestionData) => {
    if (!leader) return;
    console.log("Suggestion for leader:", leader.name, suggestionData);
    toast({
      title: "Suggestion Submitted!",
      description: "Thank you for helping keep LeaderBox up-to-date. Your suggestion will be reviewed.",
    });
    setShowSuggestEditModal(false);
  };


  if (!leader) {
    return <div className="flex justify-center items-center min-h-[calc(100vh-200px)]"><p>Loading leader profile...</p></div>;
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }} 
      animate={{ opacity: 1 }} 
      transition={{ duration: 0.5 }}
      className="space-y-6 md:space-y-8"
    >
      <LeaderProfileHeader leader={leader} />
      
      <div className="flex justify-end -mt-4 mr-2 md:mr-0">
          <Button variant="outline" size="sm" onClick={openSuggestEditDialog} className="button-outline-override text-xs">
              <Edit2 size={14} className="mr-1.5"/> Suggest Edit/Update
          </Button>
      </div>


      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mt-2">
        <LeaderMetricsCard leader={leader} />
        <LeaderEngagementCard 
          leaderName={leader.name}
          isFollowing={leader.isFollowed} 
          onFollowToggle={handleFollowToggle}
          onRate={openRatingDialog}
          canRateAgain={canRateAgain}
          userRatingDetails={userRatingDetails}
          onSendMessage={openDirectMessageDialog}
        />
      </div>

      <LeaderBioCard leader={leader} />
      <LeaderActivityTabs leader={leader} commentsList={commentsList} user={user} />

      <RatingDialog
        isOpen={showRatingModal}
        onClose={() => setShowRatingModal(false)}
        leaderName={leader.name}
        currentRating={ratingModalCurrentRating}
        setCurrentRating={setRatingModalCurrentRating}
        commentText={ratingModalCommentText}
        setCommentText={setRatingModalCommentText}
        hoverRating={ratingModalHoverRating}
        setHoverRating={setRatingModalHoverRating}
        onSubmit={handleSubmitRating}
      />
      <DirectMessageDialog
        isOpen={showDirectMessageModal}
        onClose={() => setShowDirectMessageModal(false)}
        leaderName={leader.name}
      />
      <SuggestEditDialog
        isOpen={showSuggestEditModal}
        onClose={() => setShowSuggestEditModal(false)}
        leader={leader}
        onSubmit={handleSubmitSuggestion}
      />

    </motion.div>
  );
};

export default LeaderProfilePage;