import React, { useState } from 'react';
import { useLeaderData } from '@/contexts/LeaderContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card.jsx';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { UserPlus, UserCheck, Check } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';

const FollowSuggestionsPage = () => {
  const { leaders, followLeader, unfollowLeader } = useLeaderData();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const suggestedLeaders = leaders.sort((a, b) => b.followers - a.followers).slice(0, 20);
  
  const followedCount = user?.followedLeaders?.length || 0;

  const handleFollowToggle = (leaderId, isFollowed, leaderName) => {
    if (isFollowed) {
      unfollowLeader(leaderId);
    } else {
      followLeader(leaderId);
      toast({
        title: `Followed ${leaderName}`,
        description: "You can see their updates in your feed.",
        action: (
          <Button size="sm" onClick={() => navigate('/dashboard')}>
            Go to Feed
          </Button>
        ),
      });
    }
  };
  
  const handleContinue = () => {
    toast({
      title: "Welcome to LeaderBox!",
      description: `You are following ${followedCount} leaders. Explore your dashboard to see what's happening.`,
    });
    navigate('/dashboard');
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-8 px-4"
    >
      <div className="text-center max-w-2xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-extrabold text-foreground">Welcome to LeaderBox!</h1>
        <p className="mt-2 text-lg text-muted-foreground">
          To get started, follow some political leaders. This will help us personalize your feed with relevant updates, polls, and discussions.
        </p>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mt-10">
        {suggestedLeaders.map((leader) => {
          const isFollowed = user?.followedLeaders?.includes(leader.id);
          return (
            <Card key={leader.id} className="modern-card p-4 flex flex-col items-center justify-center text-center">
              <Avatar className="w-20 h-20 mb-3 border-2 border-primary/20">
                <AvatarImage src={leader.avatarUrl} alt={leader.name} />
                <AvatarFallback>{leader.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <p className="font-semibold text-sm truncate w-full" title={leader.name}>{leader.name}</p>
              <p className="text-xs text-muted-foreground truncate w-full" title={leader.position}>{leader.position}</p>
              <Button
                size="sm"
                variant={isFollowed ? "secondary" : "default"}
                className="mt-3 w-full"
                onClick={() => handleFollowToggle(leader.id, isFollowed, leader.name)}
              >
                {isFollowed ? <UserCheck size={14} className="mr-1.5" /> : <UserPlus size={14} className="mr-1.5" />}
                {isFollowed ? 'Following' : 'Follow'}
              </Button>
            </Card>
          );
        })}
      </div>

      <div className="fixed bottom-0 left-0 right-0 p-4 bg-background/80 backdrop-blur-sm border-t border-border z-10">
        <div className="container mx-auto flex items-center justify-center">
            <Button size="lg" className="btn-primary" onClick={handleContinue} disabled={followedCount < 1}>
                <Check size={20} className="mr-2"/>
                Continue {followedCount > 0 && `(Following ${followedCount})`}
            </Button>
        </div>
        {followedCount < 1 && <p className="text-xs text-center text-muted-foreground mt-2">Follow at least one leader to continue.</p>}
      </div>
    </motion.div>
  );
};

export default FollowSuggestionsPage;