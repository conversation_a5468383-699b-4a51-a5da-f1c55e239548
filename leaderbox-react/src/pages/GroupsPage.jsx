
import React, { useState, useMemo } from 'react';
import { useGroups } from '@/contexts/GroupContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Users, Lock, Globe, PlusCircle, Search, TrendingUp, Sparkles } from 'lucide-react';
import CreateGroupDialog from '@/components/groups/CreateGroupDialog.jsx';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const GroupCard = ({ group }) => (
  <motion.div
    layout
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
    transition={{ duration: 0.3 }}
  >
    <Card className="modern-card h-full flex flex-col">
      <CardHeader className="p-0">
        <Link to={`/group/${group.id}`}>
          <img
            alt={`${group.name} cover`}
            className="w-full h-32 object-cover rounded-t-lg"
            src={group.coverImage || `https://source.unsplash.com/random/400x200?community,nigeria,${group.id}`}
          />
        </Link>
      </CardHeader>
      <CardContent className="flex-grow p-4">
         <CardTitle className="text-lg text-primary hover:underline mb-1">
          <Link to={`/group/${group.id}`}>{group.name}</Link>
        </CardTitle>
        <p className="text-sm text-muted-foreground line-clamp-2">{group.description}</p>
      </CardContent>
      <CardFooter className="p-3 border-t border-border flex justify-between items-center text-xs">
        <div className="flex items-center text-muted-foreground">
          <Users size={14} className="mr-1.5"/> {group.members.length} Members
        </div>
        <Badge variant={group.isPublic ? "secondary" : "default"} className={group.isPublic ? "border-green-500/50 text-green-700 dark:text-green-400" : "bg-destructive/80"}>
          {group.isPublic ? <Globe size={12} className="mr-1"/> : <Lock size={12} className="mr-1"/>}
          {group.isPublic ? 'Public' : 'Private'}
        </Badge>
      </CardFooter>
    </Card>
  </motion.div>
);

const GroupsPage = () => {
  const { groups, getGroupsForUser } = useGroups();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('trending');
  const [showCreateGroupDialog, setShowCreateGroupDialog] = useState(false);

  const handleCreateGroupClick = () => {
    if (!user) {
      navigate('/login');
    } else {
      setShowCreateGroupDialog(true);
    }
  };

  const visibleGroups = useMemo(() => {
    return groups.filter(group => group.status === 'visible' && (group.isPublic || (user && group.members.includes(user.id))));
  }, [groups, user]);

  const filteredGroups = useMemo(() => {
    let sourceGroups = [];
    if (activeTab === 'trending') {
      sourceGroups = [...visibleGroups].sort((a, b) => b.members.length - a.members.length);
    } else if (activeTab === 'new') {
      sourceGroups = [...visibleGroups].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    } else if (activeTab === 'my-groups' && user) {
      sourceGroups = getGroupsForUser(user.id);
    } else {
      sourceGroups = visibleGroups;
    }
    
    if (searchTerm) {
      return sourceGroups.filter(group => 
        group.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
        group.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    return sourceGroups;
  }, [visibleGroups, activeTab, user, searchTerm, getGroupsForUser]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-8 px-4"
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
            <h1 className="text-4xl font-extrabold text-foreground">Discover Groups</h1>
            <p className="text-muted-foreground mt-1">Join communities to discuss topics that matter to you.</p>
        </div>
        <Button onClick={handleCreateGroupClick} className="btn-primary self-start md:self-center">
          <PlusCircle size={18} className="mr-2" /> Create a Group
        </Button>
      </div>

      <div className="mb-6 sticky top-[70px] md:top-[80px] z-30 bg-background/80 backdrop-blur-sm py-3 px-2 -mx-2 rounded-b-lg shadow-sm">
        <div className="relative">
          <Input 
            type="search"
            placeholder="Search for groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="modern-input pl-4 pr-10 h-11"
          />
          <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
        <TabsList className="grid w-full grid-cols-3 md:w-auto md:inline-flex bg-secondary/50 rounded-lg p-1">
          <TabsTrigger value="trending" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
            <TrendingUp size={18} /> Trending
          </TabsTrigger>
          <TabsTrigger value="new" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
            <Sparkles size={18} /> New
          </TabsTrigger>
          {user && (
            <TabsTrigger value="my-groups" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
              <Users size={18} /> My Groups
            </TabsTrigger>
          )}
        </TabsList>
      </Tabs>

      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          {filteredGroups.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredGroups.map(group => (
                <GroupCard key={group.id} group={group} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16 modern-card bg-secondary/30">
              <p className="text-xl font-semibold">No groups found</p>
              <p className="text-muted-foreground mt-2">
                {searchTerm ? `No groups match "${searchTerm}".` : `There are no groups in this category yet.`}
              </p>
            </div>
          )}
        </motion.div>
      </AnimatePresence>

      <CreateGroupDialog isOpen={showCreateGroupDialog} onClose={() => setShowCreateGroupDialog(false)} />
    </motion.div>
  );
};

export default GroupsPage;
