import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Users, MessageSquare, BarChart2, FileText as FileTextIcon, ShieldCheck, TrendingUp, Edit, ShieldAlert, ShieldX, Activity, Briefcase, Newspaper, UserCog, Users2, Mail } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { usePollData } from '@/contexts/PollContext.jsx';
import { useBanter } from '@/contexts/BanterContext.jsx';
import { usePetitions } from '@/contexts/PetitionContext.jsx';

const ImportantReportsCard = () => {
  const reports = [
    { title: "Total Users", value: "1,250", trend: "+15 since last week", change: "+1.2%", icon: <Users className="w-5 h-5 text-blue-500" /> },
    { title: "Daily Active Users (DAU)", value: "300", trend: "+5% vs yesterday", change: "+5%", icon: <Activity className="w-5 h-5 text-green-500" /> },
    { title: "Monthly Active Users (MAU)", value: "980", trend: "-2% vs last month", change: "-2%", icon: <Briefcase className="w-5 h-5 text-orange-500" /> },
  ];
  return (
    <Card className="modern-card col-span-1 md:col-span-2 lg:col-span-3">
      <CardHeader><CardTitle className="text-xl text-primary">Platform Overview</CardTitle></CardHeader>
      <CardContent className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {reports.map(report => (
          <div key={report.title} className="p-4 bg-secondary/30 rounded-lg border border-border/50 flex items-start space-x-3">
            <div className="p-2 bg-primary/10 rounded-md">
                {report.icon}
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{report.title}</p>
              <p className="text-2xl font-bold text-foreground">{report.value}</p>
              <p className={`text-xs ${report.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>{report.trend}</p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

const LatestContentCard = ({ title, items, itemType, icon }) => {
  const navigateTo = (id) => {
    if (itemType === 'poll') return `/poll/${id}`;
    if (itemType === 'banter') return `/banter/${id}`;
    if (itemType === 'petition') return `/petition/${id}`;
    return "/";
  };
  return (
    <Card className="modern-card">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium text-primary">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        {items.length > 0 ? (
          <ul className="space-y-2">
            {items.map(item => (
              <li key={item.id} className="text-sm text-muted-foreground hover:text-primary">
                <Link to={navigateTo(item.id)} className="truncate block">
                  {item.title || item.topic}
                </Link>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-muted-foreground">No recent {itemType}s.</p>
        )}
      </CardContent>
    </Card>
  );
};


const AdminDashboardPage = () => {
  const { user } = useAuth();
  const { polls } = usePollData();
  const { banters } = useBanter();
  const { petitions } = usePetitions();

  const latestPolls = polls.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, 5);
  const latestBanters = banters.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, 5);
  const latestPetitions = petitions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 5);


  const canManageUsers = user?.role === 'Overall Admin';

  const adminSections = [
    { title: "Leader Profiles", icon: <Users className="w-6 h-6" />, link: "/admin/leaders", roles: ["Overall Admin", "Editor"] },
    { title: "Banter Management", icon: <MessageSquare className="w-6 h-6" />, link: "/admin/banters", roles: ["Overall Admin", "Editor", "Content Creator"] },
    { title: "Poll Management", icon: <BarChart2 className="w-6 h-6" />, link: "/admin/polls", roles: ["Overall Admin", "Editor", "Content Creator"] },
    { title: "Content Moderation", icon: <ShieldCheck className="w-6 h-6" />, link: "/admin/moderation", roles: ["Overall Admin", "Editor"] },
    { title: "Engagement Metrics", icon: <TrendingUp className="w-6 h-6" />, link: "/admin/metrics", roles: ["Overall Admin", "Editor"] },
    { title: "Keyword Blacklist", icon: <ShieldX className="w-6 h-6" />, link: "/admin/keywords", roles: ["Overall Admin", "Editor"] },
    { title: "Admin Users", icon: <UserCog className="w-6 h-6" />, link: "/admin/users", roles: ["Overall Admin"] },
    { title: "Registered Users", icon: <Users2 className="w-6 h-6" />, link: "/admin/registered-users", roles: ["Overall Admin"] },
    { title: "Email Settings", icon: <Mail className="w-6 h-6" />, link: "/admin/email-settings", roles: ["Overall Admin"] },
  ];
  
  return (
    <div className="space-y-6">
      <header className="mb-6">
        <h1 className="text-2xl md:text-3xl font-extrabold text-foreground">Admin Dashboard</h1>
        <p className="text-md text-muted-foreground mt-1">Manage LeaderBox platform content and users.</p>
        <p className="text-xs text-accent mt-0.5">Your Role: {user?.role || 'Admin'}</p>
      </header>

      <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        <ImportantReportsCard />
        <LatestContentCard title="Latest Polls" items={latestPolls} itemType="poll" icon={<BarChart2 className="w-5 h-5 text-muted-foreground" />} />
        <LatestContentCard title="Latest Banters" items={latestBanters} itemType="banter" icon={<MessageSquare className="w-5 h-5 text-muted-foreground" />} />
        <LatestContentCard title="Latest Petitions" items={latestPetitions} itemType="petition" icon={<FileTextIcon className="w-5 h-5 text-muted-foreground" />} />
      </div>
      
      <h2 className="text-xl md:text-2xl font-bold text-foreground pt-4 md:pt-6 border-t border-border">Management Sections</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {adminSections.filter(section => section.roles.includes(user?.role || "")).map((section) => (
          <Link to={section.link} key={section.title} className="block">
            <Card className="modern-card h-full hover:border-primary transition-all group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-md md:text-lg font-medium text-primary group-hover:text-primary/80">{section.title}</CardTitle>
                <span className="text-muted-foreground group-hover:text-primary">{section.icon}</span>
              </CardHeader>
              <CardContent>
                <p className="text-xs text-muted-foreground">Access and manage {section.title.toLowerCase()}.</p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      
      {user?.role === "Content Creator" && (
        <div className="mt-6 p-4 bg-secondary/50 rounded-lg border border-border">
            <h2 className="text-md font-semibold text-primary mb-2 flex items-center"><Edit className="mr-2" />Your Content Area</h2>
            <p className="text-sm text-muted-foreground">As a Content Creator, you can add and manage banters and polls you've personally created. Use the links above to navigate to the respective management pages.</p>
        </div>
      )}

      {user?.role === "Editor" && !canManageUsers && (
         <div className="mt-6 p-4 bg-secondary/50 rounded-lg border border-border">
            <h2 className="text-md font-semibold text-primary mb-2 flex items-center"><ShieldAlert className="mr-2" />Editor Privileges</h2>
            <p className="text-sm text-muted-foreground">You have access to manage all platform content (leaders, banters, polls, comments). User management and role assignments are restricted.</p>
        </div>
      )}
    </div>
  );
};

export default AdminDashboardPage;