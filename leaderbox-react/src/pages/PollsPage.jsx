import React, { useState, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { usePollData } from '@/contexts/PollContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { PlusCircle, TrendingUp, Clock, MessageSquare, Users, CheckSquare, Info, <PERSON>rkles, ListChecks, ChevronDown, ThumbsDown } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import { cn } from '@/lib/utils';
import CommentAfterVoteDialog from '@/components/polls/CommentAfterVoteDialog.jsx';
import CreatePollDialog from '@/components/polls/CreatePollDialog.jsx';

const POLLS_PER_PAGE = 20;

const PollCard = ({ poll, onVoteClick, user }) => {
  const navigate = useNavigate();
  const hasVoted = user && poll.userVotes && poll.userVotes[user.id];
  const [showBackground, setShowBackground] = useState(false);

  const optionColors = [
    "poll-option-btn-1",
    "poll-option-btn-2",
    "poll-option-btn-3",
    "poll-option-btn-4",
    "poll-option-btn-5",
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="h-full"
    >
      <Card className="modern-card h-full flex flex-col overflow-hidden">
        <CardHeader className="modern-card-header pb-3">
          <Link to={`/poll/${poll.id}`}>
            <CardTitle className="text-xl text-primary hover:underline cursor-pointer">{poll.topic}</CardTitle>
          </Link>
          <p className="text-xs text-muted-foreground">
            By {poll.source} &bull; {new Date(poll.createdAt).toLocaleDateString()}
          </p>
           {poll.description && poll.description.length > 20 && (
             <Button variant="link" size="sm" onClick={() => setShowBackground(!showBackground)} className="text-xs text-muted-foreground hover:text-primary p-0 h-auto mt-1">
               <Info size={12} className="mr-1" /> Show Background {showBackground ? <ChevronDown className="ml-1 h-3 w-3 rotate-180" /> : <ChevronDown className="ml-1 h-3 w-3" />}
             </Button>
           )}
        </CardHeader>

        <AnimatePresence>
          {showBackground && poll.description && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="px-5 md:px-6 text-xs text-muted-foreground"
            >
              <p className="border-l-2 border-primary/50 pl-2 py-1 bg-secondary/30 rounded-r-md">
                {poll.description.substring(0, 150)}{poll.description.length > 150 ? '...' : ''} 
                <Link to={`/poll/${poll.id}`} className="text-primary/80 hover:underline ml-1">Read more</Link>
              </p>
            </motion.div>
          )}
        </AnimatePresence>
        
        <CardContent className="modern-card-content flex-grow space-y-2 mt-2">
          <div className="flex flex-col gap-2">
            {poll.options.map((option, index) => (
              <Button
                key={option.id}
                onClick={() => !hasVoted && onVoteClick(poll, option.id)}
                disabled={hasVoted}
                className={cn(
                  "flex-1 justify-start rounded-md text-xs sm:text-sm py-2 px-3 h-auto min-w-[80px]",
                  hasVoted ? "opacity-70 cursor-not-allowed bg-muted text-muted-foreground" : optionColors[index % optionColors.length],
                  hasVoted && poll.userVotes[user?.id] === option.id && "!opacity-100 ring-2 ring-offset-2 ring-primary text-primary-foreground font-semibold"
                )}
              >
                {option.text}
                {hasVoted && (
                  <span className="ml-auto pl-2 text-xs opacity-90">({option.votes} vote{option.votes !==1 ? 's' : ''})</span>
                )}
              </Button>
            ))}
          </div>
           {!hasVoted && <p className="text-xs text-muted-foreground italic pt-1">Select an option to vote.</p>}
           {hasVoted && <p className="text-xs text-green-600 font-medium italic pt-1">You have voted on this poll.</p>}
        </CardContent>
        <CardFooter className="modern-card-footer flex-col items-start space-y-2">
          <div className="flex justify-between w-full text-xs text-muted-foreground">
            <span className="flex items-center"><Users size={14} className="mr-1" /> {poll.totalVotes || 0} Total Votes</span>
            <span className="flex items-center"><MessageSquare size={14} className="mr-1" /> {poll.commentsCount || 0} Comments</span>
          </div>
            <Button asChild variant="outline" className="w-full btn-outline-primary text-sm">
              <Link to={`/poll/${poll.id}`}>
                <MessageSquare size={16} className="mr-2" /> View Discussion & Results
              </Link>
            </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

const PollsPage = () => {
  const { polls, createPoll, voteOnPoll } = usePollData();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showCreatePollDialog, setShowCreatePollDialog] = useState(false);
  
  const [pollForCommentModal, setPollForCommentModal] = useState(null);
  const [selectedOptionForCommentModal, setSelectedOptionForCommentModal] = useState(null);
  const [visiblePollsCount, setVisiblePollsCount] = useState(POLLS_PER_PAGE);
  const [activeTab, setActiveTab] = useState('trending');


  const handleCreatePoll = (pollData) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to create a poll.", variant: "destructive" });
      navigate('/login');
      return;
    }
    const newPoll = createPoll(pollData);
    if (newPoll) {
      toast({ title: "Poll Created!", description: "Your poll has been successfully created." });
      navigate(`/poll/${newPoll.id}`);
    } else {
      toast({ title: "Error", description: "Could not create poll.", variant: "destructive" });
    }
  };

  const handleVoteFromCard = (poll, optionId) => {
    if (!user) {
      toast({ title: "Login Required", description: "Please log in to vote.", variant: "destructive" });
      navigate('/login');
      return;
    }
    setSelectedOptionForCommentModal(optionId);
    setPollForCommentModal(poll);
  };

  const submitVoteWithOptionalComment = (pollId, optionId, comment) => {
    voteOnPoll(pollId, optionId, comment); 
    toast({ title: "Vote Cast!", description: "Your vote has been recorded." });
    setPollForCommentModal(null);
    setSelectedOptionForCommentModal(null);
  };
  
  const pollsToDisplay = useMemo(() => {
    let sortedPolls;
    if (activeTab === 'trending') {
      sortedPolls = [...polls].sort((a, b) => (b.totalVotes + (b.comments?.length || 0)) - (a.totalVotes + (a.comments?.length || 0)));
    } else { // 'new'
      sortedPolls = [...polls].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    }
    return sortedPolls.slice(0, visiblePollsCount);
  }, [polls, activeTab, visiblePollsCount]);

  const totalPollsInCurrentTab = useMemo(() => {
    if (activeTab === 'trending') {
      return polls.length; // approximation for trending for now
    } else {
      return polls.length;
    }
  }, [polls, activeTab]);


  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="space-y-8"
    >
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 bg-gradient-to-r from-primary/10 via-card to-accent/10 rounded-xl shadow border border-border">
        <div>
          <h1 className="text-3xl md:text-4xl font-extrabold gradient-text from-primary to-accent">Community Polls</h1>
          <p className="text-muted-foreground mt-1">Voice your opinion, see what others think, and engage in discussions.</p>
        </div>
        <Button onClick={() => user ? setShowCreatePollDialog(true) : navigate('/login')} className="btn-primary rounded-full px-6 py-3 text-base self-start sm:self-center">
          <PlusCircle size={20} className="mr-2" /> Create Poll
        </Button>
      </div>

      <Tabs defaultValue="trending" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-auto md:inline-flex bg-secondary/50 rounded-lg p-1 mb-6">
          <TabsTrigger value="trending" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
            <TrendingUp size={18} /> Trending
          </TabsTrigger>
          <TabsTrigger value="new" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-md rounded-md px-4 py-2 text-sm font-medium flex items-center justify-center gap-2">
            <Sparkles size={18} /> Newest
          </TabsTrigger>
        </TabsList>

        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {pollsToDisplay.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {pollsToDisplay.map(poll => (
                  <PollCard key={poll.id} poll={poll} onVoteClick={handleVoteFromCard} user={user} />
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-8">No {activeTab} polls right now. Be the first to create one!</p>
            )}
          </motion.div>
        </AnimatePresence>
      </Tabs>
      
      {totalPollsInCurrentTab > visiblePollsCount && (
        <div className="text-center mt-8">
          <Button onClick={() => setVisiblePollsCount(prev => prev + POLLS_PER_PAGE)} className="btn-outline-primary">
            Load More Polls
          </Button>
        </div>
      )}


      <CreatePollDialog 
        isOpen={showCreatePollDialog} 
        onClose={() => setShowCreatePollDialog(false)}
        onCreatePoll={handleCreatePoll}
      />

      {pollForCommentModal && selectedOptionForCommentModal && (
        <CommentAfterVoteDialog
          isOpen={!!pollForCommentModal}
          onClose={() => {
            setPollForCommentModal(null);
            setSelectedOptionForCommentModal(null);
          }}
          pollTopic={pollForCommentModal.topic}
          onSubmitComment={(comment) => submitVoteWithOptionalComment(pollForCommentModal.id, selectedOptionForCommentModal, comment)}
        />
      )}

    </motion.div>
  );
};

export default PollsPage;