
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowLeft, MessageSquare } from 'lucide-react';
import { useBanter } from '@/contexts/BanterContext.jsx';
import { useAuth } from '@/contexts/AuthContext.jsx';
import { useToast } from "@/components/ui/use-toast";
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import BanterPost from '@/components/banter/single-banter/BanterPost.jsx';
import BanterReplyForm from '@/components/banter/single-banter/BanterReplyForm.jsx';
import BanterCommentList from '@/components/banter/single-banter/BanterCommentList.jsx';
import EditBanterDialog from '@/components/banter/EditBanterDialog.jsx';

const SingleBanterPage = () => {
  const { banterId } = useParams();
  const navigate = useNavigate();
  const { 
    getBanterById, 
    addCommentToBanter, 
    updateBanter,
    banters: allBanters 
  } = useBanter();
  const { user } = useAuth();
  const { toast } = useToast();

  const [banter, setBanter] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [replyingTo, setReplyingTo] = useState(null); 

  useEffect(() => {
    const currentBanter = getBanterById(banterId);
    if (currentBanter) {
      setBanter(currentBanter);
    } else {
      toast({ title: "Error", description: "Banter not found.", variant: "destructive" });
      navigate("/banter-room");
    }
  }, [banterId, getBanterById, toast, navigate, allBanters]); 

  const handlePostComment = (commentText, attachedMedia) => {
    addCommentToBanter(banterId, commentText, replyingTo?.parentCommentId || null, attachedMedia); 
    toast({ title: "Reply Posted!", description: "Your reply has been added to the banter." });
    setReplyingTo(null);
  };
  
  const handleUpdateBanter = (updatedData) => {
    updateBanter(banterId, updatedData);
    toast({ title: "Banter Updated!", description: "Your changes have been saved." });
    setIsEditing(false);
  };

  if (!banter) {
    return <div className="flex justify-center items-center min-h-[calc(100vh-200px)]"><p>Loading banter...</p></div>;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-3xl mx-auto space-y-6"
    >
      <Button variant="outline" onClick={() => navigate('/banter-room')} className="mb-4 btn-outline-primary">
        <ArrowLeft size={18} className="mr-2" /> Back to Banter Room
      </Button>

      <BanterPost banter={banter} onEdit={() => setIsEditing(true)} />
      
      <EditBanterDialog
        isOpen={isEditing}
        onClose={() => setIsEditing(false)}
        banter={banter}
        onSubmit={handleUpdateBanter}
      />

      <Card className="modern-card">
        <CardHeader className="modern-card-header">
          <CardTitle className="text-xl text-primary flex items-center">
            <MessageSquare size={24} className="mr-2" /> Drop Your Reply ({banter.comments?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent className="modern-card-content">
          <BanterReplyForm 
            onSubmit={handlePostComment}
            replyingTo={replyingTo}
            onCancelReply={() => setReplyingTo(null)}
          />

          <BanterCommentList 
            banterId={banterId}
            comments={banter.comments || []}
            onReply={setReplyingTo}
          />
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default SingleBanterPage;
