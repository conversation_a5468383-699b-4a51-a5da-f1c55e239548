# LeaderBox Data Migration Strategy

## Overview

This document outlines the strategy for migrating data from localStorage-based storage to PostgreSQL database using Prisma ORM. The migration will handle both development/demo data and any production user data.

## localStorage Data Structure Analysis

### Current localStorage Keys

1. **`leaderboxUser`** - Current authenticated user object
2. **`leaderboxAllUsers`** - Array of all registered users
3. **`leaders`** - Array of political leaders with ratings and followers
4. **`leaderUserRatings`** - Object mapping user-leader ratings
5. **`leaderComments`** - Object with leader comments by leader ID
6. **`userLeaderCommentVotes`** - User votes on leader comments
7. **`polls`** - Array of polls with options and votes
8. **`userPollItemVotes`** - User voting history for polls
9. **`leaderboxBanters`** - Array of social posts/discussions
10. **`leaderboxBanterUserVotes`** - User votes on banter posts
11. **`leaderboxPetitions`** - Array of petition campaigns
12. **`leaderboxGroups`** - Array of user groups and communities
13. **`leaderboxUserProfileLikes`** - User profile like interactions
14. **`leaderboxOnboardingComplete_${userId}`** - Per-user onboarding status

## Migration Approach

### Phase 1: Data Extraction
- Create Node.js scripts to read localStorage data from browser
- Export data to JSON files for processing
- Validate data structure and identify inconsistencies

### Phase 2: Data Transformation
- Convert localStorage format to database schema format
- Generate UUIDs for all entities
- Establish proper foreign key relationships
- Handle data type conversions (strings to numbers, dates, etc.)

### Phase 3: Data Import
- Use Prisma Client to insert transformed data
- Handle referential integrity constraints
- Implement transaction-based imports for data consistency
- Create rollback procedures for failed migrations

## Data Transformation Rules

### Users (`leaderboxUser` + `leaderboxAllUsers`)
```javascript
// localStorage format
{
  id: 'userDemo123',
  email: '<EMAIL>',
  name: 'Demo User',
  state: 'Lagos',
  lga: 'Ikeja',
  gender: 'Male',
  party: 'Neutral',
  isLoggedIn: true,
  isAdmin: false,
  role: 'User',
  status: 'active',
  avatarUrl: '',
  followedLeaders: ['1', '3'],
  createdAt: '2023-12-01T10:00:00.000Z',
  petitionsCreated: 12,
  profileLikesCount: 1500,
  groupMembersCount: 50
}

// Database format (Prisma)
{
  id: uuid(),
  email: '<EMAIL>',
  name: 'Demo User',
  state: 'Lagos',
  lga: 'Ikeja',
  gender: 'Male',
  party: 'Neutral',
  role: 'User',
  status: 'active',
  avatarUrl: null,
  isAdmin: false,
  onboardingComplete: true, // derived from localStorage key
  petitionsCreated: 12,
  profileLikesCount: 1500,
  groupMembersCount: 50,
  createdAt: new Date('2023-12-01T10:00:00.000Z'),
  updatedAt: new Date()
}
```

### Leaders (`leaders`)
```javascript
// localStorage format
{
  id: '1',
  name: 'Bola Ahmed Tinubu',
  position: 'President',
  party: 'APC',
  state: 'Lagos',
  bio: 'Current President of Nigeria...',
  detailedBio: 'Detailed biography...',
  avatarUrl: 'https://example.com/avatar.jpg',
  followers: 1250,
  isFollowed: false,
  rating: 3.8,
  ratingCount: 450
}

// Database format
{
  id: uuid(),
  name: 'Bola Ahmed Tinubu',
  position: 'President',
  party: 'APC',
  state: 'Lagos',
  bio: 'Current President of Nigeria...',
  detailedBio: 'Detailed biography...',
  avatarUrl: 'https://example.com/avatar.jpg',
  followersCount: 1250,
  ratingAverage: 3.8,
  ratingCount: 450,
  createdAt: new Date(),
  updatedAt: new Date()
}
```

### Polls (`polls`)
```javascript
// localStorage format
{
  id: 'poll1',
  topic: 'Should Nigeria invest more in renewable energy?',
  description: 'Considering Nigeria\'s current energy crisis...',
  options: [
    { id: 'opt1a', text: 'Renewable Energy', votes: 75 },
    { id: 'opt1b', text: 'Fossil Fuels', votes: 40 }
  ],
  source: 'Admin',
  createdAt: '2023-12-01T10:00:00.000Z',
  totalVotes: 115,
  upvotes: 25,
  downvotes: 3,
  commentsCount: 12,
  userVotes: { 'userDemo123': 'opt1a' },
  comments: [...]
}

// Database format
Poll: {
  id: uuid(),
  topic: 'Should Nigeria invest more in renewable energy?',
  description: 'Considering Nigeria\'s current energy crisis...',
  creatorId: adminUserId, // lookup from source
  totalVotes: 115,
  upvotes: 25,
  downvotes: 3,
  commentsCount: 12,
  media: [],
  createdAt: new Date('2023-12-01T10:00:00.000Z'),
  updatedAt: new Date()
}

PollOptions: [
  {
    id: uuid(),
    pollId: pollId,
    text: 'Renewable Energy',
    votes: 75,
    optionOrder: 0
  },
  {
    id: uuid(),
    pollId: pollId,
    text: 'Fossil Fuels',
    votes: 40,
    optionOrder: 1
  }
]

PollVotes: [
  {
    id: uuid(),
    userId: userUuid,
    pollId: pollUuid,
    optionId: optionUuid,
    createdAt: new Date()
  }
]
```

## Migration Scripts Structure

### 1. Data Extraction Script (`extract-data.js`)
```javascript
// Extracts localStorage data from browser and saves to JSON files
const extractLocalStorageData = () => {
  const data = {
    users: JSON.parse(localStorage.getItem('leaderboxAllUsers') || '[]'),
    currentUser: JSON.parse(localStorage.getItem('leaderboxUser') || 'null'),
    leaders: JSON.parse(localStorage.getItem('leaders') || '[]'),
    polls: JSON.parse(localStorage.getItem('polls') || '[]'),
    banters: JSON.parse(localStorage.getItem('leaderboxBanters') || '[]'),
    petitions: JSON.parse(localStorage.getItem('leaderboxPetitions') || '[]'),
    groups: JSON.parse(localStorage.getItem('leaderboxGroups') || '[]'),
    // ... other data
  };
  
  // Save to files
  fs.writeFileSync('migration-data.json', JSON.stringify(data, null, 2));
};
```

### 2. Data Transformation Script (`transform-data.js`)
```javascript
const transformUsers = (localStorageUsers) => {
  return localStorageUsers.map(user => ({
    id: generateUUID(),
    email: user.email,
    name: user.name,
    state: user.state,
    lga: user.lga,
    gender: user.gender,
    party: user.party,
    role: user.role || 'User',
    status: user.status || 'active',
    avatarUrl: user.avatarUrl || null,
    isAdmin: user.isAdmin || false,
    onboardingComplete: checkOnboardingStatus(user.id),
    petitionsCreated: user.petitionsCreated || 0,
    profileLikesCount: user.profileLikesCount || 0,
    groupMembersCount: user.groupMembersCount || 0,
    createdAt: new Date(user.createdAt || Date.now()),
    updatedAt: new Date()
  }));
};
```

### 3. Data Import Script (`import-data.js`)
```javascript
const importData = async (transformedData) => {
  const prisma = new PrismaClient();
  
  try {
    await prisma.$transaction(async (tx) => {
      // Import users first
      await tx.user.createMany({
        data: transformedData.users
      });
      
      // Import leaders
      await tx.leader.createMany({
        data: transformedData.leaders
      });
      
      // Import relationships
      await tx.userLeaderFollow.createMany({
        data: transformedData.userLeaderFollows
      });
      
      // ... continue with other entities
    });
    
    console.log('Data migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
};
```

## Data Validation Rules

### Pre-Migration Validation
- Verify all required fields are present
- Check data type consistency
- Validate email formats and uniqueness
- Ensure referential integrity can be maintained
- Check for duplicate IDs or conflicting data

### Post-Migration Validation
- Verify record counts match expected numbers
- Check foreign key relationships are properly established
- Validate data integrity constraints
- Test sample queries to ensure data accessibility
- Compare sample data between old and new systems

## Rollback Strategy

### Backup Procedures
1. Create full database backup before migration
2. Export current localStorage data as JSON backup
3. Document all transformation rules and mappings
4. Create rollback scripts for each migration step

### Rollback Process
1. Stop application services
2. Restore database from backup
3. Clear migrated data if partial migration occurred
4. Restart services with original localStorage system
5. Investigate and fix migration issues
6. Re-attempt migration after fixes

## Testing Strategy

### Unit Tests
- Test individual transformation functions
- Validate data type conversions
- Test UUID generation and mapping
- Verify relationship establishment

### Integration Tests
- Test complete migration pipeline
- Verify data integrity after migration
- Test rollback procedures
- Validate application functionality with migrated data

### Performance Tests
- Measure migration time for different data volumes
- Test database performance with migrated data
- Verify query performance meets requirements

## Migration Execution Plan

### Pre-Migration Checklist
- [ ] Database backup completed
- [ ] Migration scripts tested in development
- [ ] Rollback procedures verified
- [ ] Stakeholders notified of migration window
- [ ] Monitoring systems prepared

### Migration Steps
1. **Preparation** (30 minutes)
   - Stop application services
   - Create database backup
   - Verify backup integrity

2. **Data Extraction** (15 minutes)
   - Extract localStorage data
   - Validate extracted data
   - Save extraction logs

3. **Data Transformation** (30 minutes)
   - Transform data to database format
   - Validate transformed data
   - Generate mapping files

4. **Data Import** (45 minutes)
   - Import data using Prisma transactions
   - Verify import success
   - Run post-migration validation

5. **Application Update** (30 minutes)
   - Deploy new SvelteKit application
   - Update configuration
   - Start services

6. **Verification** (30 minutes)
   - Test critical user flows
   - Verify data accessibility
   - Monitor for errors

### Post-Migration Tasks
- Monitor application performance
- Address any immediate issues
- Collect user feedback
- Document lessons learned
- Plan for future migrations

## Risk Mitigation

### Data Loss Prevention
- Multiple backup strategies
- Transaction-based imports
- Comprehensive validation
- Rollback procedures

### Performance Issues
- Batch processing for large datasets
- Database optimization
- Connection pooling
- Query performance monitoring

### User Disruption
- Scheduled maintenance windows
- Clear communication
- Quick rollback capability
- User support preparation

---

*This migration strategy ensures a safe, reliable transition from localStorage to database persistence while maintaining data integrity and minimizing user disruption.*
