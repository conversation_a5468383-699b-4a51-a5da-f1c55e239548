# LeaderBox Migration - Updated Project Requirements Document (PRD)

## Executive Summary

This document outlines the updated migration timeline for LeaderBox from React + localStorage to SvelteKit + Prisma + PostgreSQL. Phase 1 (authentication foundation) is complete, and we're now implementing Phase 1B (core UI components and admin features) before proceeding to Phase 2A (advanced features).

## 1. Technical Architecture

### 1.1 Hybrid Approach Strategy
- **API Routes (80%)**: RESTful endpoints for data operations, user interactions, and admin functions
- **SSR (20%)**: Server-side rendering for SEO-critical pages and initial data loading
- **Authentication**: <PERSON> Auth with session-based authentication
- **Database**: PostgreSQL with Prisma ORM
- **Frontend**: SvelteKit with TypeScript

### 1.2 Technology Stack
- **Backend**: SvelteKit API routes, Prisma ORM, PostgreSQL
- **Authentication**: Custom session management based on <PERSON> Auth best practices
- **Frontend**: Svelte 5, TypeScript, Tailwind CSS
- **Testing**: <PERSON><PERSON><PERSON>, Playwright
- **Deployment**: PostgreSQL + SvelteKit deployment

## 2. Feature Specifications

### 2.1 Authentication System (Priority 1)

#### User Stories
- **AS A** new user **I WANT TO** register with email/password **SO THAT** I can access the platform
- **AS A** registered user **I WANT TO** login securely **SO THAT** I can access my account
- **AS A** logged-in user **I WANT TO** logout **SO THAT** my session is terminated securely
- **AS A** user **I WANT TO** update my profile **SO THAT** my information stays current

#### API Endpoints
```typescript
POST /api/auth/register
Request: { email: string, password: string, name: string, state?: string, lga?: string, gender?: string, party?: string }
Response: { success: boolean, user?: User, error?: string }

POST /api/auth/login  
Request: { email: string, password: string }
Response: { success: boolean, user?: User, error?: string }

POST /api/auth/logout
Request: {}
Response: { success: boolean }

GET /api/auth/validate
Response: { user: User | null, session: Session | null }

PATCH /api/auth/profile
Request: Partial<User>
Response: { success: boolean, user?: User, error?: string }
```

#### Acceptance Criteria
- [ ] User registration with email validation
- [ ] Secure password hashing (handled by Lucia)
- [ ] Session management with secure cookies
- [ ] Profile update functionality
- [ ] Proper error handling and validation
- [ ] Rate limiting on auth endpoints

### 2.2 User Management System (Priority 1)

#### User Stories
- **AS AN** admin **I WANT TO** manage user accounts **SO THAT** I can maintain platform quality
- **AS A** user **I WANT TO** view other user profiles **SO THAT** I can learn about community members
- **AS A** user **I WANT TO** follow other users **SO THAT** I can stay updated on their activities

#### API Endpoints
```typescript
GET /api/users
Response: { users: User[], pagination: PaginationInfo }

GET /api/users/[id]
Response: { user: User, stats: UserStats }

PATCH /api/users/[id]
Request: Partial<User>
Response: { success: boolean, user?: User, error?: string }

POST /api/users/[id]/follow
Response: { success: boolean, isFollowing: boolean }

DELETE /api/users/[id]/follow
Response: { success: boolean, isFollowing: boolean }
```

### 2.3 Leader Management System (Priority 2)

#### User Stories
- **AS A** user **I WANT TO** view leader profiles **SO THAT** I can learn about political figures
- **AS A** user **I WANT TO** rate leaders **SO THAT** I can express my opinion
- **AS A** user **I WANT TO** comment on leaders **SO THAT** I can share detailed feedback
- **AS A** user **I WANT TO** follow leaders **SO THAT** I can track their activities

#### API Endpoints
```typescript
GET /api/leaders
Response: { leaders: Leader[], pagination: PaginationInfo }

GET /api/leaders/[id]
Response: { leader: Leader, ratings: Rating[], comments: Comment[] }

POST /api/leaders/[id]/rate
Request: { rating: number }
Response: { success: boolean, newAverage: number }

POST /api/leaders/[id]/comments
Request: { text: string, rating?: number, media?: string[] }
Response: { success: boolean, comment: Comment }

POST /api/leaders/[id]/follow
Response: { success: boolean, isFollowing: boolean }
```

### 2.4 Polling System (Priority 2)

#### User Stories
- **AS A** user **I WANT TO** view polls **SO THAT** I can participate in political discussions
- **AS A** user **I WANT TO** vote on polls **SO THAT** I can express my opinion
- **AS A** user **I WANT TO** create polls **SO THAT** I can gather community opinions
- **AS A** user **I WANT TO** comment on polls **SO THAT** I can discuss issues

#### API Endpoints
```typescript
GET /api/polls
Response: { polls: Poll[], pagination: PaginationInfo }

GET /api/polls/[id]
Response: { poll: Poll, options: PollOption[], userVote?: PollVote }

POST /api/polls
Request: { topic: string, description?: string, options: string[], media?: string[] }
Response: { success: boolean, poll: Poll }

POST /api/polls/[id]/vote
Request: { optionId: string }
Response: { success: boolean, results: PollResults }

POST /api/polls/[id]/comments
Request: { text: string, media?: string[] }
Response: { success: boolean, comment: PollComment }
```

## 3. Database Schema Integration

### 3.1 Session Management Tables
```sql
-- Session table for custom auth implementation
CREATE TABLE sessions (
    id TEXT NOT NULL PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id),
    secret_hash BYTEA NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add to existing users table
ALTER TABLE users ADD COLUMN password_hash TEXT;
```

### 3.2 Migration Strategy
- Extend existing Prisma schema for custom session management
- Maintain existing relationships and constraints
- Add session management tables with proper security
- Preserve data integrity during migration

## 4. Updated Implementation Timeline

### ✅ Phase 1: Authentication Foundation (COMPLETED)
- [x] Custom session management implementation
- [x] Authentication API routes (login, register, logout, validate, profile)
- [x] Environment variable validation with Zod
- [x] Zeptomail email integration
- [x] Password reset functionality
- [x] Frontend authentication pages (login, register, forgot password, reset password)

### 🔄 Phase 1B: Core UI & Admin Features (IN PROGRESS)
**Priority Order:**
1. Homepage migration with exact design fidelity
2. Navigation bar component with auth state integration
3. Footer component migration
4. Admin panel foundation and routing
5. Leaders management (Admin CRUD operations)
6. Leader view page (public profile)
7. User dashboard with activity feed

### 📋 Phase 2A: Advanced Features (PLANNED)
- User management APIs and social features
- Polling system implementation
- Banter rooms and petition functionality
- Advanced search and filtering
- Real-time notifications

### 📋 Phase 2B: Data Migration & Production (PLANNED)
- Complete data migration from localStorage
- Comprehensive testing suite
- Performance optimization
- Production deployment

## 5. Success Metrics

### 5.1 Technical Metrics
- [ ] API response time < 200ms average
- [ ] Database query time < 100ms average
- [ ] Test coverage > 90% for API routes
- [ ] Zero critical security vulnerabilities

### 5.2 Functional Metrics
- [ ] 100% feature parity with React version
- [ ] Successful data migration with zero data loss
- [ ] All user workflows functional
- [ ] Admin dashboard fully operational

## 6. Risk Mitigation

### 6.1 Technical Risks
- **Authentication Integration**: Thorough testing of Lucia Auth setup
- **Data Migration**: Comprehensive backup and rollback procedures
- **Performance**: Load testing and optimization
- **Security**: Security audit of all endpoints

### 6.2 Mitigation Strategies
- Incremental implementation with feature flags
- Comprehensive testing at each stage
- Regular backup procedures
- Security-first development approach

## 7. Testing Requirements

### 7.1 Unit Tests
- All API route handlers
- Database utility functions
- Authentication middleware
- Validation functions

### 7.2 Integration Tests
- Complete user workflows
- Database operations
- Authentication flows
- API endpoint interactions

### 7.3 End-to-End Tests
- User registration and login
- Leader rating and commenting
- Poll creation and voting
- Admin operations

## Next Steps

1. **Phase 2A**: Implement core authentication and user management
2. **Phase 2B**: Build leader and polling systems
3. **Phase 2C**: Complete frontend migration and testing
4. **Phase 2D**: Data migration and production deployment

---

*This PRD serves as the definitive guide for Phase 2 implementation, ensuring all stakeholders understand requirements, timelines, and success criteria.*
