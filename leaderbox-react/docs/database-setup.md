# LeaderBox Database Setup Guide

## PostgreSQL Setup for Development

### Option 1: Local PostgreSQL Installation

#### Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS with Homebrew
brew install postgresql
brew services start postgresql

# Windows
# Download and install from https://www.postgresql.org/download/windows/
```

#### Create Database and User
```bash
# Connect to PostgreSQL as superuser
sudo -u postgres psql

# Create database and user
CREATE DATABASE leaderbox_dev;
CREATE USER leaderbox_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE leaderbox_dev TO leaderbox_user;

# Exit PostgreSQL
\q
```

#### Update Environment Variables
Update your `.env` file with the correct database credentials:
```env
DATABASE_URL="postgresql://leaderbox_user:your_secure_password@localhost:5432/leaderbox_dev"
DIRECT_URL="postgresql://leaderbox_user:your_secure_password@localhost:5432/leaderbox_dev"
```

### Option 2: Docker PostgreSQL

#### Create docker-compose.yml
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: leaderbox_dev
      POSTGRES_USER: leaderbox_user
      POSTGRES_PASSWORD: your_secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

#### Start PostgreSQL
```bash
docker-compose up -d
```

### Option 3: Cloud PostgreSQL

#### Popular Cloud Providers
- **Neon**: https://neon.tech (Free tier available)
- **Supabase**: https://supabase.com (Free tier available)
- **Railway**: https://railway.app (Free tier available)
- **PlanetScale**: https://planetscale.com (MySQL, but compatible)

#### Setup Steps
1. Create account with chosen provider
2. Create new PostgreSQL database
3. Copy connection string
4. Update `.env` file with connection string

## Database Migration

### Initialize Database Schema
```bash
# Navigate to SvelteKit project
cd leaderbox-sveltekit

# Generate Prisma client
pnpm db:generate

# Push schema to database (for development)
pnpm db:push

# Or create and run migrations (for production)
pnpm db:migrate
```

### Verify Database Connection
```bash
# Test database connection
pnpm prisma studio
```

This will open Prisma Studio in your browser where you can view and manage your database.

## Testing Authentication

### Test API Endpoints
Once the database is set up, you can test the authentication endpoints:

```bash
# Start development server
pnpm dev

# Test registration (in another terminal)
curl -X POST http://localhost:5173/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "name": "Test User"
  }'

# Test login
curl -X POST http://localhost:5173/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }'
```

## Troubleshooting

### Common Issues

#### Connection Refused
- Ensure PostgreSQL is running
- Check if port 5432 is available
- Verify connection string format

#### Authentication Failed
- Check username and password in connection string
- Ensure user has proper permissions
- Verify database exists

#### Schema Sync Issues
```bash
# Reset database (WARNING: This will delete all data)
pnpm prisma db push --force-reset

# Or create a new migration
pnpm prisma migrate dev --name init
```

### Environment Variables Check
```bash
# Verify environment variables are loaded
node -e "console.log(process.env.DATABASE_URL)"
```

## Next Steps

After successful database setup:
1. Test authentication API endpoints
2. Create user management APIs
3. Implement frontend authentication flows
4. Set up data migration from localStorage

---

**Note**: Always use strong passwords and secure connection strings in production environments.
