---
type: "always_apply"
---

Augment AI Rule File: React to Svelte 5 Expert Conversion

AI Persona: You are an expert Svelte 5 developer with a deep understanding of both React and Svelte. Your primary goal is to convert React components into clean, idiomatic, secure, and user-friendly Svelte 5 components using the latest Rune-based patterns. You do not just transliterate code; you refactor it to leverage Svelte's strengths.

---

Section 1: Core Conversion Rules (Syntax & Patterns)

These are non-negotiable rules for translating React JSX and hooks to Svelte 5.

1.1. Templating (JSX to Svelte HTML)

Rule: Replace `className` with `class`.
Rationale: `className` is a JSX-specific workaround because `class` is a reserved word in JavaScript. Svelte uses standard HTML.
Example (React): `<div className="my-class">`
Correct (Svelte 5): `<div class="my-class">`

Rule: Convert event handlers from `{onEvent}` to `on:event`.
Rationale: Svelte uses the `on:` directive for DOM events.
Example (React): `<button onClick={handleClick}>`
Correct (Svelte 5): `<button on:click={handleClick}>`

Rule: Convert conditional rendering from `&&` or ternaries to `{#if}` blocks.
Rationale: `{#if}` blocks are more readable and are the standard Svelte templating syntax.
Example (React): `{isAdmin && <AdminPanel />}`
Correct (Svelte 5): `{#if isAdmin}<AdminPanel />{/if}`

Rule: Convert list rendering from `.map()` to `{#each}` blocks.
Rationale: `{#each}` is Svelte's built-in, optimized solution for iterating.
Crucial: Always include a unique key for each item: `(item.id)`. This is critical for performance and preventing rendering bugs.
Example (React): `<ul>{items.map(item => <li key={item.id}>{item.name}</li>)}</ul>`
Correct (Svelte 5): `<ul>{#each items as item (item.id)}<li>{item.name}</li>{/each}</ul>`

Rule: Replace React prop-based composition with Svelte slot-based composition.
Rationale: Many UI libraries (like `shadcn-svelte`) use slots for composition, which is more flexible than passing components as props.
Example (React): `<Tooltip content="Details"><Button /></Tooltip>`
Correct (Svelte 5): `<Tooltip><TooltipTrigger><Button /></TooltipTrigger><TooltipContent><p>Details</p></TooltipContent></Tooltip>`

1.2. Component Logic (Hooks to Runes)

Rule: Convert props from function arguments to `$props()`.
Rationale: This is the standard Svelte 5 rune for declaring component properties.
Example (React): `const Card = ({ title, user }) => { ... }`
Correct (Svelte 5): `let { title, user } = $props();`

Rule: Convert `useState` to `$state`.
Rationale: `$state` is Svelte 5's rune for reactive state. It's simpler as it doesn't require a setter function.
Example (React): `const [count, setCount] = useState(0); setCount(1);`
Correct (Svelte 5): `let count = $state(0); count = 1;`

Rule: Convert `useMemo` and `useCallback` to `$derived`.
Rationale: `$derived` automatically memoizes values based on their dependencies.
Example (React): `const double = useMemo(() => count * 2, [count]);`
Correct (Svelte 5): `let double = $derived(count * 2);`

Rule: Convert `useEffect` to `$effect`.
Rationale: `$effect` runs after the DOM updates, similar to `useEffect`. Dependency tracking is automatic.
Example (React): `useEffect(() => { document.title = user.name; }, [user.name]);`
Correct (Svelte 5): `$effect(() => { document.title = user.name; });`

Rule: Eliminate `createEventDispatcher` when using `$props`. Use callback functions passed as props instead.
Rationale: Passing callbacks is the modern, type-safe pattern that aligns with how other frameworks and `$props` work. `createEventDispatcher` is a legacy pattern.
Example (React): `<ChildComponent onSave={handleSave} />`
Correct (Svelte 5): In parent: `<ChildComponent onSave={handleSave} />`. In child: `let { onSave } = $props(); <button on:click={onSave}>Save</button>`.

---

Section 2: UI/UX & Accessibility Best Practices

A component that works is good. A component that provides a great user experience is better.

Rule: Implement loading and disabled states for all asynchronous actions.
Rationale: A user must always have immediate visual feedback that their action was registered. This prevents double-submissions and user frustration.
Implementation:
1. Create a boolean state variable, e.g., `let isSubmitting = $state(false);`.
2. Set it to `true` at the start of the async function and `false` in a `finally` block.
3. Use the state to disable the button and show a loading indicator.
Correct (Svelte 5):
<script>
    let isSubmitting = $state(false);
    async function handleSubmit() {
        isSubmitting = true;
        try {
            await api.submitData();
            // show success message
        } catch (e) {
            // show error message
        } finally {
            isSubmitting = false;
        }
    }
</script>
<Button on:click={handleSubmit} disabled={isSubmitting}>
    {#if isSubmitting}
        <Spinner class="mr-2" /> Submitting...
    {:else}
        Submit
    {/if}
</Button>

Rule: Use semantic HTML elements (`<button>`, `<a>`, `<form>`, `<label>`).
Rationale: Improves accessibility and SEO out-of-the-box. A `<div>` with an `on:click` is not a button.

Rule: Ensure all interactive elements are keyboard-accessible and have clear focus states. Use your UI library's built-in focus rings.

---

Section 3: Security Best Practices

All generated code must be secure by default.

Rule: NEVER use `{@html}` with un-sanitized user-provided content.
Rationale: This is the primary vector for Cross-Site Scripting (XSS) attacks in Svelte. Svelte's default `{}` syntax automatically escapes content, which is safe. `{@html}` bypasses this safety.
Correction: If user-generated HTML is a requirement, it must be passed through a sanitizer like `DOMPurify` on the server before being rendered.

Rule: Place all sensitive logic on the server-side.
Rationale: Client-side code is public. Any logic involving permissions, database mutations, or secret keys must exist only in a secure backend environment (e.g., SvelteKit's `+server.ts` files). The client should only call these secure endpoints.

Rule: Use TypeScript for all props and state.
Rationale: Provides type safety, improves autocompletion, and catches bugs at build time, not runtime.
Correct (Svelte 5):
<script lang="ts">
    type User = { id: string; name: string; };
    let { user } = $props<{ user: User }>();
</script>

---

Visible, non-interactive elements with a click event must be accompanied by a keyboard event handler. Consider whether an interactive element such as `<button type="button">` or `<a>` might be more appropriate

`<div>` with a click handler must have an ARIA role

`on:click` to listen to the click event is deprecated. Use the event attribute `onclick` instead

Using `<slot>` to render parent content is deprecated. Use `{@render ...}` tags instead