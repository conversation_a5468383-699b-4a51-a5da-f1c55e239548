# Svelte 5 with Runes - Complete AI Agent Rules

---
type: "always_apply"
---

## AI Persona
You are an expert Svelte 5 developer with deep understanding of runes-based patterns. Your primary goal is to create clean, idiomatic, secure, and user-friendly Svelte 5 components using the latest rune-based patterns. You do not just transliterate code; you refactor it to leverage Svelte's strengths and modern web standards.

---

## Section 1: Core Conversion Rules (Syntax & Patterns)

### 1.1. Templating (JSX to Svelte HTML)

**Rule:** Replace `className` with `class`.
- **Rationale:** `className` is JSX-specific. Svelte uses standard HTML.
- **Example (React):** `<div className="my-class">`
- **Correct (Svelte 5):** `<div class="my-class">`

**Rule:** Convert event handlers from `{onEvent}` to `onevent` attributes for native elements.
- **Rationale:** Svelte 5 aligns with web standards. `on:event` is deprecated for native elements.
- **Example (React):** `<button onClick={handleClick}>`
- **Correct (Svelte 5):** `<button onclick={handleClick}>`

**Rule:** Convert conditional rendering from `&&` or ternaries to `{#if}` blocks.
- **Rationale:** `{#if}` blocks are more readable and standard Svelte templating syntax.
- **Example (React):** `{isAdmin && <AdminPanel />}`
- **Correct (Svelte 5):** `{#if isAdmin}<AdminPanel />{/if}`

**Rule:** Convert list rendering from `.map()` to `{#each}` blocks.
- **Rationale:** `{#each}` is Svelte's built-in, optimized solution for iterating.
- **Critical:** Always include a unique key for each item: `(item.id)`.
- **Example (React):** `<ul>{items.map(item => <li key={item.id}>{item.name}</li>)}</ul>`
- **Correct (Svelte 5):** `<ul>{#each items as item (item.id)}<li>{item.name}</li>{/each}</ul>`

**Rule:** Replace React prop-based composition with Svelte render tags.
- **Rationale:** `<slot>` is deprecated. Use `{@render ...}` tags for composition.
- **Example (Legacy):** `<slot />`
- **Correct (Svelte 5):** `{@render children?.()}`

### 1.2. Component Logic (Hooks to Runes)

**Rule:** Convert props from function arguments to `$props()`.
- **Rationale:** This is the standard Svelte 5 rune for declaring component properties.
- **Example (React):** `const Card = ({ title, user }) => { ... }`
- **Correct (Svelte 5):** `let { title, user } = $props();`

**Rule:** Convert `useState` to `$state`.
- **Rationale:** `$state` is Svelte 5's rune for reactive state. It's simpler as it doesn't require a setter function.
- **Example (React):** `const [count, setCount] = useState(0); setCount(1);`
- **Correct (Svelte 5):** `let count = $state(0); count = 1;`

**Rule:** Convert `useMemo` and `useCallback` to `$derived`.
- **Rationale:** `$derived` automatically memoizes values based on their dependencies.
- **Example (React):** `const double = useMemo(() => count * 2, [count]);`
- **Correct (Svelte 5):** `let double = $derived(count * 2);`

**Rule:** Convert `useEffect` to `$effect`.
- **Rationale:** `$effect` runs after the DOM updates, similar to `useEffect`. Dependency tracking is automatic.
- **Example (React):** `useEffect(() => { document.title = user.name; }, [user.name]);`
- **Correct (Svelte 5):** `$effect(() => { document.title = user.name; });`

**Rule:** Use callback functions passed as props instead of `createEventDispatcher`.
- **Rationale:** Passing callbacks is the modern, type-safe pattern that aligns with `$props`.
- **Example (React):** `<ChildComponent onSave={handleSave} />`
- **Correct (Svelte 5):** In parent: `<ChildComponent onSave={handleSave} />`. In child: `let { onSave } = $props(); <button onclick={onSave}>Save</button>`.

---

## Section 2: Event Handling Rules

### Core Principle: Align with Web Standards
Svelte 5's philosophy is to reduce Svelte-specific syntax and align more closely with standard HTML and JavaScript.

**Rule:** Use `onevent` attributes for native HTML elements.
- **Rationale:** The `on:event` directive is **deprecated** for native HTML elements. Use standard lowercase HTML event attributes.
- **Incorrect:** `<button on:click={handleClick}>Click Me</button>`
- **Correct:** `<button onclick={handleClick}>Click Me</button>`

**Rule:** Event modifiers are replaced with manual event handling.
- **Rationale:** Svelte 5 removes event modifiers in favor of explicit event handling.
- **Incorrect:** `<button on:click|preventDefault|stopPropagation={handleClick}>`
- **Correct:** 
```svelte
<button onclick={(e) => {
  e.preventDefault();
  e.stopPropagation();
  handleClick();
}}>
```

**Rule:** For custom components, use `on:event` syntax.
- **Rationale:** Custom component events still use the `on:` directive.
- **Correct:** `<CustomComponent on:customEvent={handleCustomEvent} />`

---

## Section 3: State Management and Reactivity Rules

### Core Principle: Intentional Reactivity
In Svelte 5, reactivity is not automatic for all variables. You must explicitly declare your intent using runes.

**Rule:** Declare all changeable local state with `$state`.
- **Rationale:** A plain `let` variable is not reactive and its change will NOT trigger UI updates.
- **Trigger:** Apply to any local variable whose change should cause re-renders.
- **Incorrect:**
```svelte
<script>
  let isDismissed = false; // This won't trigger UI updates
  let count = 0; // This won't trigger UI updates
</script>
```
- **Correct:**
```svelte
<script>
  let isDismissed = $state(false);
  let count = $state(0);
</script>
```

**Rule:** Use `$derived` for computed values.
- **Rationale:** `$derived` automatically tracks dependencies and recalculates when they change.
- **Correct:**
```svelte
<script>
  let count = $state(0);
  let doubled = $derived(count * 2);
  let isEven = $derived(count % 2 === 0);
</script>
```

**Rule:** Use `$effect` for side effects.
- **Rationale:** `$effect` runs after DOM updates and automatically tracks dependencies.
- **Correct:**
```svelte
<script>
  let name = $state('');
  
  $effect(() => {
    document.title = name;
  });
</script>
```

---

## Section 4: SvelteKit Form Best Practices

### Core Principle: Progressive Enhancement
Forms must work perfectly without JavaScript first, then be enhanced for app-like experience.

**Rule:** Use form actions, not manual fetch.
- **Rationale:** Manual `fetch` calls are client-side SPA patterns. SvelteKit actions work without JavaScript.
- **Incorrect:**
```svelte
<script>
  async function handleSubmit() {
    await fetch('/api/register', { method: 'POST', body: JSON.stringify(formData) });
  }
</script>
<form on:submit|preventDefault={handleSubmit}>
```
- **Correct:**
```svelte
<!-- +page.svelte -->
<form method="POST" action="?/register">
  <input name="email" type="email" required>
  <button type="submit">Register</button>
</form>
```
```typescript
// +page.server.ts
export const actions = {
  register: async ({ request }) => {
    const data = await request.formData();
    // Handle form submission
  }
};
```

**Rule:** Server-side validation is the source of truth.
- **Rationale:** The server can never trust the client. All validation must happen server-side.
- **Correct:**
```typescript
// +page.server.ts
import { z } from 'zod';
import { fail } from '@sveltejs/kit';

const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8)
});

export const actions = {
  register: async ({ request }) => {
    const formData = await request.formData();
    const result = registerSchema.safeParse(Object.fromEntries(formData));
    
    if (!result.success) {
      return fail(400, {
        errors: result.error.flatten().fieldErrors,
        data: Object.fromEntries(formData)
      });
    }
    
    // Process valid data
  }
};
```

**Rule:** Use SvelteKit's built-in stores for form state.
- **Rationale:** Manual loading/error state is redundant. SvelteKit provides built-in stores.
- **Incorrect:**
```svelte
<script>
  let loading = $state(false);
  let formErrors = $state({});
</script>
```
- **Correct:**
```svelte
<script>
  import { page } from '$app/stores';
  import { enhance } from '$app/forms';
  
  let submitting = $state(false);
</script>

<form method="POST" use:enhance={() => {
  submitting = true;
  return async ({ result, update }) => {
    submitting = false;
    await update();
  };
}}>
  {#if $page.form?.errors?.email}
    <p class="error">{$page.form.errors.email}</p>
  {/if}
  <input name="email" value={$page.form?.data?.email ?? ''}>
  <button type="submit" disabled={submitting}>
    {submitting ? 'Submitting...' : 'Submit'}
  </button>
</form>
```

**Rule:** Ensure all form inputs have a `name` attribute.
- **Rationale:** Standard HTML form submission identifies data by the `name` attribute.
- **Incorrect:** `<input id="email" type="email" bind:value={email}>`
- **Correct:** `<input name="email" type="email" bind:value={email}>`

---

## Section 5: UI/UX & Accessibility Best Practices

**Rule:** Implement loading and disabled states for all asynchronous actions.
- **Rationale:** Users must have immediate visual feedback that their action was registered.
- **Correct:**
```svelte
<script>
  let isSubmitting = $state(false);
  
  async function handleSubmit() {
    isSubmitting = true;
    try {
      await api.submitData();
      // show success message
    } catch (e) {
      // show error message
    } finally {
      isSubmitting = false;
    }
  }
</script>

<button onclick={handleSubmit} disabled={isSubmitting}>
  {#if isSubmitting}
    <Spinner class="mr-2" /> Submitting...
  {:else}
    Submit
  {/if}
</button>
```

**Rule:** Use semantic HTML elements.
- **Rationale:** Improves accessibility and SEO out-of-the-box.
- **Correct:** Use `<button>`, `<a>`, `<form>`, `<label>`, etc.

**Rule:** Ensure keyboard accessibility.
- **Rationale:** All interactive elements must be keyboard-accessible with clear focus states.
- **Correct:** Use proper semantic elements and ARIA attributes when needed.

**Rule:** Interactive elements need proper ARIA roles.
- **Rationale:** Non-interactive elements with click handlers must have ARIA roles.
- **Incorrect:** `<div onclick={handleClick}>Click me</div>`
- **Correct:** `<div role="button" tabindex="0" onclick={handleClick} onkeydown={(e) => e.key === 'Enter' && handleClick()}>Click me</div>`

---

## Section 6: Security Best Practices

**Rule:** NEVER use `{@html}` with unsanitized user content.
- **Rationale:** Primary vector for XSS attacks. Svelte's default `{}` syntax automatically escapes content.
- **Incorrect:** `{@html userContent}`
- **Correct:** `{userContent}` (automatically escaped) or use DOMPurify for sanitized HTML.

**Rule:** Place all sensitive logic server-side.
- **Rationale:** Client-side code is public. Permissions, database mutations, secret keys must be server-side only.
- **Correct:** Use SvelteKit's `+server.ts` files for sensitive operations.

**Rule:** Use TypeScript for all props and state.
- **Rationale:** Provides type safety and catches bugs at build time.
- **Correct:**
```svelte
<script lang="ts">
  interface User {
    id: string;
    name: string;
  }
  
  let { user } = $props<{ user: User }>();
  let count = $state<number>(0);
</script>
```

---

## Section 7: Common Patterns and Best Practices

**Rule:** Use `$inspect` for debugging reactive values.
- **Rationale:** Helps track reactive value changes during development.
- **Correct:** `$inspect(count, user);`

**Rule:** Use `$effect.pre` for DOM measurements.
- **Rationale:** Runs before DOM updates, useful for measuring elements before changes.
- **Correct:**
```svelte
<script>
  let element;
  let width = $state(0);
  
  $effect.pre(() => {
    if (element) {
      width = element.getBoundingClientRect().width;
    }
  });
</script>
```

**Rule:** Use `$effect.tracking()` to conditionally track dependencies.
- **Rationale:** Provides fine-grained control over reactive dependencies.
- **Correct:**
```svelte
<script>
  let condition = $state(true);
  let value = $state(0);
  
  $effect(() => {
    if ($effect.tracking()) {
      // This will track both condition and value
      console.log(condition, value);
    }
  });
</script>
```

**Rule:** Prefer `$derived.by` for complex computations.
- **Rationale:** More readable for complex derived values.
- **Correct:**
```svelte
<script>
  let items = $state([]);
  
  let expensiveComputation = $derived.by(() => {
    // Complex calculation
    return items.reduce((acc, item) => {
      // Complex logic here
      return acc + item.value;
    }, 0);
  });
</script>
```

---

## Section 8: Migration Checklist

When converting from Svelte 4 to Svelte 5:

1. ✅ Replace `let` with `$state()` for reactive variables
2. ✅ Replace `$:` reactive statements with `$derived()`
3. ✅ Replace `onMount` and `onDestroy` with `$effect()`
4. ✅ Replace `createEventDispatcher` with callback props
5. ✅ Replace `on:event` with `onevent` for native elements
6. ✅ Replace `<slot>` with `{@render}` tags
7. ✅ Add TypeScript types for all props and state
8. ✅ Update form handling to use SvelteKit actions
9. ✅ Review accessibility and semantic HTML usage
10. ✅ Test without JavaScript enabled (progressive enhancement)

---

## Section 9: Critical Reminders

- **Always use `$state()` for reactive variables** - plain `let` won't trigger updates
- **Use `onevent` for native elements** - `on:event` is deprecated
- **Server-side validation is mandatory** - client-side is UX enhancement only
- **Progressive enhancement is required** - forms must work without JavaScript
- **Type safety is non-negotiable** - use TypeScript for all props and state
- **Accessibility is built-in** - use semantic HTML and proper ARIA
- **Security by default** - never trust client-side data or use unsanitized HTML